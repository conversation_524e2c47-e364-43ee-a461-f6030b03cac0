.footer {
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  padding: 60px 0 20px;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-section {
    h3 {
      font-size: 1.8rem;
      font-weight: 700;
      margin-bottom: 20px;
      background: linear-gradient(45deg, #64b5f6, #42a5f5);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #64b5f6;
    }

    p {
      line-height: 1.6;
      opacity: 0.8;
      margin-bottom: 20px;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 10px;

        a {
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          transition: color 0.3s ease;

          &:hover {
            color: #64b5f6;
          }
        }
      }
    }

    .contact-info {
      p {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;

        i {
          color: #64b5f6;
          width: 20px;
        }
      }
    }
  }

  .social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: rgba(100, 181, 246, 0.1);
      color: #64b5f6;
      border-radius: 50%;
      text-decoration: none;
      transition: all 0.3s ease;
      border: 1px solid rgba(100, 181, 246, 0.2);

      i {
        font-size: 1.2rem;
      }

      &:hover {
        background: #64b5f6;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(100, 181, 246, 0.3);
      }
    }
  }

  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    p {
      margin: 0;
      opacity: 0.7;
      font-size: 0.9rem;

      i.fa-heart {
        color: #e91e63;
        animation: heartbeat 1.5s ease-in-out infinite;
      }
    }
  }

  @keyframes heartbeat {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @media (max-width: 768px) {
    padding: 40px 0 20px;

    .footer-content {
      grid-template-columns: 1fr;
      gap: 30px;
      text-align: center;
    }

    .footer-bottom {
      flex-direction: column;
      text-align: center;
      gap: 10px;
    }

    .social-links {
      justify-content: center;
    }
  }
}