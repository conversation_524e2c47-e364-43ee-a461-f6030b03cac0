@import '../../../styles/variables';
@import '../../../styles/mixins';

.footer {
  background: linear-gradient(135deg, $primary-dark 0%, $primary-medium 50%, $secondary-dark 100%);
  color: $neutral-white;
  padding: $spacing-3xl 0 $spacing-lg;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-section {
    h3 {
      font-size: 1.8rem;
      font-weight: 700;
      margin-bottom: $spacing-lg;
      @include text-gradient($text-gradient-accent);
    }

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: $spacing-lg;
      color: $secondary-light;
    }

    p {
      line-height: 1.6;
      opacity: 0.8;
      margin-bottom: 20px;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 10px;

        a {
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          transition: color 0.3s ease;

          &:hover {
            color: $secondary-light;
          }
        }
      }
    }

    .contact-info {
      p {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;

        i {
          color: $secondary-light;
          width: 20px;
        }
      }
    }
  }

  .social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;

    a {
      @include social-link;
      background: rgba($secondary-light, 0.1);
      color: $secondary-light;
      border: 1px solid rgba($secondary-light, 0.2);

      i {
        font-size: 1.2rem;
      }

      &:hover {
        background: $secondary-light;
        color: $neutral-white;
        transform: translateY(-3px);
        box-shadow: $shadow-glow;
      }
    }
  }

  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;

    p {
      margin: 0;
      opacity: 0.7;
      font-size: 0.9rem;

      i.fa-heart {
        color: $accent-rose;
        animation: heartbeat 1.5s ease-in-out infinite;
      }
    }
  }

  @keyframes heartbeat {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  @media (max-width: 768px) {
    padding: 40px 0 20px;

    .footer-content {
      grid-template-columns: 1fr;
      gap: 30px;
      text-align: center;
    }

    .footer-bottom {
      flex-direction: column;
      text-align: center;
      gap: 10px;
    }

    .social-links {
      justify-content: center;
    }
  }
}