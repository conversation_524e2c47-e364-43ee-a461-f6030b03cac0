{"version": 3, "file": "rxjs-interop.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/rxjs-interop/src/take_until_destroyed.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/rxjs-interop/src/output_from_observable.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/rxjs-interop/src/output_to_observable.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/rxjs-interop/src/to_observable.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/rxjs-interop/src/to_signal.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/rxjs-interop/src/pending_until_event.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/rxjs-interop/src/rx_resource.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {assertInInjectionContext, DestroyRef, inject} from '../../src/core';\nimport {MonoTypeOperatorFunction, Observable} from 'rxjs';\nimport {takeUntil} from 'rxjs/operators';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi 19.0\n */\nexport function takeUntilDestroyed<T>(destroyRef?: DestroyRef): MonoTypeOperatorFunction<T> {\n  if (!destroyRef) {\n    ngDevMode && assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n\n  const destroyed$ = new Observable<void>((subscriber) => {\n    if ((destroyRef as unknown as {destroyed: boolean}).destroyed) {\n      subscriber.next();\n      return;\n    }\n    const unregisterFn = destroyRef!.onDestroy(subscriber.next.bind(subscriber));\n    return unregisterFn;\n  });\n\n  return <T>(source: Observable<T>) => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  assertInInjectionContext,\n  DestroyRef,\n  inject,\n  OutputOptions,\n  OutputRef,\n  OutputRefSubscription,\n  ɵRuntimeError,\n  ɵRuntimeErrorCode,\n} from '../../src/core';\nimport {Observable} from 'rxjs';\n\nimport {takeUntilDestroyed} from './take_until_destroyed';\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef<T> implements OutputRef<T> {\n  private destroyed = false;\n\n  destroyRef = inject(DestroyRef);\n\n  constructor(private source: Observable<T>) {\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n\n  subscribe(callbackFn: (value: T) => void): OutputRefSubscription {\n    if (this.destroyed) {\n      throw new ɵRuntimeError(\n        ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED,\n        ngDevMode &&\n          'Unexpected subscription to destroyed `OutputRef`. ' +\n            'The owning directive/component is destroyed.',\n      );\n    }\n\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: (value) => callbackFn(value),\n    });\n\n    return {\n      unsubscribe: () => subscription.unsubscribe(),\n    };\n  }\n}\n\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi 19.0\n */\nexport function outputFromObservable<T>(\n  observable: Observable<T>,\n  opts?: OutputOptions,\n): OutputRef<T> {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef<T>(observable);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {OutputRef, ɵgetOutputDestroyRef} from '../../src/core';\nimport {Observable} from 'rxjs';\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi 19.0\n */\nexport function outputToObservable<T>(ref: OutputRef<T>): Observable<T> {\n  const destroyRef = ɵgetOutputDestroyRef(ref);\n\n  return new Observable<T>((observer) => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    const unregisterOnDestroy = destroyRef?.onDestroy(() => observer.complete());\n\n    const subscription = ref.subscribe((v) => observer.next(v));\n    return () => {\n      subscription.unsubscribe();\n      unregisterOnDestroy?.();\n    };\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  assertInInjectionContext,\n  DestroyRef,\n  effect,\n  inject,\n  Injector,\n  Signal,\n  untracked,\n} from '../../src/core';\nimport {Observable, ReplaySubject} from 'rxjs';\n\n/**\n * Options for `toObservable`.\n *\n * @publicApi 20.0\n */\nexport interface ToObservableOptions {\n  /**\n   * The `Injector` to use when creating the underlying `effect` which watches the signal.\n   *\n   * If this isn't specified, the current [injection context](guide/di/dependency-injection-context)\n   * will be used.\n   */\n  injector?: Injector;\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi 20.0\n */\nexport function toObservable<T>(source: Signal<T>, options?: ToObservableOptions): Observable<T> {\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(toObservable);\n  }\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject<T>(1);\n\n  const watcher = effect(\n    () => {\n      let value: T;\n      try {\n        value = source();\n      } catch (err) {\n        untracked(() => subject.error(err));\n        return;\n      }\n      untracked(() => subject.next(value));\n    },\n    {injector, manualCleanup: true},\n  );\n\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n\n  return subject.asObservable();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  assertInInjectionContext,\n  assertNotInReactiveContext,\n  computed,\n  DestroyRef,\n  inject,\n  Injector,\n  signal,\n  Signal,\n  WritableSignal,\n  ɵRuntimeError,\n  ɵRuntimeErrorCode,\n} from '../../src/core';\nimport {ValueEqualityFn} from '../../primitives/signals';\nimport {Observable, Subscribable} from 'rxjs';\n\n/**\n * Options for `toSignal`.\n *\n * @publicApi 20.0\n */\nexport interface ToSignalOptions<T> {\n  /**\n   * Initial value for the signal produced by `toSignal`.\n   *\n   * This will be the value of the signal until the observable emits its first value.\n   */\n  initialValue?: unknown;\n\n  /**\n   * Whether to require that the observable emits synchronously when `toSignal` subscribes.\n   *\n   * If this is `true`, `toSignal` will assert that the observable produces a value immediately upon\n   * subscription. Setting this option removes the need to either deal with `undefined` in the\n   * signal type or provide an `initialValue`, at the cost of a runtime error if this requirement is\n   * not met.\n   */\n  requireSync?: boolean;\n\n  /**\n   * `Injector` which will provide the `DestroyRef` used to clean up the Observable subscription.\n   *\n   * If this is not provided, a `DestroyRef` will be retrieved from the current [injection\n   * context](guide/di/dependency-injection-context), unless manual cleanup is requested.\n   */\n  injector?: Injector;\n\n  /**\n   * Whether the subscription should be automatically cleaned up (via `DestroyRef`) when\n   * `toSignal`'s creation context is destroyed.\n   *\n   * If manual cleanup is enabled, then `DestroyRef` is not used, and the subscription will persist\n   * until the `Observable` itself completes.\n   */\n  manualCleanup?: boolean;\n\n  /**\n   * A comparison function which defines equality for values emitted by the observable.\n   *\n   * Equality comparisons are executed against the initial value if one is provided.\n   */\n  equal?: ValueEqualityFn<T>;\n}\n\n// Base case: no options -> `undefined` in the result type.\nexport function toSignal<T>(source: Observable<T> | Subscribable<T>): Signal<T | undefined>;\n// Options with `undefined` initial value and no `requiredSync` -> `undefined`.\nexport function toSignal<T>(\n  source: Observable<T> | Subscribable<T>,\n  options: NoInfer<ToSignalOptions<T | undefined>> & {\n    initialValue?: undefined;\n    requireSync?: false;\n  },\n): Signal<T | undefined>;\n// Options with `null` initial value -> `null`.\nexport function toSignal<T>(\n  source: Observable<T> | Subscribable<T>,\n  options: NoInfer<ToSignalOptions<T | null>> & {initialValue?: null; requireSync?: false},\n): Signal<T | null>;\n// Options with `undefined` initial value and `requiredSync` -> strict result type.\nexport function toSignal<T>(\n  source: Observable<T> | Subscribable<T>,\n  options: NoInfer<ToSignalOptions<T>> & {initialValue?: undefined; requireSync: true},\n): Signal<T>;\n// Options with a more specific initial value type.\nexport function toSignal<T, const U extends T>(\n  source: Observable<T> | Subscribable<T>,\n  options: NoInfer<ToSignalOptions<T | U>> & {initialValue: U; requireSync?: false},\n): Signal<T | U>;\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nexport function toSignal<T, U = undefined>(\n  source: Observable<T> | Subscribable<T>,\n  options?: ToSignalOptions<T | U> & {initialValue?: U},\n): Signal<T | U> {\n  typeof ngDevMode !== 'undefined' &&\n    ngDevMode &&\n    assertNotInReactiveContext(\n      toSignal,\n      'Invoking `toSignal` causes new subscriptions every time. ' +\n        'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.',\n    );\n\n  const requiresCleanup = !options?.manualCleanup;\n\n  if (ngDevMode && requiresCleanup && !options?.injector) {\n    assertInInjectionContext(toSignal);\n  }\n\n  const cleanupRef = requiresCleanup\n    ? (options?.injector?.get(DestroyRef) ?? inject(DestroyRef))\n    : null;\n\n  const equal = makeToSignalEqual(options?.equal);\n\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state: WritableSignal<State<T | U>>;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({kind: StateKind.NoValue}, {equal});\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal<State<T | U>>(\n      {kind: StateKind.Value, value: options?.initialValue as U},\n      {equal},\n    );\n  }\n\n  let destroyUnregisterFn: (() => void) | undefined;\n\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: (value) => state.set({kind: StateKind.Value, value}),\n    error: (error) => {\n      state.set({kind: StateKind.Error, error});\n      destroyUnregisterFn?.();\n    },\n    complete: () => {\n      destroyUnregisterFn?.();\n    },\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n\n  if (options?.requireSync && state().kind === StateKind.NoValue) {\n    throw new ɵRuntimeError(\n      ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT,\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.',\n    );\n  }\n\n  // Unsubscribe when the current context is destroyed, if requested.\n  destroyUnregisterFn = cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(\n    () => {\n      const current = state();\n      switch (current.kind) {\n        case StateKind.Value:\n          return current.value;\n        case StateKind.Error:\n          throw current.error;\n        case StateKind.NoValue:\n          // This shouldn't really happen because the error is thrown on creation.\n          throw new ɵRuntimeError(\n            ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT,\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n              '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.',\n          );\n      }\n    },\n    {equal: options?.equal},\n  );\n}\n\nfunction makeToSignalEqual<T>(\n  userEquality: ValueEqualityFn<T> = Object.is,\n): ValueEqualityFn<State<T>> {\n  return (a, b) =>\n    a.kind === StateKind.Value && b.kind === StateKind.Value && userEquality(a.value, b.value);\n}\n\nconst enum StateKind {\n  NoValue,\n  Value,\n  Error,\n}\n\ninterface NoValueState {\n  kind: StateKind.NoValue;\n}\n\ninterface ValueState<T> {\n  kind: StateKind.Value;\n  value: T;\n}\n\ninterface ErrorState {\n  kind: StateKind.Error;\n  error: unknown;\n}\n\ntype State<T> = NoValueState | ValueState<T> | ErrorState;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {assertInInjectionContext, PendingTasks, inject, Injector} from '../../src/core';\nimport {MonoTypeOperatorFunction, Observable} from 'rxjs';\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview 20.0\n */\nexport function pendingUntilEvent<T>(injector?: Injector): MonoTypeOperatorFunction<T> {\n  if (injector === undefined) {\n    ngDevMode && assertInInjectionContext(pendingUntilEvent);\n    injector = inject(Injector);\n  }\n  const taskService = injector.get(PendingTasks);\n\n  return (sourceObservable) => {\n    return new Observable<T>((originalSubscriber) => {\n      // create a new task on subscription\n      const removeTask = taskService.add();\n\n      let cleanedUp = false;\n      function cleanupTask() {\n        if (cleanedUp) {\n          return;\n        }\n\n        removeTask();\n        cleanedUp = true;\n      }\n\n      const innerSubscription = sourceObservable.subscribe({\n        next: (v) => {\n          originalSubscriber.next(v);\n          cleanupTask();\n        },\n        complete: () => {\n          originalSubscriber.complete();\n          cleanupTask();\n        },\n        error: (e) => {\n          originalSubscriber.error(e);\n          cleanupTask();\n        },\n      });\n      innerSubscription.add(() => {\n        originalSubscriber.unsubscribe();\n        cleanupTask();\n      });\n      return innerSubscription;\n    });\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  assertInInjectionContext,\n  resource,\n  ResourceLoaderParams,\n  ResourceRef,\n  Signal,\n  signal,\n  BaseResourceOptions,\n  ɵRuntimeError,\n  ɵRuntimeErrorCode,\n  ResourceStreamItem,\n} from '../../src/core';\nimport {Observable, Subscription} from 'rxjs';\nimport {encapsulateResourceError} from '../../src/resource/resource';\n\n/**\n * Like `ResourceOptions` but uses an RxJS-based `loader`.\n *\n * @experimental\n */\nexport interface RxResourceOptions<T, R> extends BaseResourceOptions<T, R> {\n  stream: (params: ResourceLoaderParams<R>) => Observable<T>;\n}\n\n/**\n * Like `resource` but uses an RxJS based `loader` which maps the request to an `Observable` of the\n * resource's value.\n *\n * @experimental\n */\nexport function rxResource<T, R>(\n  opts: RxResourceOptions<T, R> & {defaultValue: NoInfer<T>},\n): ResourceRef<T>;\n\n/**\n * Like `resource` but uses an RxJS based `loader` which maps the request to an `Observable` of the\n * resource's value.\n *\n * @experimental\n */\nexport function rxResource<T, R>(opts: RxResourceOptions<T, R>): ResourceRef<T | undefined>;\nexport function rxResource<T, R>(opts: RxResourceOptions<T, R>): ResourceRef<T | undefined> {\n  if (ngDevMode && !opts?.injector) {\n    assertInInjectionContext(rxResource);\n  }\n  return resource<T, R>({\n    ...opts,\n    loader: undefined,\n    stream: (params) => {\n      let sub: Subscription;\n\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      params.abortSignal.addEventListener('abort', onAbort);\n\n      // Start off stream as undefined.\n      const stream = signal<ResourceStreamItem<T>>({value: undefined as T});\n      let resolve: ((value: Signal<ResourceStreamItem<T>>) => void) | undefined;\n      const promise = new Promise<Signal<ResourceStreamItem<T>>>((r) => (resolve = r));\n\n      function send(value: ResourceStreamItem<T>): void {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      }\n\n      // TODO(alxhub): remove after g3 updated to rename loader -> stream\n      const streamFn = opts.stream ?? (opts as {loader?: RxResourceOptions<T, R>['stream']}).loader;\n      if (streamFn === undefined) {\n        throw new ɵRuntimeError(\n          ɵRuntimeErrorCode.MUST_PROVIDE_STREAM_OPTION,\n          ngDevMode && `Must provide \\`stream\\` option.`,\n        );\n      }\n\n      sub = streamFn(params).subscribe({\n        next: (value) => send({value}),\n        error: (error: unknown) => {\n          send({error: encapsulateResourceError(error)});\n          params.abortSignal.removeEventListener('abort', onAbort);\n        },\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new ɵRuntimeError(\n                ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE,\n                ngDevMode && 'Resource completed before producing a value',\n              ),\n            });\n          }\n          params.abortSignal.removeEventListener('abort', onAbort);\n        },\n      });\n\n      return promise;\n    },\n  });\n}\n"], "names": ["ɵRuntimeError", "ɵgetOutputDestroyRef"], "mappings": ";;;;;;;;;;;;;;;;AAYA;;;;;;;;;AASG;AACG,SAAU,kBAAkB,CAAI,UAAuB,EAAA;IAC3D,IAAI,CAAC,UAAU,EAAE;AACf,QAAA,SAAS,IAAI,wBAAwB,CAAC,kBAAkB,CAAC;AACzD,QAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;;IAGjC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAO,CAAC,UAAU,KAAI;AACrD,QAAA,IAAK,UAA8C,CAAC,SAAS,EAAE;YAC7D,UAAU,CAAC,IAAI,EAAE;YACjB;;AAEF,QAAA,MAAM,YAAY,GAAG,UAAW,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5E,QAAA,OAAO,YAAY;AACrB,KAAC,CAAC;IAEF,OAAO,CAAI,MAAqB,KAAI;QAClC,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AAC3C,KAAC;AACH;;AClBA;;;;;AAKG;AACH,MAAM,uBAAuB,CAAA;AAKP,IAAA,MAAA;IAJZ,SAAS,GAAG,KAAK;AAEzB,IAAA,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAE/B,IAAA,WAAA,CAAoB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM;AACxB,QAAA,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAK;AAC7B,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACvB,SAAC,CAAC;;AAGJ,IAAA,SAAS,CAAC,UAA8B,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAIA,YAAa,CAAA,GAAA,+CAErB,SAAS;gBACP,oDAAoD;AAClD,oBAAA,8CAA8C,CACnD;;;AAIH,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;YACnF,IAAI,EAAE,CAAC,KAAK,KAAK,UAAU,CAAC,KAAK,CAAC;AACnC,SAAA,CAAC;QAEF,OAAO;AACL,YAAA,WAAW,EAAE,MAAM,YAAY,CAAC,WAAW,EAAE;SAC9C;;AAEJ;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACa,SAAA,oBAAoB,CAClC,UAAyB,EACzB,IAAoB,EAAA;AAEpB,IAAA,SAAS,IAAI,wBAAwB,CAAC,oBAAoB,CAAC;AAC3D,IAAA,OAAO,IAAI,uBAAuB,CAAI,UAAU,CAAC;AACnD;;AC/EA;;;;;;;AAOG;AACG,SAAU,kBAAkB,CAAI,GAAiB,EAAA;AACrD,IAAA,MAAM,UAAU,GAAGC,mBAAoB,CAAC,GAAG,CAAC;AAE5C,IAAA,OAAO,IAAI,UAAU,CAAI,CAAC,QAAQ,KAAI;;;;AAIpC,QAAA,MAAM,mBAAmB,GAAG,UAAU,EAAE,SAAS,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAE5E,QAAA,MAAM,YAAY,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3D,QAAA,OAAO,MAAK;YACV,YAAY,CAAC,WAAW,EAAE;YAC1B,mBAAmB,IAAI;AACzB,SAAC;AACH,KAAC,CAAC;AACJ;;ACAA;;;;;;;;AAQG;AACa,SAAA,YAAY,CAAI,MAAiB,EAAE,OAA6B,EAAA;AAC9E,IAAA,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnC,wBAAwB,CAAC,YAAY,CAAC;;IAExC,MAAM,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC;AACtD,IAAA,MAAM,OAAO,GAAG,IAAI,aAAa,CAAI,CAAC,CAAC;AAEvC,IAAA,MAAM,OAAO,GAAG,MAAM,CACpB,MAAK;AACH,QAAA,IAAI,KAAQ;AACZ,QAAA,IAAI;YACF,KAAK,GAAG,MAAM,EAAE;;QAChB,OAAO,GAAG,EAAE;YACZ,SAAS,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnC;;QAEF,SAAS,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACrC,EACD,EAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,EAAC,CAChC;IAED,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MAAK;QACtC,OAAO,CAAC,OAAO,EAAE;QACjB,OAAO,CAAC,QAAQ,EAAE;AACpB,KAAC,CAAC;AAEF,IAAA,OAAO,OAAO,CAAC,YAAY,EAAE;AAC/B;;AC4BA;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAA,QAAQ,CACtB,MAAuC,EACvC,OAAqD,EAAA;IAErD,OAAO,SAAS,KAAK,WAAW;QAC9B,SAAS;QACT,0BAA0B,CACxB,QAAQ,EACR,2DAA2D;AACzD,YAAA,oGAAoG,CACvG;AAEH,IAAA,MAAM,eAAe,GAAG,CAAC,OAAO,EAAE,aAAa;IAE/C,IAAI,SAAS,IAAI,eAAe,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE;QACtD,wBAAwB,CAAC,QAAQ,CAAC;;IAGpC,MAAM,UAAU,GAAG;AACjB,WAAG,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC;UACzD,IAAI;IAER,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,EAAE,KAAK,CAAC;;;AAI/C,IAAA,IAAI,KAAmC;AACvC,IAAA,IAAI,OAAO,EAAE,WAAW,EAAE;;AAExB,QAAA,KAAK,GAAG,MAAM,CAAC,EAAC,IAAI,EAAA,CAAA,0BAAoB,EAAE,EAAC,KAAK,EAAC,CAAC;;SAC7C;;AAEL,QAAA,KAAK,GAAG,MAAM,CACZ,EAAC,IAAI,EAAA,CAAA,wBAAmB,KAAK,EAAE,OAAO,EAAE,YAAiB,EAAC,EAC1D,EAAC,KAAK,EAAC,CACR;;AAGH,IAAA,IAAI,mBAA6C;;;;;;;AAQjD,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;AAC3B,QAAA,IAAI,EAAE,CAAC,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,EAAC,IAAI,EAAA,CAAA,wBAAmB,KAAK,EAAC,CAAC;AAC1D,QAAA,KAAK,EAAE,CAAC,KAAK,KAAI;YACf,KAAK,CAAC,GAAG,CAAC,EAAC,IAAI,2BAAmB,KAAK,EAAC,CAAC;YACzC,mBAAmB,IAAI;SACxB;QACD,QAAQ,EAAE,MAAK;YACb,mBAAmB,IAAI;SACxB;;;AAGF,KAAA,CAAC;IAEF,IAAI,OAAO,EAAE,WAAW,IAAI,KAAK,EAAE,CAAC,IAAI,KAAsB,CAAA,0BAAE;QAC9D,MAAM,IAAID,YAAa,CAAA,GAAA,yDAErB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,YAAA,qFAAqF,CACxF;;;AAIH,IAAA,mBAAmB,GAAG,UAAU,EAAE,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;IAItE,OAAO,QAAQ,CACb,MAAK;AACH,QAAA,MAAM,OAAO,GAAG,KAAK,EAAE;AACvB,QAAA,QAAQ,OAAO,CAAC,IAAI;AAClB,YAAA,KAAA,CAAA;gBACE,OAAO,OAAO,CAAC,KAAK;AACtB,YAAA,KAAA,CAAA;gBACE,MAAM,OAAO,CAAC,KAAK;AACrB,YAAA,KAAA,CAAA;;gBAEE,MAAM,IAAIA,YAAa,CAAA,GAAA,yDAErB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,oBAAA,qFAAqF,CACxF;;KAEN,EACD,EAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAC,CACxB;AACH;AAEA,SAAS,iBAAiB,CACxB,YAAmC,GAAA,MAAM,CAAC,EAAE,EAAA;IAE5C,OAAO,CAAC,CAAC,EAAE,CAAC,KACV,CAAC,CAAC,IAAI,KAAA,CAAA,0BAAwB,CAAC,CAAC,IAAI,KAAoB,CAAA,0BAAI,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC;AAC9F;;AC/MA;;;;;;;;AAQG;AACG,SAAU,iBAAiB,CAAI,QAAmB,EAAA;AACtD,IAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,QAAA,SAAS,IAAI,wBAAwB,CAAC,iBAAiB,CAAC;AACxD,QAAA,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;;IAE7B,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC;IAE9C,OAAO,CAAC,gBAAgB,KAAI;AAC1B,QAAA,OAAO,IAAI,UAAU,CAAI,CAAC,kBAAkB,KAAI;;AAE9C,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;YAEpC,IAAI,SAAS,GAAG,KAAK;AACrB,YAAA,SAAS,WAAW,GAAA;gBAClB,IAAI,SAAS,EAAE;oBACb;;AAGF,gBAAA,UAAU,EAAE;gBACZ,SAAS,GAAG,IAAI;;AAGlB,YAAA,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,SAAS,CAAC;AACnD,gBAAA,IAAI,EAAE,CAAC,CAAC,KAAI;AACV,oBAAA,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1B,oBAAA,WAAW,EAAE;iBACd;gBACD,QAAQ,EAAE,MAAK;oBACb,kBAAkB,CAAC,QAAQ,EAAE;AAC7B,oBAAA,WAAW,EAAE;iBACd;AACD,gBAAA,KAAK,EAAE,CAAC,CAAC,KAAI;AACX,oBAAA,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3B,oBAAA,WAAW,EAAE;iBACd;AACF,aAAA,CAAC;AACF,YAAA,iBAAiB,CAAC,GAAG,CAAC,MAAK;gBACzB,kBAAkB,CAAC,WAAW,EAAE;AAChC,gBAAA,WAAW,EAAE;AACf,aAAC,CAAC;AACF,YAAA,OAAO,iBAAiB;AAC1B,SAAC,CAAC;AACJ,KAAC;AACH;;ACdM,SAAU,UAAU,CAAO,IAA6B,EAAA;AAC5D,IAAA,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE;QAChC,wBAAwB,CAAC,UAAU,CAAC;;AAEtC,IAAA,OAAO,QAAQ,CAAO;AACpB,QAAA,GAAG,IAAI;AACP,QAAA,MAAM,EAAE,SAAS;AACjB,QAAA,MAAM,EAAE,CAAC,MAAM,KAAI;AACjB,YAAA,IAAI,GAAiB;;;YAIrB,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,WAAW,EAAE;YACvC,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC;;YAGrD,MAAM,MAAM,GAAG,MAAM,CAAwB,EAAC,KAAK,EAAE,SAAc,EAAC,CAAC;AACrE,YAAA,IAAI,OAAqE;AACzE,YAAA,MAAM,OAAO,GAAG,IAAI,OAAO,CAAgC,CAAC,CAAC,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;YAEhF,SAAS,IAAI,CAAC,KAA4B,EAAA;AACxC,gBAAA,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;AACjB,gBAAA,OAAO,GAAG,MAAM,CAAC;gBACjB,OAAO,GAAG,SAAS;;;YAIrB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,IAAK,IAAqD,CAAC,MAAM;AAC7F,YAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,gBAAA,MAAM,IAAIA,YAAa,CAAA,GAAA,qDAErB,SAAS,IAAI,CAAA,+BAAA,CAAiC,CAC/C;;AAGH,YAAA,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC;gBAC/B,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC;AAC9B,gBAAA,KAAK,EAAE,CAAC,KAAc,KAAI;oBACxB,IAAI,CAAC,EAAC,KAAK,EAAE,wBAAwB,CAAC,KAAK,CAAC,EAAC,CAAC;oBAC9C,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;iBACzD;gBACD,QAAQ,EAAE,MAAK;oBACb,IAAI,OAAO,EAAE;AACX,wBAAA,IAAI,CAAC;AACH,4BAAA,KAAK,EAAE,IAAIA,YAAa,wEAEtB,SAAS,IAAI,6CAA6C,CAC3D;AACF,yBAAA,CAAC;;oBAEJ,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC;iBACzD;AACF,aAAA,CAAC;AAEF,YAAA,OAAO,OAAO;SACf;AACF,KAAA,CAAC;AACJ;;;;"}