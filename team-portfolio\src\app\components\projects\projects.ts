import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface Project {
  id: number;
  title: string;
  description: string;
  longDescription: string;
  technologies: string[];
  category: string;
  image: string;
  demoUrl?: string;
  githubUrl?: string;
  featured: boolean;
  teamMember: 'Ibrahim' | 'Ichrak' | 'Both';
  status: 'Completed' | 'In Progress' | 'Planned';
}

@Component({
  selector: 'app-projects',
  imports: [CommonModule],
  templateUrl: './projects.html',
  styleUrl: './projects.scss'
})
export class Projects {
  projects: Project[] = [
    {
      id: 1,
      title: 'AI-Powered E-Commerce Platform',
      description: 'Modern e-commerce platform with AI-driven product recommendations and real-time analytics.',
      longDescription: 'A comprehensive e-commerce solution featuring machine learning algorithms for personalized product recommendations, real-time inventory management, and advanced analytics dashboard. Built with microservices architecture for scalability.',
      technologies: ['Angular', 'Node.js', 'Python', 'TensorFlow', 'MongoDB', 'Docker', 'AWS'],
      category: 'Full Stack',
      image: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=600&h=400&fit=crop&crop=center',
      demoUrl: 'https://demo-ecommerce.example.com',
      githubUrl: 'https://github.com/team/ai-ecommerce',
      featured: true,
      teamMember: 'Both',
      status: 'Completed'
    },
    {
      id: 2,
      title: 'Smart Healthcare Mobile App',
      description: 'Cross-platform mobile application for patient management and telemedicine consultations.',
      longDescription: 'Revolutionary healthcare app connecting patients with doctors through secure video consultations, appointment scheduling, prescription management, and health monitoring with IoT device integration.',
      technologies: ['React Native', 'Firebase', 'Node.js', 'Socket.io', 'MongoDB', 'Stripe'],
      category: 'Mobile Development',
      image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=600&h=400&fit=crop&crop=center',
      demoUrl: 'https://healthcare-demo.example.com',
      githubUrl: 'https://github.com/team/healthcare-app',
      featured: true,
      teamMember: 'Ichrak',
      status: 'Completed'
    },
    {
      id: 3,
      title: 'DevOps Automation Pipeline',
      description: 'Complete CI/CD pipeline with automated testing, deployment, and monitoring solutions.',
      longDescription: 'Enterprise-grade DevOps solution featuring automated CI/CD pipelines, infrastructure as code, container orchestration, and comprehensive monitoring with alerting systems for high-availability applications.',
      technologies: ['Jenkins', 'Docker', 'Kubernetes', 'Terraform', 'AWS', 'Prometheus', 'Grafana'],
      category: 'DevOps',
      image: 'https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?w=600&h=400&fit=crop&crop=center',
      githubUrl: 'https://github.com/team/devops-automation',
      featured: true,
      teamMember: 'Ibrahim',
      status: 'Completed'
    },
    {
      id: 4,
      title: 'Real-Time Chat Application',
      description: 'Scalable chat platform with video calls, file sharing, and team collaboration features.',
      longDescription: 'Modern communication platform supporting real-time messaging, video conferencing, file sharing, screen sharing, and team workspaces with end-to-end encryption for secure communications.',
      technologies: ['Vue.js', 'Socket.io', 'WebRTC', 'Node.js', 'Redis', 'PostgreSQL'],
      category: 'Web Application',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=400&fit=crop&crop=center',
      demoUrl: 'https://chat-demo.example.com',
      githubUrl: 'https://github.com/team/realtime-chat',
      featured: false,
      teamMember: 'Both',
      status: 'Completed'
    },
    {
      id: 5,
      title: 'Blockchain Voting System',
      description: 'Secure and transparent voting platform built on blockchain technology.',
      longDescription: 'Decentralized voting system ensuring transparency, security, and immutability using smart contracts. Features voter authentication, real-time results, and audit trails for democratic processes.',
      technologies: ['Solidity', 'Web3.js', 'React', 'Ethereum', 'IPFS', 'MetaMask'],
      category: 'Blockchain',
      image: 'https://images.unsplash.com/photo-1639762681485-074b7f938ba0?w=600&h=400&fit=crop&crop=center',
      githubUrl: 'https://github.com/team/blockchain-voting',
      featured: false,
      teamMember: 'Ibrahim',
      status: 'In Progress'
    },
    {
      id: 6,
      title: 'IoT Smart Home Dashboard',
      description: 'Comprehensive dashboard for managing smart home devices and automation.',
      longDescription: 'Intelligent home automation system with real-time device monitoring, energy consumption tracking, security alerts, and voice control integration for seamless smart home management.',
      technologies: ['Flutter', 'Firebase', 'Arduino', 'Raspberry Pi', 'MQTT', 'TensorFlow Lite'],
      category: 'IoT',
      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=400&fit=crop&crop=center',
      demoUrl: 'https://smarthome-demo.example.com',
      featured: false,
      teamMember: 'Ichrak',
      status: 'In Progress'
    }
  ];

  filteredProjects: Project[] = this.projects;
  selectedCategory: string = 'All';
  categories: string[] = ['All', 'Full Stack', 'Mobile Development', 'DevOps', 'Web Application', 'Blockchain', 'IoT'];

  filterProjects(category: string) {
    this.selectedCategory = category;
    if (category === 'All') {
      this.filteredProjects = this.projects;
    } else {
      this.filteredProjects = this.projects.filter(project => project.category === category);
    }
  }

  getFeaturedProjects(): Project[] {
    return this.projects.filter(project => project.featured);
  }
}
