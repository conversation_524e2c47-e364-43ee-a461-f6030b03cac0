import { Injectable } from '@angular/core';

export interface TeamMember {
  id: number;
  name: string;
  title: string;
  description: string;
  skills: string[];
  image: string;
  email: string;
  linkedin: string;
  github: string;
  huggingface?: string;
  specializations: string[];
  experience: string;
}

@Injectable({
  providedIn: 'root'
})
export class TeamService {

  private teamMembers: TeamMember[] = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>uad<PERSON>',
      title: 'Full Stack Web & Mobile Developer',
      description: 'Creative developer with expertise in building responsive web applications and cross-platform mobile solutions. Focused on user experience and modern development frameworks.',
      skills: [
        'JavaScript', 'TypeScript', 'React', 'React Native', 'Angular', 'Vue.js',
        'Node.js', 'Express.js', 'Flutter', 'Dart', 'Swift', 'Kotlin',
        'HTML5', 'CSS3', 'SASS', 'Tailwind CSS', 'Bootstrap',
        'MongoDB', 'Firebase', 'PostgreSQL', 'GraphQL', 'REST APIs'
      ],
      image: '/assets/images/ichrak.jpg',
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/ichrak-jaouadi',
      github: 'https://github.com/ichrak-jaouadi',
      specializations: [
        'Mobile App Development',
        'Frontend Development',
        'UI/UX Implementation',
        'Cross-Platform Solutions'
      ],
      experience: '4+ years in web and mobile development'
    },
    {
      id: 2,
      name: 'Ibrahim Aloui',
      title: 'Full Stack AI Engineer & DevOps Professional',
      description: 'Passionate about building intelligent systems and scalable infrastructure. Experienced in machine learning, cloud technologies, and modern development practices.',
      skills: [
        'Python', 'TypeScript', 'JavaScript', 'Node.js', 'React', 'Angular',
        'TensorFlow', 'PyTorch', 'LLM', 'Chatbot', 'Docker', 'Kubernetes', 'AWS', 'Azure',
        'CI/CD', 'Jenkins', 'GitLab CI', 'Terraform', 'Ansible',
        'MongoDB', 'PostgreSQL', 'Redis', 'Elasticsearch'
      ],
      image: '/assets/images/ibrahim.jpg',
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/ibrahim-aloui',
      github: 'https://github.com/ibrahim-aloui',
      huggingface: 'https://huggingface.co/ibrahim-aloui',
      specializations: [
        'Machine Learning & AI',
        'Cloud Architecture',
        'DevOps & Infrastructure',
        'Full Stack Development'
      ],
      experience: '5+ years in AI/ML and DevOps'
    }
  ];

  constructor() { }

  getTeamMembers(): TeamMember[] {
    return this.teamMembers;
  }

  getTeamMember(id: number): TeamMember | undefined {
    return this.teamMembers.find(member => member.id === id);
  }
}
