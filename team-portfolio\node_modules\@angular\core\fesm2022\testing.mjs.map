{"version": 3, "file": "testing.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/async.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/application_error_handler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/defer.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/test_bed_common.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/component_fixture.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/fake_async.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/metadata_overrider.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/resolvers.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/test_bed_compiler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/test_bed.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/primitives/dom-navigation/testing/fake_navigation.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/test_hooks.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/testing/src/logger.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n/**\n * Wraps a test function in an asynchronous test zone. The test will automatically\n * complete when all asynchronous calls within this zone are done. Can be used\n * to wrap an {@link inject} call.\n *\n * Example:\n *\n * ```ts\n * it('...', waitForAsync(inject([AClass], (object) => {\n *   object.doSomething.then(() => {\n *     expect(...);\n *   })\n * })));\n * ```\n *\n * @publicApi\n */\nexport function waitForAsync(fn: Function): (done: any) => any {\n  const _Zone: any = typeof Zone !== 'undefined' ? Zone : null;\n  if (!_Zone) {\n    return function () {\n      return Promise.reject(\n        'Zone is needed for the waitForAsync() test helper but could not be found. ' +\n          'Please make sure that your environment includes zone.js',\n      );\n    };\n  }\n  const asyncTest = _Zone && _Zone[_Zone.__symbol__('asyncTest')];\n  if (typeof asyncTest === 'function') {\n    return asyncTest(fn);\n  }\n  return function () {\n    return Promise.reject(\n      'zone-testing.js is needed for the async() test helper but could not be found. ' +\n        'Please make sure that your environment includes zone.js/testing',\n    );\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ErrorHandler, inject, NgZone, Injectable, EnvironmentInjector} from '../../src/core';\n\nexport const RETHROW_APPLICATION_ERRORS_DEFAULT = true;\n\n@Injectable()\nexport class TestBedApplicationErrorHandler {\n  private readonly zone = inject(NgZone);\n  private readonly injector = inject(EnvironmentInjector);\n  private userErrorHandler?: ErrorHandler;\n  readonly whenStableRejectFunctions: Set<(e: unknown) => void> = new Set();\n\n  handleError(e: unknown) {\n    try {\n      this.zone.runOutsideAngular(() => {\n        this.userErrorHandler ??= this.injector.get(ErrorHandler);\n        this.userErrorHandler.handleError(e);\n      });\n    } catch (userError: unknown) {\n      e = userError;\n    }\n\n    // Instead of throwing the error when there are outstanding `fixture.whenStable` promises,\n    // reject those promises with the error. This allows developers to write\n    // expectAsync(fix.whenStable()).toBeRejected();\n    if (this.whenStableRejectFunctions.size > 0) {\n      for (const fn of this.whenStableRejectFunctions.values()) {\n        fn(e);\n      }\n      this.whenStableRejectFunctions.clear();\n    } else {\n      throw e;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ɵCONTAINER_HEADER_OFFSET as CONTAINER_HEADER_OFFSET,\n  ɵDeferBlockDetails as DeferBlockDetails,\n  ɵDeferBlockState as DeferBlockState,\n  ɵgetDeferBlocks as getDeferBlocks,\n  ɵrenderDeferBlockState as renderDeferBlockState,\n  ɵtriggerResourceLoading as triggerResourceLoading,\n} from '../../src/core';\n\nimport type {ComponentFixture} from './component_fixture';\n\n/**\n * Represents an individual defer block for testing purposes.\n *\n * @publicApi\n */\nexport class DeferBlockFixture {\n  /** @docs-private */\n  constructor(\n    private block: DeferBlockDetails,\n    private componentFixture: ComponentFixture<unknown>,\n  ) {}\n\n  /**\n   * Renders the specified state of the defer fixture.\n   * @param state the defer state to render\n   */\n  async render(state: DeferBlockState): Promise<void> {\n    if (!hasStateTemplate(state, this.block)) {\n      const stateAsString = getDeferBlockStateNameFromEnum(state);\n      throw new Error(\n        `Tried to render this defer block in the \\`${stateAsString}\\` state, ` +\n          `but there was no @${stateAsString.toLowerCase()} block defined in a template.`,\n      );\n    }\n    if (state === DeferBlockState.Complete) {\n      await triggerResourceLoading(this.block.tDetails, this.block.lView, this.block.tNode);\n    }\n    // If the `render` method is used explicitly - skip timer-based scheduling for\n    // `@placeholder` and `@loading` blocks and render them immediately.\n    const skipTimerScheduling = true;\n    renderDeferBlockState(state, this.block.tNode, this.block.lContainer, skipTimerScheduling);\n    this.componentFixture.detectChanges();\n  }\n\n  /**\n   * Retrieves all nested child defer block fixtures\n   * in a given defer block.\n   */\n  getDeferBlocks(): Promise<DeferBlockFixture[]> {\n    const deferBlocks: DeferBlockDetails[] = [];\n    // An LContainer that represents a defer block has at most 1 view, which is\n    // located right after an LContainer header. Get a hold of that view and inspect\n    // it for nested defer blocks.\n    const deferBlockFixtures = [];\n    if (this.block.lContainer.length >= CONTAINER_HEADER_OFFSET) {\n      const lView = this.block.lContainer[CONTAINER_HEADER_OFFSET];\n      getDeferBlocks(lView, deferBlocks);\n      for (const block of deferBlocks) {\n        deferBlockFixtures.push(new DeferBlockFixture(block, this.componentFixture));\n      }\n    }\n    return Promise.resolve(deferBlockFixtures);\n  }\n}\n\nfunction hasStateTemplate(state: DeferBlockState, block: DeferBlockDetails) {\n  switch (state) {\n    case DeferBlockState.Placeholder:\n      return block.tDetails.placeholderTmplIndex !== null;\n    case DeferBlockState.Loading:\n      return block.tDetails.loadingTmplIndex !== null;\n    case DeferBlockState.Error:\n      return block.tDetails.errorTmplIndex !== null;\n    case DeferBlockState.Complete:\n      return true;\n    default:\n      return false;\n  }\n}\n\nfunction getDeferBlockStateNameFromEnum(state: DeferBlockState) {\n  switch (state) {\n    case DeferBlockState.Placeholder:\n      return 'Placeholder';\n    case DeferBlockState.Loading:\n      return 'Loading';\n    case DeferBlockState.Error:\n      return 'Error';\n    default:\n      return 'Main';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  InjectionToken,\n  SchemaMetadata,\n  ɵDeferBlockBehavior as DeferBlockBehavior,\n} from '../../src/core';\n\n/** Whether test modules should be torn down by default. */\nexport const TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT = true;\n\n/** Whether unknown elements in templates should throw by default. */\nexport const THROW_ON_UNKNOWN_ELEMENTS_DEFAULT = false;\n\n/** Whether unknown properties in templates should throw by default. */\nexport const THROW_ON_UNKNOWN_PROPERTIES_DEFAULT = false;\n\n/** Whether defer blocks should use manual triggering or play through normally. */\nexport const DEFER_BLOCK_DEFAULT_BEHAVIOR = DeferBlockBehavior.Playthrough;\n\n/**\n * An abstract class for inserting the root test component element in a platform independent way.\n *\n * @publicApi\n */\nexport class TestComponentRenderer {\n  insertRootElement(rootElementId: string) {}\n  removeAllRootElements?() {}\n}\n\n/**\n * @publicApi\n */\nexport const ComponentFixtureAutoDetect = new InjectionToken<boolean>('ComponentFixtureAutoDetect');\n\n/**\n * @publicApi\n */\nexport const ComponentFixtureNoNgZone = new InjectionToken<boolean>('ComponentFixtureNoNgZone');\n\n/**\n * @publicApi\n */\nexport interface TestModuleMetadata {\n  providers?: any[];\n  declarations?: any[];\n  imports?: any[];\n  schemas?: Array<SchemaMetadata | any[]>;\n  teardown?: ModuleTeardownOptions;\n  /**\n   * Whether NG0304 runtime errors should be thrown when unknown elements are present in component's\n   * template. Defaults to `false`, where the error is simply logged. If set to `true`, the error is\n   * thrown.\n   * @see [NG8001](/errors/NG8001) for the description of the problem and how to fix it\n   */\n  errorOnUnknownElements?: boolean;\n  /**\n   * Whether errors should be thrown when unknown properties are present in component's template.\n   * Defaults to `false`, where the error is simply logged.\n   * If set to `true`, the error is thrown.\n   * @see [NG8002](/errors/NG8002) for the description of the error and how to fix it\n   */\n  errorOnUnknownProperties?: boolean;\n\n  /**\n   * Whether errors that happen during application change detection should be rethrown.\n   *\n   * When `true`, errors that are caught during application change detection will\n   * be reported to the `ErrorHandler` and rethrown to prevent them from going\n   * unnoticed in tests.\n   *\n   * When `false`, errors are only forwarded to the `ErrorHandler`, which by default\n   * simply logs them to the console.\n   *\n   * Defaults to `true`.\n   */\n  rethrowApplicationErrors?: boolean;\n\n  /**\n   * Whether defer blocks should behave with manual triggering or play through normally.\n   * Defaults to `manual`.\n   */\n  deferBlockBehavior?: DeferBlockBehavior;\n}\n\n/**\n * @publicApi\n */\nexport interface TestEnvironmentOptions {\n  /**\n   * Configures the test module teardown behavior in `TestBed`.\n   */\n  teardown?: ModuleTeardownOptions;\n  /**\n   * Whether errors should be thrown when unknown elements are present in component's template.\n   * Defaults to `false`, where the error is simply logged.\n   * If set to `true`, the error is thrown.\n   * @see [NG8001](/errors/NG8001) for the description of the error and how to fix it\n   */\n  errorOnUnknownElements?: boolean;\n  /**\n   * Whether errors should be thrown when unknown properties are present in component's template.\n   * Defaults to `false`, where the error is simply logged.\n   * If set to `true`, the error is thrown.\n   * @see [NG8002](/errors/NG8002) for the description of the error and how to fix it\n   */\n  errorOnUnknownProperties?: boolean;\n}\n\n/**\n * Configures the test module teardown behavior in `TestBed`.\n * @publicApi\n */\nexport interface ModuleTeardownOptions {\n  /** Whether the test module should be destroyed after every test. Defaults to `true`. */\n  destroyAfterEach: boolean;\n\n  /** Whether errors during test module destruction should be re-thrown. Defaults to `true`. */\n  rethrowErrors?: boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Subscription} from 'rxjs';\nimport {\n  ApplicationRef,\n  ChangeDetectorRef,\n  ComponentRef,\n  DebugElement,\n  ɵDeferBlockDetails as DeferBlockDetails,\n  ɵEffectScheduler as EffectScheduler,\n  ElementRef,\n  getDebugNode,\n  ɵgetDeferBlocks as getD<PERSON>er<PERSON><PERSON><PERSON>,\n  inject,\n  NgZone,\n  ɵNoopNgZone as NoopNgZone,\n  RendererFactory2,\n  ViewRef,\n  ɵZONELESS_ENABLED as ZONELESS_ENABLED,\n  ɵChangeDetectionScheduler,\n  ɵNotificationSource,\n} from '../../src/core';\nimport {PendingTasksInternal} from '../../src/pending_tasks';\n\nimport {TestBedApplicationErrorHandler} from './application_error_handler';\nimport {DeferBlockFixture} from './defer';\nimport {ComponentFixtureAutoDetect, ComponentFixtureNoNgZone} from './test_bed_common';\n\ninterface TestAppRef {\n  allTestViews: Set<ViewRef>;\n  includeAllTestViews: boolean;\n  autoDetectTestViews: Set<ViewRef>;\n}\n\n/**\n * Fixture for debugging and testing a component.\n *\n * @publicApi\n */\nexport class ComponentFixture<T> {\n  /**\n   * The DebugElement associated with the root element of this component.\n   */\n  debugElement: DebugElement;\n\n  /**\n   * The instance of the root component class.\n   */\n  componentInstance: T;\n\n  /**\n   * The native element at the root of the component.\n   */\n  nativeElement: any;\n\n  /**\n   * The ElementRef for the element at the root of the component.\n   */\n  elementRef: ElementRef;\n\n  /**\n   * The ChangeDetectorRef for the component\n   */\n  changeDetectorRef: ChangeDetectorRef;\n\n  private _renderer: RendererFactory2 | null | undefined;\n  private _isDestroyed: boolean = false;\n  /** @internal */\n  protected readonly _noZoneOptionIsSet = inject(ComponentFixtureNoNgZone, {optional: true});\n  /** @internal */\n  protected _ngZone: NgZone = this._noZoneOptionIsSet ? new NoopNgZone() : inject(NgZone);\n  // Inject ApplicationRef to ensure NgZone stableness causes after render hooks to run\n  // This will likely happen as a result of fixture.detectChanges because it calls ngZone.run\n  // This is a crazy way of doing things but hey, it's the world we live in.\n  // The zoneless scheduler should instead do this more imperatively by attaching\n  // the `ComponentRef` to `ApplicationRef` and calling `appRef.tick` as the `detectChanges`\n  // behavior.\n  /** @internal */\n  protected readonly _appRef = inject(ApplicationRef);\n  private readonly _testAppRef = this._appRef as unknown as TestAppRef;\n  private readonly pendingTasks = inject(PendingTasksInternal);\n  private readonly appErrorHandler = inject(TestBedApplicationErrorHandler);\n  private readonly zonelessEnabled = inject(ZONELESS_ENABLED);\n  private readonly scheduler = inject(ɵChangeDetectionScheduler);\n  private readonly rootEffectScheduler = inject(EffectScheduler);\n  private readonly autoDetectDefault = this.zonelessEnabled ? true : false;\n  private autoDetect =\n    inject(ComponentFixtureAutoDetect, {optional: true}) ?? this.autoDetectDefault;\n\n  private subscriptions = new Subscription();\n\n  // TODO(atscott): Remove this from public API\n  ngZone = this._noZoneOptionIsSet ? null : this._ngZone;\n\n  /** @docs-private */\n  constructor(public componentRef: ComponentRef<T>) {\n    this.changeDetectorRef = componentRef.changeDetectorRef;\n    this.elementRef = componentRef.location;\n    this.debugElement = <DebugElement>getDebugNode(this.elementRef.nativeElement);\n    this.componentInstance = componentRef.instance;\n    this.nativeElement = this.elementRef.nativeElement;\n    this.componentRef = componentRef;\n\n    this._testAppRef.allTestViews.add(this.componentRef.hostView);\n    if (this.autoDetect) {\n      this._testAppRef.autoDetectTestViews.add(this.componentRef.hostView);\n      this.scheduler?.notify(ɵNotificationSource.ViewAttached);\n      this.scheduler?.notify(ɵNotificationSource.MarkAncestorsForTraversal);\n    }\n    this.componentRef.hostView.onDestroy(() => {\n      this._testAppRef.allTestViews.delete(this.componentRef.hostView);\n      this._testAppRef.autoDetectTestViews.delete(this.componentRef.hostView);\n    });\n    // Create subscriptions outside the NgZone so that the callbacks run outside\n    // of NgZone.\n    this._ngZone.runOutsideAngular(() => {\n      this.subscriptions.add(\n        this._ngZone.onError.subscribe({\n          next: (error: any) => {\n            // The rethrow here is to ensure that errors don't go unreported. Since `NgZone.onHandleError` returns `false`,\n            // ZoneJS will not throw the error coming out of a task. Instead, the handling is defined by\n            // the chain of parent delegates and whether they indicate the error is handled in some way (by returning `false`).\n            // Unfortunately, 'onError' does not forward the information about whether the error was handled by a parent zone\n            // so cannot know here whether throwing is appropriate. As a half-solution, we can check to see if we're inside\n            // a fakeAsync context, which we know has its own error handling.\n            // https://github.com/angular/angular/blob/db2f2d99c82aae52d8a0ae46616c6411d070b35e/packages/zone.js/lib/zone-spec/fake-async-test.ts#L783-L784\n            // https://github.com/angular/angular/blob/db2f2d99c82aae52d8a0ae46616c6411d070b35e/packages/zone.js/lib/zone-spec/fake-async-test.ts#L473-L478\n            if (typeof Zone === 'undefined' || Zone.current.get('FakeAsyncTestZoneSpec')) {\n              return;\n            }\n            throw error;\n          },\n        }),\n      );\n    });\n  }\n\n  /**\n   * Trigger a change detection cycle for the component.\n   */\n  detectChanges(checkNoChanges = true): void {\n    const originalCheckNoChanges = this.componentRef.changeDetectorRef.checkNoChanges;\n    try {\n      if (!checkNoChanges) {\n        this.componentRef.changeDetectorRef.checkNoChanges = () => {};\n      }\n\n      if (this.zonelessEnabled) {\n        try {\n          this._testAppRef.includeAllTestViews = true;\n          this._appRef.tick();\n        } finally {\n          this._testAppRef.includeAllTestViews = false;\n        }\n      } else {\n        // Run the change detection inside the NgZone so that any async tasks as part of the change\n        // detection are captured by the zone and can be waited for in isStable.\n        this._ngZone.run(() => {\n          // Flush root effects before `detectChanges()`, to emulate the sequencing of `tick()`.\n          this.rootEffectScheduler.flush();\n          this.changeDetectorRef.detectChanges();\n          this.checkNoChanges();\n        });\n      }\n    } finally {\n      this.componentRef.changeDetectorRef.checkNoChanges = originalCheckNoChanges;\n    }\n  }\n\n  /**\n   * Do a change detection run to make sure there were no changes.\n   */\n  checkNoChanges(): void {\n    this.changeDetectorRef.checkNoChanges();\n  }\n\n  /**\n   * Set whether the fixture should autodetect changes.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   *\n   * @param autoDetect Whether to autodetect changes. By default, `true`.\n   * @deprecated For `autoDetect: true`, use `autoDetectChanges()`.\n   * We have not seen a use-case for `autoDetect: false` but `changeDetectorRef.detach()` is a close equivalent.\n   */\n  autoDetectChanges(autoDetect: boolean): void;\n  /**\n   * Enables automatically synchronizing the view, as it would in an application.\n   *\n   * Also runs detectChanges once so that any existing change is detected.\n   */\n  autoDetectChanges(): void;\n  autoDetectChanges(autoDetect = true): void {\n    if (!autoDetect && this.zonelessEnabled) {\n      throw new Error('Cannot set autoDetect to false with zoneless change detection.');\n    }\n    if (this._noZoneOptionIsSet && !this.zonelessEnabled) {\n      throw new Error('Cannot call autoDetectChanges when ComponentFixtureNoNgZone is set.');\n    }\n\n    if (autoDetect) {\n      this._testAppRef.autoDetectTestViews.add(this.componentRef.hostView);\n    } else {\n      this._testAppRef.autoDetectTestViews.delete(this.componentRef.hostView);\n    }\n\n    this.autoDetect = autoDetect;\n    this.detectChanges();\n  }\n\n  /**\n   * Return whether the fixture is currently stable or has async tasks that have not been completed\n   * yet.\n   */\n  isStable(): boolean {\n    return !this.pendingTasks.hasPendingTasks;\n  }\n\n  /**\n   * Get a promise that resolves when the fixture is stable.\n   *\n   * This can be used to resume testing after events have triggered asynchronous activity or\n   * asynchronous change detection.\n   */\n  whenStable(): Promise<any> {\n    if (this.isStable()) {\n      return Promise.resolve(false);\n    }\n\n    return new Promise((resolve, reject) => {\n      this.appErrorHandler.whenStableRejectFunctions.add(reject);\n      this._appRef.whenStable().then(() => {\n        this.appErrorHandler.whenStableRejectFunctions.delete(reject);\n        resolve(true);\n      });\n    });\n  }\n\n  /**\n   * Retrieves all defer block fixtures in the component fixture.\n   */\n  getDeferBlocks(): Promise<DeferBlockFixture[]> {\n    const deferBlocks: DeferBlockDetails[] = [];\n    const lView = (this.componentRef.hostView as any)['_lView'];\n    getDeferBlocks(lView, deferBlocks);\n\n    const deferBlockFixtures = [];\n    for (const block of deferBlocks) {\n      deferBlockFixtures.push(new DeferBlockFixture(block, this));\n    }\n\n    return Promise.resolve(deferBlockFixtures);\n  }\n\n  private _getRenderer() {\n    if (this._renderer === undefined) {\n      this._renderer = this.componentRef.injector.get(RendererFactory2, null);\n    }\n    return this._renderer as RendererFactory2 | null;\n  }\n\n  /**\n   * Get a promise that resolves when the ui state is stable following animations.\n   */\n  whenRenderingDone(): Promise<any> {\n    const renderer = this._getRenderer();\n    if (renderer && renderer.whenRenderingDone) {\n      return renderer.whenRenderingDone();\n    }\n    return this.whenStable();\n  }\n\n  /**\n   * Trigger component destruction.\n   */\n  destroy(): void {\n    this.subscriptions.unsubscribe();\n    this._testAppRef.autoDetectTestViews.delete(this.componentRef.hostView);\n    this._testAppRef.allTestViews.delete(this.componentRef.hostView);\n    if (!this._isDestroyed) {\n      this.componentRef.destroy();\n      this._isDestroyed = true;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// Needed for the global `Zone` ambient types to be available.\nimport type {} from 'zone.js';\n\nconst _Zone: any = typeof Zone !== 'undefined' ? Zone : null;\nconst fakeAsyncTestModule = _Zone && _Zone[_Zone.__symbol__('fakeAsyncTest')];\n\nconst fakeAsyncTestModuleNotLoadedErrorMessage = `zone-testing.js is needed for the fakeAsync() test helper but could not be found.\n        Please make sure that your environment includes zone.js/testing`;\n\n/**\n * Clears out the shared fake async zone for a test.\n * To be called in a global `beforeEach`.\n *\n * @publicApi\n */\nexport function resetFakeAsyncZone(): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\nexport function resetFakeAsyncZoneIfExists(): void {\n  if (fakeAsyncTestModule && (Zone as any)['ProxyZoneSpec']?.isLoaded()) {\n    fakeAsyncTestModule.resetFakeAsyncZone();\n  }\n}\n\n/**\n * Wraps a function to be executed in the `fakeAsync` zone:\n * - Microtasks are manually executed by calling `flushMicrotasks()`.\n * - Timers are synchronous; `tick()` simulates the asynchronous passage of time.\n *\n * Can be used to wrap `inject()` calls.\n *\n * @param fn The function that you want to wrap in the `fakeAsync` zone.\n * @param options\n *   - flush: When true, will drain the macrotask queue after the test function completes.\n *     When false, will throw an exception at the end of the function if there are pending timers.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n *\n * @returns The function wrapped to be executed in the `fakeAsync` zone.\n * Any arguments passed when calling this returned function will be passed through to the `fn`\n * function in the parameters when it is called.\n *\n * @publicApi\n */\nexport function fakeAsync(fn: Function, options?: {flush?: boolean}): (...args: any[]) => any {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.fakeAsync(fn, options);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Simulates the asynchronous passage of time for the timers in the `fakeAsync` zone.\n *\n * The microtasks queue is drained at the very start of this function and after any timer callback\n * has been executed.\n *\n * @param millis The number of milliseconds to advance the virtual timer.\n * @param tickOptions The options to pass to the `tick()` function.\n *\n * @usageNotes\n *\n * The `tick()` option is a flag called `processNewMacroTasksSynchronously`,\n * which determines whether or not to invoke new macroTasks.\n *\n * If you provide a `tickOptions` object, but do not specify a\n * `processNewMacroTasksSynchronously` property (`tick(100, {})`),\n * then `processNewMacroTasksSynchronously` defaults to true.\n *\n * If you omit the `tickOptions` parameter (`tick(100))`), then\n * `tickOptions` defaults to `{processNewMacroTasksSynchronously: true}`.\n *\n * ### Example\n *\n * {@example core/testing/ts/fake_async.ts region='basic'}\n *\n * The following example includes a nested timeout (new macroTask), and\n * the `tickOptions` parameter is allowed to default. In this case,\n * `processNewMacroTasksSynchronously` defaults to true, and the nested\n * function is executed on each tick.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick();\n *   expect(nestedTimeoutInvoked).toBe(true);\n * }));\n * ```\n *\n * In the following case, `processNewMacroTasksSynchronously` is explicitly\n * set to false, so the nested timeout function is not invoked.\n *\n * ```ts\n * it ('test with nested setTimeout', fakeAsync(() => {\n *   let nestedTimeoutInvoked = false;\n *   function funcWithNestedTimeout() {\n *     setTimeout(() => {\n *       nestedTimeoutInvoked = true;\n *     });\n *   };\n *   setTimeout(funcWithNestedTimeout);\n *   tick(0, {processNewMacroTasksSynchronously: false});\n *   expect(nestedTimeoutInvoked).toBe(false);\n * }));\n * ```\n *\n *\n * @publicApi\n */\nexport function tick(\n  millis: number = 0,\n  tickOptions: {processNewMacroTasksSynchronously: boolean} = {\n    processNewMacroTasksSynchronously: true,\n  },\n): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.tick(millis, tickOptions);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Flushes any pending microtasks and simulates the asynchronous passage of time for the timers in\n * the `fakeAsync` zone by\n * draining the macrotask queue until it is empty.\n *\n * @param maxTurns The maximum number of times the scheduler attempts to clear its queue before\n *     throwing an error.\n * @returns The simulated time elapsed, in milliseconds.\n *\n * @publicApi\n */\nexport function flush(maxTurns?: number): number {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flush(maxTurns);\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Discard all remaining periodic tasks.\n *\n * @publicApi\n */\nexport function discardPeriodicTasks(): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.discardPeriodicTasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n\n/**\n * Flush any pending microtasks.\n *\n * @publicApi\n */\nexport function flushMicrotasks(): void {\n  if (fakeAsyncTestModule) {\n    return fakeAsyncTestModule.flushMicrotasks();\n  }\n  throw new Error(fakeAsyncTestModuleNotLoadedErrorMessage);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵstringify as stringify} from '../../src/core';\n\nimport {MetadataOverride} from './metadata_override';\n\ntype StringMap = {\n  [key: string]: any;\n};\n\nlet _nextReferenceId = 0;\n\nexport class MetadataOverrider {\n  private _references = new Map<any, string>();\n  /**\n   * Creates a new instance for the given metadata class\n   * based on an old instance and overrides.\n   */\n  overrideMetadata<C extends T, T>(\n    metadataClass: {new (options: T): C},\n    oldMetadata: C,\n    override: MetadataOverride<T>,\n  ): C {\n    const props: StringMap = {};\n    if (oldMetadata) {\n      _valueProps(oldMetadata).forEach((prop) => (props[prop] = (<any>oldMetadata)[prop]));\n    }\n\n    if (override.set) {\n      if (override.remove || override.add) {\n        throw new Error(`Cannot set and add/remove ${stringify(metadataClass)} at the same time!`);\n      }\n      setMetadata(props, override.set);\n    }\n    if (override.remove) {\n      removeMetadata(props, override.remove, this._references);\n    }\n    if (override.add) {\n      addMetadata(props, override.add);\n    }\n    return new metadataClass(<any>props);\n  }\n}\n\nfunction removeMetadata(metadata: StringMap, remove: any, references: Map<any, string>) {\n  const removeObjects = new Set<string>();\n  for (const prop in remove) {\n    const removeValue = remove[prop];\n    if (Array.isArray(removeValue)) {\n      removeValue.forEach((value: any) => {\n        removeObjects.add(_propHashKey(prop, value, references));\n      });\n    } else {\n      removeObjects.add(_propHashKey(prop, removeValue, references));\n    }\n  }\n\n  for (const prop in metadata) {\n    const propValue = metadata[prop];\n    if (Array.isArray(propValue)) {\n      metadata[prop] = propValue.filter(\n        (value: any) => !removeObjects.has(_propHashKey(prop, value, references)),\n      );\n    } else {\n      if (removeObjects.has(_propHashKey(prop, propValue, references))) {\n        metadata[prop] = undefined;\n      }\n    }\n  }\n}\n\nfunction addMetadata(metadata: StringMap, add: any) {\n  for (const prop in add) {\n    const addValue = add[prop];\n    const propValue = metadata[prop];\n    if (propValue != null && Array.isArray(propValue)) {\n      metadata[prop] = propValue.concat(addValue);\n    } else {\n      metadata[prop] = addValue;\n    }\n  }\n}\n\nfunction setMetadata(metadata: StringMap, set: any) {\n  for (const prop in set) {\n    metadata[prop] = set[prop];\n  }\n}\n\nfunction _propHashKey(propName: any, propValue: any, references: Map<any, string>): string {\n  let nextObjectId = 0;\n  const objectIds = new Map<object, string>();\n  const replacer = (key: any, value: any) => {\n    if (value !== null && typeof value === 'object') {\n      if (objectIds.has(value)) {\n        return objectIds.get(value);\n      }\n      // Record an id for this object such that any later references use the object's id instead\n      // of the object itself, in order to break cyclic pointers in objects.\n      objectIds.set(value, `ɵobj#${nextObjectId++}`);\n\n      // The first time an object is seen the object itself is serialized.\n      return value;\n    } else if (typeof value === 'function') {\n      value = _serializeReference(value, references);\n    }\n    return value;\n  };\n\n  return `${propName}:${JSON.stringify(propValue, replacer)}`;\n}\n\nfunction _serializeReference(ref: any, references: Map<any, string>): string {\n  let id = references.get(ref);\n  if (!id) {\n    id = `${stringify(ref)}${_nextReferenceId++}`;\n    references.set(ref, id);\n  }\n  return id;\n}\n\nfunction _valueProps(obj: any): string[] {\n  const props: string[] = [];\n  // regular public props\n  Object.keys(obj).forEach((prop) => {\n    if (!prop.startsWith('_')) {\n      props.push(prop);\n    }\n  });\n\n  // getters\n  let proto = obj;\n  while ((proto = Object.getPrototypeOf(proto))) {\n    Object.keys(proto).forEach((protoProp) => {\n      const desc = Object.getOwnPropertyDescriptor(proto, protoProp);\n      if (!protoProp.startsWith('_') && desc && 'get' in desc) {\n        props.push(protoProp);\n      }\n    });\n  }\n  return props;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Component,\n  Directive,\n  NgModule,\n  Pipe,\n  Type,\n  ɵReflectionCapabilities as ReflectionCapabilities,\n} from '../../src/core';\n\nimport {MetadataOverride} from './metadata_override';\nimport {MetadataOverrider} from './metadata_overrider';\n\nconst reflection = new ReflectionCapabilities();\n\n/**\n * Base interface to resolve `@Component`, `@Directive`, `@Pipe` and `@NgModule`.\n */\nexport interface Resolver<T> {\n  addOverride(type: Type<any>, override: MetadataOverride<T>): void;\n  setOverrides(overrides: Array<[Type<any>, MetadataOverride<T>]>): void;\n  resolve(type: Type<any>): T | null;\n}\n\n/**\n * Allows to override ivy metadata for tests (via the `TestBed`).\n */\nabstract class OverrideResolver<T> implements Resolver<T> {\n  private overrides = new Map<Type<any>, MetadataOverride<T>[]>();\n  private resolved = new Map<Type<any>, T | null>();\n\n  abstract get type(): any;\n\n  addOverride(type: Type<any>, override: MetadataOverride<T>) {\n    const overrides = this.overrides.get(type) || [];\n    overrides.push(override);\n    this.overrides.set(type, overrides);\n    this.resolved.delete(type);\n  }\n\n  setOverrides(overrides: Array<[Type<any>, MetadataOverride<T>]>) {\n    this.overrides.clear();\n    overrides.forEach(([type, override]) => {\n      this.addOverride(type, override);\n    });\n  }\n\n  getAnnotation(type: Type<any>): T | null {\n    const annotations = reflection.annotations(type);\n    // Try to find the nearest known Type annotation and make sure that this annotation is an\n    // instance of the type we are looking for, so we can use it for resolution. Note: there might\n    // be multiple known annotations found due to the fact that Components can extend Directives (so\n    // both Directive and Component annotations would be present), so we always check if the known\n    // annotation has the right type.\n    for (let i = annotations.length - 1; i >= 0; i--) {\n      const annotation = annotations[i];\n      const isKnownType =\n        annotation instanceof Directive ||\n        annotation instanceof Component ||\n        annotation instanceof Pipe ||\n        annotation instanceof NgModule;\n      if (isKnownType) {\n        return annotation instanceof this.type ? (annotation as unknown as T) : null;\n      }\n    }\n    return null;\n  }\n\n  resolve(type: Type<any>): T | null {\n    let resolved: T | null = this.resolved.get(type) || null;\n\n    if (!resolved) {\n      resolved = this.getAnnotation(type);\n      if (resolved) {\n        const overrides = this.overrides.get(type);\n        if (overrides) {\n          const overrider = new MetadataOverrider();\n          overrides.forEach((override) => {\n            resolved = overrider.overrideMetadata(this.type, resolved!, override);\n          });\n        }\n      }\n      this.resolved.set(type, resolved);\n    }\n\n    return resolved;\n  }\n}\n\nexport class DirectiveResolver extends OverrideResolver<Directive> {\n  override get type() {\n    return Directive;\n  }\n}\n\nexport class ComponentResolver extends OverrideResolver<Component> {\n  override get type() {\n    return Component;\n  }\n}\n\nexport class PipeResolver extends OverrideResolver<Pipe> {\n  override get type() {\n    return Pipe;\n  }\n}\n\nexport class NgModuleResolver extends OverrideResolver<NgModule> {\n  override get type() {\n    return NgModule;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ResourceLoader} from '@angular/compiler';\nimport {\n  ApplicationInitStatus,\n  ɵINTERNAL_APPLICATION_ERROR_HANDLER as INTERNAL_APPLICATION_ERROR_HANDLER,\n  ɵChangeDetectionScheduler as ChangeDetectionScheduler,\n  ɵChangeDetectionSchedulerImpl as ChangeDetectionSchedulerImpl,\n  Compiler,\n  COMPILER_OPTIONS,\n  Component,\n  Directive,\n  Injector,\n  inject,\n  InjectorType,\n  LOCALE_ID,\n  ModuleWithComponentFactories,\n  ModuleWithProviders,\n  NgModule,\n  NgModuleFactory,\n  Pipe,\n  PlatformRef,\n  Provider,\n  resolveForwardRef,\n  StaticProvider,\n  Type,\n  ɵclearResolutionOfComponentResourcesQueue,\n  ɵcompileComponent as compileComponent,\n  ɵcompileDirective as compileDirective,\n  ɵcompileNgModuleDefs as compileNgModuleDefs,\n  ɵcompilePipe as compilePipe,\n  ɵDEFAULT_LOCALE_ID as DEFAULT_LOCALE_ID,\n  ɵDEFER_BLOCK_CONFIG as DEFER_BLOCK_CONFIG,\n  ɵdepsTracker as depsTracker,\n  ɵDirectiveDef as DirectiveDef,\n  ɵgenerateStandaloneInDeclarationsError,\n  ɵgetAsyncClassMetadataFn as getAsyncClassMetadataFn,\n  ɵgetInjectableDef as getInjectableDef,\n  ɵInternalEnvironmentProviders as InternalEnvironmentProviders,\n  ɵinternalProvideZoneChangeDetection as internalProvideZoneChangeDetection,\n  ɵisComponentDefPendingResolution,\n  ɵisEnvironmentProviders as isEnvironmentProviders,\n  ɵNG_COMP_DEF as NG_COMP_DEF,\n  ɵNG_DIR_DEF as NG_DIR_DEF,\n  ɵNG_INJ_DEF as NG_INJ_DEF,\n  ɵNG_MOD_DEF as NG_MOD_DEF,\n  ɵNG_PIPE_DEF as NG_PIPE_DEF,\n  ɵNgModuleFactory as R3NgModuleFactory,\n  ɵNgModuleTransitiveScopes as NgModuleTransitiveScopes,\n  ɵNgModuleType as NgModuleType,\n  ɵpatchComponentDefWithScope as patchComponentDefWithScope,\n  ɵRender3ComponentFactory as ComponentFactory,\n  ɵRender3NgModuleRef as NgModuleRef,\n  ɵresolveComponentResources,\n  ɵrestoreComponentResolutionQueue,\n  ɵsetLocaleId as setLocaleId,\n  ɵtransitiveScopesFor as transitiveScopesFor,\n  ɵɵInjectableDeclaration as InjectableDeclaration,\n  NgZone,\n  ErrorHandler,\n  ENVIRONMENT_INITIALIZER,\n} from '../../src/core';\n\nimport {ComponentDef, ComponentType} from '../../src/render3';\n\nimport {MetadataOverride} from './metadata_override';\nimport {\n  ComponentResolver,\n  DirectiveResolver,\n  NgModuleResolver,\n  PipeResolver,\n  Resolver,\n} from './resolvers';\nimport {DEFER_BLOCK_DEFAULT_BEHAVIOR, TestModuleMetadata} from './test_bed_common';\nimport {\n  RETHROW_APPLICATION_ERRORS_DEFAULT,\n  TestBedApplicationErrorHandler,\n} from './application_error_handler';\n\nenum TestingModuleOverride {\n  DECLARATION,\n  OVERRIDE_TEMPLATE,\n}\n\nfunction isTestingModuleOverride(value: unknown): value is TestingModuleOverride {\n  return (\n    value === TestingModuleOverride.DECLARATION || value === TestingModuleOverride.OVERRIDE_TEMPLATE\n  );\n}\n\nfunction assertNoStandaloneComponents(\n  types: Type<any>[],\n  resolver: Resolver<any>,\n  location: string,\n) {\n  types.forEach((type) => {\n    if (!getAsyncClassMetadataFn(type)) {\n      const component = resolver.resolve(type);\n      if (component && (component.standalone == null || component.standalone)) {\n        throw new Error(ɵgenerateStandaloneInDeclarationsError(type, location));\n      }\n    }\n  });\n}\n\n// Resolvers for Angular decorators\ntype Resolvers = {\n  module: Resolver<NgModule>;\n  component: Resolver<Directive>;\n  directive: Resolver<Component>;\n  pipe: Resolver<Pipe>;\n};\n\ninterface CleanupOperation {\n  fieldName: string;\n  object: any;\n  originalValue: unknown;\n}\n\nexport class TestBedCompiler {\n  private originalComponentResolutionQueue: Map<Type<any>, Component> | null = null;\n\n  // Testing module configuration\n  private declarations: Type<any>[] = [];\n  private imports: Type<any>[] = [];\n  private providers: Provider[] = [];\n  private schemas: any[] = [];\n\n  // Queues of components/directives/pipes that should be recompiled.\n  private pendingComponents = new Set<Type<any>>();\n  private pendingDirectives = new Set<Type<any>>();\n  private pendingPipes = new Set<Type<any>>();\n\n  // Set of components with async metadata, i.e. components with `@defer` blocks\n  // in their templates.\n  private componentsWithAsyncMetadata = new Set<Type<unknown>>();\n\n  // Keep track of all components and directives, so we can patch Providers onto defs later.\n  private seenComponents = new Set<Type<any>>();\n  private seenDirectives = new Set<Type<any>>();\n\n  // Keep track of overridden modules, so that we can collect all affected ones in the module tree.\n  private overriddenModules = new Set<NgModuleType<any>>();\n\n  // Store resolved styles for Components that have template overrides present and `styleUrls`\n  // defined at the same time.\n  private existingComponentStyles = new Map<Type<any>, string[]>();\n\n  private resolvers: Resolvers = initResolvers();\n\n  // Map of component type to an NgModule that declares it.\n  //\n  // There are a couple special cases:\n  // - for standalone components, the module scope value is `null`\n  // - when a component is declared in `TestBed.configureTestingModule()` call or\n  //   a component's template is overridden via `TestBed.overrideTemplateUsingTestingModule()`.\n  //   we use a special value from the `TestingModuleOverride` enum.\n  private componentToModuleScope = new Map<Type<any>, Type<any> | TestingModuleOverride | null>();\n\n  // Map that keeps initial version of component/directive/pipe defs in case\n  // we compile a Type again, thus overriding respective static fields. This is\n  // required to make sure we restore defs to their initial states between test runs.\n  // Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of an\n  // NgModule), store all of them in a map.\n  private initialNgDefs = new Map<Type<any>, Map<string, PropertyDescriptor | undefined>>();\n\n  // Array that keeps cleanup operations for initial versions of component/directive/pipe/module\n  // defs in case TestBed makes changes to the originals.\n  private defCleanupOps: CleanupOperation[] = [];\n\n  private _injector: Injector | null = null;\n  private compilerProviders: Provider[] | null = null;\n\n  private providerOverrides: Provider[] = [];\n  private rootProviderOverrides: Provider[] = [];\n  // Overrides for injectables with `{providedIn: SomeModule}` need to be tracked and added to that\n  // module's provider list.\n  private providerOverridesByModule = new Map<InjectorType<any>, Provider[]>();\n  private providerOverridesByToken = new Map<any, Provider>();\n  private scopesWithOverriddenProviders = new Set<Type<any>>();\n\n  private testModuleType: NgModuleType<any>;\n  private testModuleRef: NgModuleRef<any> | null = null;\n\n  private deferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n  private rethrowApplicationTickErrors = RETHROW_APPLICATION_ERRORS_DEFAULT;\n\n  constructor(\n    private platform: PlatformRef,\n    private additionalModuleTypes: Type<any> | Type<any>[],\n  ) {\n    class DynamicTestModule {}\n    this.testModuleType = DynamicTestModule as any;\n  }\n\n  setCompilerProviders(providers: Provider[] | null): void {\n    this.compilerProviders = providers;\n    this._injector = null;\n  }\n\n  configureTestingModule(moduleDef: TestModuleMetadata): void {\n    // Enqueue any compilation tasks for the directly declared component.\n    if (moduleDef.declarations !== undefined) {\n      // Verify that there are no standalone components\n      assertNoStandaloneComponents(\n        moduleDef.declarations,\n        this.resolvers.component,\n        '\"TestBed.configureTestingModule\" call',\n      );\n      this.queueTypeArray(moduleDef.declarations, TestingModuleOverride.DECLARATION);\n      this.declarations.push(...moduleDef.declarations);\n    }\n\n    // Enqueue any compilation tasks for imported modules.\n    if (moduleDef.imports !== undefined) {\n      this.queueTypesFromModulesArray(moduleDef.imports);\n      this.imports.push(...moduleDef.imports);\n    }\n\n    if (moduleDef.providers !== undefined) {\n      this.providers.push(...moduleDef.providers);\n    }\n\n    if (moduleDef.schemas !== undefined) {\n      this.schemas.push(...moduleDef.schemas);\n    }\n\n    this.deferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    this.rethrowApplicationTickErrors =\n      moduleDef.rethrowApplicationErrors ?? RETHROW_APPLICATION_ERRORS_DEFAULT;\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): void {\n    depsTracker.clearScopeCacheFor(ngModule);\n    this.overriddenModules.add(ngModule as NgModuleType<any>);\n\n    // Compile the module right away.\n    this.resolvers.module.addOverride(ngModule, override);\n    const metadata = this.resolvers.module.resolve(ngModule);\n    if (metadata === null) {\n      throw invalidTypeError(ngModule.name, 'NgModule');\n    }\n\n    this.recompileNgModule(ngModule, metadata);\n\n    // At this point, the module has a valid module def (ɵmod), but the override may have introduced\n    // new declarations or imported modules. Ingest any possible new types and add them to the\n    // current queue.\n    this.queueTypesFromModulesArray([ngModule]);\n  }\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): void {\n    this.verifyNoStandaloneFlagOverrides(component, override);\n    this.resolvers.component.addOverride(component, override);\n    this.pendingComponents.add(component);\n\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(component);\n  }\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): void {\n    this.verifyNoStandaloneFlagOverrides(directive, override);\n    this.resolvers.directive.addOverride(directive, override);\n    this.pendingDirectives.add(directive);\n  }\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): void {\n    this.verifyNoStandaloneFlagOverrides(pipe, override);\n    this.resolvers.pipe.addOverride(pipe, override);\n    this.pendingPipes.add(pipe);\n  }\n\n  private verifyNoStandaloneFlagOverrides(\n    type: Type<any>,\n    override: MetadataOverride<Component | Directive | Pipe>,\n  ) {\n    if (\n      override.add?.hasOwnProperty('standalone') ||\n      override.set?.hasOwnProperty('standalone') ||\n      override.remove?.hasOwnProperty('standalone')\n    ) {\n      throw new Error(\n        `An override for the ${type.name} class has the \\`standalone\\` flag. ` +\n          `Changing the \\`standalone\\` flag via TestBed overrides is not supported.`,\n      );\n    }\n  }\n\n  overrideProvider(\n    token: any,\n    provider: {useFactory?: Function; useValue?: any; deps?: any[]; multi?: boolean},\n  ): void {\n    let providerDef: Provider;\n    if (provider.useFactory !== undefined) {\n      providerDef = {\n        provide: token,\n        useFactory: provider.useFactory,\n        deps: provider.deps || [],\n        multi: provider.multi,\n      };\n    } else if (provider.useValue !== undefined) {\n      providerDef = {provide: token, useValue: provider.useValue, multi: provider.multi};\n    } else {\n      providerDef = {provide: token};\n    }\n\n    const injectableDef: InjectableDeclaration<any> | null =\n      typeof token !== 'string' ? getInjectableDef(token) : null;\n    const providedIn = injectableDef === null ? null : resolveForwardRef(injectableDef.providedIn);\n    const overridesBucket =\n      providedIn === 'root' ? this.rootProviderOverrides : this.providerOverrides;\n    overridesBucket.push(providerDef);\n\n    // Keep overrides grouped by token as well for fast lookups using token\n    this.providerOverridesByToken.set(token, providerDef);\n    if (injectableDef !== null && providedIn !== null && typeof providedIn !== 'string') {\n      const existingOverrides = this.providerOverridesByModule.get(providedIn);\n      if (existingOverrides !== undefined) {\n        existingOverrides.push(providerDef);\n      } else {\n        this.providerOverridesByModule.set(providedIn, [providerDef]);\n      }\n    }\n  }\n\n  overrideTemplateUsingTestingModule(type: Type<any>, template: string): void {\n    const def = (type as any)[NG_COMP_DEF];\n    const hasStyleUrls = (): boolean => {\n      const metadata = this.resolvers.component.resolve(type)! as Component;\n      return !!metadata.styleUrl || !!metadata.styleUrls?.length;\n    };\n    const overrideStyleUrls = !!def && !ɵisComponentDefPendingResolution(type) && hasStyleUrls();\n\n    // In Ivy, compiling a component does not require knowing the module providing the\n    // component's scope, so overrideTemplateUsingTestingModule can be implemented purely via\n    // overrideComponent. Important: overriding template requires full Component re-compilation,\n    // which may fail in case styleUrls are also present (thus Component is considered as required\n    // resolution). In order to avoid this, we preemptively set styleUrls to an empty array,\n    // preserve current styles available on Component def and restore styles back once compilation\n    // is complete.\n    const override = overrideStyleUrls\n      ? {template, styles: [], styleUrls: [], styleUrl: undefined}\n      : {template};\n    this.overrideComponent(type, {set: override});\n\n    if (overrideStyleUrls && def.styles && def.styles.length > 0) {\n      this.existingComponentStyles.set(type, def.styles);\n    }\n\n    // Set the component's scope to be the testing module.\n    this.componentToModuleScope.set(type, TestingModuleOverride.OVERRIDE_TEMPLATE);\n  }\n\n  private async resolvePendingComponentsWithAsyncMetadata() {\n    if (this.componentsWithAsyncMetadata.size === 0) return;\n\n    const promises = [];\n    for (const component of this.componentsWithAsyncMetadata) {\n      const asyncMetadataFn = getAsyncClassMetadataFn(component);\n      if (asyncMetadataFn) {\n        promises.push(asyncMetadataFn());\n      }\n    }\n    this.componentsWithAsyncMetadata.clear();\n\n    const resolvedDeps = await Promise.all(promises);\n    const flatResolvedDeps = resolvedDeps.flat(2);\n    this.queueTypesFromModulesArray(flatResolvedDeps);\n\n    // Loaded standalone components might contain imports of NgModules\n    // with providers, make sure we override providers there too.\n    for (const component of flatResolvedDeps) {\n      this.applyProviderOverridesInScope(component);\n    }\n  }\n\n  async compileComponents(): Promise<void> {\n    this.clearComponentResolutionQueue();\n\n    // Wait for all async metadata for components that were\n    // overridden, we need resolved metadata to perform an override\n    // and re-compile a component.\n    await this.resolvePendingComponentsWithAsyncMetadata();\n\n    // Verify that there were no standalone components present in the `declarations` field\n    // during the `TestBed.configureTestingModule` call. We perform this check here in addition\n    // to the logic in the `configureTestingModule` function, since at this point we have\n    // all async metadata resolved.\n    assertNoStandaloneComponents(\n      this.declarations,\n      this.resolvers.component,\n      '\"TestBed.configureTestingModule\" call',\n    );\n\n    // Run compilers for all queued types.\n    let needsAsyncResources = this.compileTypesSync();\n\n    // compileComponents() should not be async unless it needs to be.\n    if (needsAsyncResources) {\n      let resourceLoader: ResourceLoader;\n      let resolver = (url: string): Promise<string> => {\n        if (!resourceLoader) {\n          resourceLoader = this.injector.get(ResourceLoader);\n        }\n        return Promise.resolve(resourceLoader.get(url));\n      };\n      await ɵresolveComponentResources(resolver);\n    }\n  }\n\n  finalize(): NgModuleRef<any> {\n    // One last compile\n    this.compileTypesSync();\n\n    // Create the testing module itself.\n    this.compileTestModule();\n\n    this.applyTransitiveScopes();\n\n    this.applyProviderOverrides();\n\n    // Patch previously stored `styles` Component values (taken from ɵcmp), in case these\n    // Components have `styleUrls` fields defined and template override was requested.\n    this.patchComponentsWithExistingStyles();\n\n    // Clear the componentToModuleScope map, so that future compilations don't reset the scope of\n    // every component.\n    this.componentToModuleScope.clear();\n\n    const parentInjector = this.platform.injector;\n    this.testModuleRef = new NgModuleRef(this.testModuleType, parentInjector, []);\n\n    // ApplicationInitStatus.runInitializers() is marked @internal to core.\n    // Cast it to any before accessing it.\n    (this.testModuleRef.injector.get(ApplicationInitStatus) as any).runInitializers();\n\n    // Set locale ID after running app initializers, since locale information might be updated while\n    // running initializers. This is also consistent with the execution order while bootstrapping an\n    // app (see `packages/core/src/application_ref.ts` file).\n    const localeId = this.testModuleRef.injector.get(LOCALE_ID, DEFAULT_LOCALE_ID);\n    setLocaleId(localeId);\n\n    return this.testModuleRef;\n  }\n\n  /**\n   * @internal\n   */\n  _compileNgModuleSync(moduleType: Type<any>): void {\n    this.queueTypesFromModulesArray([moduleType]);\n    this.compileTypesSync();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n\n  /**\n   * @internal\n   */\n  async _compileNgModuleAsync(moduleType: Type<any>): Promise<void> {\n    this.queueTypesFromModulesArray([moduleType]);\n    await this.compileComponents();\n    this.applyProviderOverrides();\n    this.applyProviderOverridesInScope(moduleType);\n    this.applyTransitiveScopes();\n  }\n\n  /**\n   * @internal\n   */\n  _getModuleResolver(): Resolver<NgModule> {\n    return this.resolvers.module;\n  }\n\n  /**\n   * @internal\n   */\n  _getComponentFactories(moduleType: NgModuleType): ComponentFactory<any>[] {\n    return maybeUnwrapFn(moduleType.ɵmod.declarations).reduce((factories, declaration) => {\n      const componentDef = (declaration as any).ɵcmp;\n      componentDef && factories.push(new ComponentFactory(componentDef, this.testModuleRef!));\n      return factories;\n    }, [] as ComponentFactory<any>[]);\n  }\n\n  private compileTypesSync(): boolean {\n    // Compile all queued components, directives, pipes.\n    let needsAsyncResources = false;\n    this.pendingComponents.forEach((declaration) => {\n      if (getAsyncClassMetadataFn(declaration)) {\n        throw new Error(\n          `Component '${declaration.name}' has unresolved metadata. ` +\n            `Please call \\`await TestBed.compileComponents()\\` before running this test.`,\n        );\n      }\n\n      needsAsyncResources = needsAsyncResources || ɵisComponentDefPendingResolution(declaration);\n\n      const metadata = this.resolvers.component.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Component');\n      }\n\n      this.maybeStoreNgDef(NG_COMP_DEF, declaration);\n      depsTracker.clearScopeCacheFor(declaration);\n      compileComponent(declaration, metadata);\n    });\n    this.pendingComponents.clear();\n\n    this.pendingDirectives.forEach((declaration) => {\n      const metadata = this.resolvers.directive.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Directive');\n      }\n      this.maybeStoreNgDef(NG_DIR_DEF, declaration);\n      compileDirective(declaration, metadata);\n    });\n    this.pendingDirectives.clear();\n\n    this.pendingPipes.forEach((declaration) => {\n      const metadata = this.resolvers.pipe.resolve(declaration);\n      if (metadata === null) {\n        throw invalidTypeError(declaration.name, 'Pipe');\n      }\n      this.maybeStoreNgDef(NG_PIPE_DEF, declaration);\n      compilePipe(declaration, metadata);\n    });\n    this.pendingPipes.clear();\n\n    return needsAsyncResources;\n  }\n\n  private applyTransitiveScopes(): void {\n    if (this.overriddenModules.size > 0) {\n      // Module overrides (via `TestBed.overrideModule`) might affect scopes that were previously\n      // calculated and stored in `transitiveCompileScopes`. If module overrides are present,\n      // collect all affected modules and reset scopes to force their re-calculation.\n      const testingModuleDef = (this.testModuleType as any)[NG_MOD_DEF];\n      const affectedModules = this.collectModulesAffectedByOverrides(testingModuleDef.imports);\n      if (affectedModules.size > 0) {\n        affectedModules.forEach((moduleType) => {\n          depsTracker.clearScopeCacheFor(moduleType);\n        });\n      }\n    }\n\n    const moduleToScope = new Map<Type<any> | TestingModuleOverride, NgModuleTransitiveScopes>();\n    const getScopeOfModule = (\n      moduleType: Type<any> | TestingModuleOverride,\n    ): NgModuleTransitiveScopes => {\n      if (!moduleToScope.has(moduleType)) {\n        const isTestingModule = isTestingModuleOverride(moduleType);\n        const realType = isTestingModule ? this.testModuleType : (moduleType as Type<any>);\n        moduleToScope.set(moduleType, transitiveScopesFor(realType));\n      }\n      return moduleToScope.get(moduleType)!;\n    };\n\n    this.componentToModuleScope.forEach((moduleType, componentType) => {\n      if (moduleType !== null) {\n        const moduleScope = getScopeOfModule(moduleType);\n        this.storeFieldOfDefOnType(componentType, NG_COMP_DEF, 'directiveDefs');\n        this.storeFieldOfDefOnType(componentType, NG_COMP_DEF, 'pipeDefs');\n        patchComponentDefWithScope(getComponentDef(componentType)!, moduleScope);\n      }\n      // `tView` that is stored on component def contains information about directives and pipes\n      // that are in the scope of this component. Patching component scope will cause `tView` to be\n      // changed. Store original `tView` before patching scope, so the `tView` (including scope\n      // information) is restored back to its previous/original state before running next test.\n      // Resetting `tView` is also needed for cases when we apply provider overrides and those\n      // providers are defined on component's level, in which case they may end up included into\n      // `tView.blueprint`.\n      this.storeFieldOfDefOnType(componentType, NG_COMP_DEF, 'tView');\n    });\n\n    this.componentToModuleScope.clear();\n  }\n\n  private applyProviderOverrides(): void {\n    const maybeApplyOverrides = (field: string) => (type: Type<any>) => {\n      const resolver = field === NG_COMP_DEF ? this.resolvers.component : this.resolvers.directive;\n      const metadata = resolver.resolve(type)!;\n      if (this.hasProviderOverrides(metadata.providers)) {\n        this.patchDefWithProviderOverrides(type, field);\n      }\n    };\n    this.seenComponents.forEach(maybeApplyOverrides(NG_COMP_DEF));\n    this.seenDirectives.forEach(maybeApplyOverrides(NG_DIR_DEF));\n\n    this.seenComponents.clear();\n    this.seenDirectives.clear();\n  }\n\n  /**\n   * Applies provider overrides to a given type (either an NgModule or a standalone component)\n   * and all imported NgModules and standalone components recursively.\n   */\n  private applyProviderOverridesInScope(type: Type<any>): void {\n    const hasScope = isStandaloneComponent(type) || isNgModule(type);\n\n    // The function can be re-entered recursively while inspecting dependencies\n    // of an NgModule or a standalone component. Exit early if we come across a\n    // type that can not have a scope (directive or pipe) or the type is already\n    // processed earlier.\n    if (!hasScope || this.scopesWithOverriddenProviders.has(type)) {\n      return;\n    }\n    this.scopesWithOverriddenProviders.add(type);\n\n    // NOTE: the line below triggers JIT compilation of the module injector,\n    // which also invokes verification of the NgModule semantics, which produces\n    // detailed error messages. The fact that the code relies on this line being\n    // present here is suspicious and should be refactored in a way that the line\n    // below can be moved (for ex. after an early exit check below).\n    const injectorDef: any = (type as any)[NG_INJ_DEF];\n\n    // No provider overrides, exit early.\n    if (this.providerOverridesByToken.size === 0) return;\n\n    if (isStandaloneComponent(type)) {\n      // Visit all component dependencies and override providers there.\n      const def = getComponentDef(type);\n      const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n      for (const dependency of dependencies) {\n        this.applyProviderOverridesInScope(dependency);\n      }\n    } else {\n      const providers: Array<Provider | InternalEnvironmentProviders> = [\n        ...injectorDef.providers,\n        ...(this.providerOverridesByModule.get(type as InjectorType<any>) || []),\n      ];\n      if (this.hasProviderOverrides(providers)) {\n        this.maybeStoreNgDef(NG_INJ_DEF, type);\n\n        this.storeFieldOfDefOnType(type, NG_INJ_DEF, 'providers');\n        injectorDef.providers = this.getOverriddenProviders(providers);\n      }\n\n      // Apply provider overrides to imported modules recursively\n      const moduleDef = (type as any)[NG_MOD_DEF];\n      const imports = maybeUnwrapFn(moduleDef.imports);\n      for (const importedModule of imports) {\n        this.applyProviderOverridesInScope(importedModule);\n      }\n      // Also override the providers on any ModuleWithProviders imports since those don't appear in\n      // the moduleDef.\n      for (const importedModule of flatten(injectorDef.imports)) {\n        if (isModuleWithProviders(importedModule)) {\n          this.defCleanupOps.push({\n            object: importedModule,\n            fieldName: 'providers',\n            originalValue: importedModule.providers,\n          });\n          importedModule.providers = this.getOverriddenProviders(\n            importedModule.providers as Array<Provider | InternalEnvironmentProviders>,\n          );\n        }\n      }\n    }\n  }\n\n  private patchComponentsWithExistingStyles(): void {\n    this.existingComponentStyles.forEach(\n      (styles, type) => ((type as any)[NG_COMP_DEF].styles = styles),\n    );\n    this.existingComponentStyles.clear();\n  }\n\n  private queueTypeArray(arr: any[], moduleType: Type<any> | TestingModuleOverride): void {\n    for (const value of arr) {\n      if (Array.isArray(value)) {\n        this.queueTypeArray(value, moduleType);\n      } else {\n        this.queueType(value, moduleType);\n      }\n    }\n  }\n\n  private recompileNgModule(ngModule: Type<any>, metadata: NgModule): void {\n    // Cache the initial ngModuleDef as it will be overwritten.\n    this.maybeStoreNgDef(NG_MOD_DEF, ngModule);\n    this.maybeStoreNgDef(NG_INJ_DEF, ngModule);\n\n    compileNgModuleDefs(ngModule as NgModuleType<any>, metadata);\n  }\n\n  private maybeRegisterComponentWithAsyncMetadata(type: Type<unknown>) {\n    const asyncMetadataFn = getAsyncClassMetadataFn(type);\n    if (asyncMetadataFn) {\n      this.componentsWithAsyncMetadata.add(type);\n    }\n  }\n\n  private queueType(type: Type<any>, moduleType: Type<any> | TestingModuleOverride | null): void {\n    // If this is a component with async metadata (i.e. a component with a `@defer` block\n    // in a template) - store it for future processing.\n    this.maybeRegisterComponentWithAsyncMetadata(type);\n\n    const component = this.resolvers.component.resolve(type);\n    if (component) {\n      // Check whether a give Type has respective NG def (ɵcmp) and compile if def is\n      // missing. That might happen in case a class without any Angular decorators extends another\n      // class where Component/Directive/Pipe decorator is defined.\n      if (ɵisComponentDefPendingResolution(type) || !type.hasOwnProperty(NG_COMP_DEF)) {\n        this.pendingComponents.add(type);\n      }\n      this.seenComponents.add(type);\n\n      // Keep track of the module which declares this component, so later the component's scope\n      // can be set correctly. If the component has already been recorded here, then one of several\n      // cases is true:\n      // * the module containing the component was imported multiple times (common).\n      // * the component is declared in multiple modules (which is an error).\n      // * the component was in 'declarations' of the testing module, and also in an imported module\n      //   in which case the module scope will be TestingModuleOverride.DECLARATION.\n      // * overrideTemplateUsingTestingModule was called for the component in which case the module\n      //   scope will be TestingModuleOverride.OVERRIDE_TEMPLATE.\n      //\n      // If the component was previously in the testing module's 'declarations' (meaning the\n      // current value is TestingModuleOverride.DECLARATION), then `moduleType` is the component's\n      // real module, which was imported. This pattern is understood to mean that the component\n      // should use its original scope, but that the testing module should also contain the\n      // component in its scope.\n      if (\n        !this.componentToModuleScope.has(type) ||\n        this.componentToModuleScope.get(type) === TestingModuleOverride.DECLARATION\n      ) {\n        this.componentToModuleScope.set(type, moduleType);\n      }\n      return;\n    }\n\n    const directive = this.resolvers.directive.resolve(type);\n    if (directive) {\n      if (!type.hasOwnProperty(NG_DIR_DEF)) {\n        this.pendingDirectives.add(type);\n      }\n      this.seenDirectives.add(type);\n      return;\n    }\n\n    const pipe = this.resolvers.pipe.resolve(type);\n    if (pipe && !type.hasOwnProperty(NG_PIPE_DEF)) {\n      this.pendingPipes.add(type);\n      return;\n    }\n  }\n\n  private queueTypesFromModulesArray(arr: any[]): void {\n    // Because we may encounter the same NgModule or a standalone Component while processing\n    // the dependencies of an NgModule or a standalone Component, we cache them in this set so we\n    // can skip ones that have already been seen encountered. In some test setups, this caching\n    // resulted in 10X runtime improvement.\n    const processedDefs = new Set();\n    const queueTypesFromModulesArrayRecur = (arr: any[]): void => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          queueTypesFromModulesArrayRecur(value);\n        } else if (hasNgModuleDef(value)) {\n          const def = value.ɵmod;\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n          // Look through declarations, imports, and exports, and queue\n          // everything found there.\n          this.queueTypeArray(maybeUnwrapFn(def.declarations), value);\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.imports));\n          queueTypesFromModulesArrayRecur(maybeUnwrapFn(def.exports));\n        } else if (isModuleWithProviders(value)) {\n          queueTypesFromModulesArrayRecur([value.ngModule]);\n        } else if (isStandaloneComponent(value)) {\n          this.queueType(value, null);\n          const def = getComponentDef(value);\n\n          if (processedDefs.has(def)) {\n            continue;\n          }\n          processedDefs.add(def);\n\n          const dependencies = maybeUnwrapFn(def.dependencies ?? []);\n          dependencies.forEach((dependency) => {\n            // Note: in AOT, the `dependencies` might also contain regular\n            // (NgModule-based) Component, Directive and Pipes, so we handle\n            // them separately and proceed with recursive process for standalone\n            // Components and NgModules only.\n            if (isStandaloneComponent(dependency) || hasNgModuleDef(dependency)) {\n              queueTypesFromModulesArrayRecur([dependency]);\n            } else {\n              this.queueType(dependency, null);\n            }\n          });\n        }\n      }\n    };\n    queueTypesFromModulesArrayRecur(arr);\n  }\n\n  // When module overrides (via `TestBed.overrideModule`) are present, it might affect all modules\n  // that import (even transitively) an overridden one. For all affected modules we need to\n  // recalculate their scopes for a given test run and restore original scopes at the end. The goal\n  // of this function is to collect all affected modules in a set for further processing. Example:\n  // if we have the following module hierarchy: A -> B -> C (where `->` means `imports`) and module\n  // `C` is overridden, we consider `A` and `B` as affected, since their scopes might become\n  // invalidated with the override.\n  private collectModulesAffectedByOverrides(arr: any[]): Set<NgModuleType<any>> {\n    const seenModules = new Set<NgModuleType<any>>();\n    const affectedModules = new Set<NgModuleType<any>>();\n    const calcAffectedModulesRecur = (arr: any[], path: NgModuleType<any>[]): void => {\n      for (const value of arr) {\n        if (Array.isArray(value)) {\n          // If the value is an array, just flatten it (by invoking this function recursively),\n          // keeping \"path\" the same.\n          calcAffectedModulesRecur(value, path);\n        } else if (hasNgModuleDef(value)) {\n          if (seenModules.has(value)) {\n            // If we've seen this module before and it's included into \"affected modules\" list, mark\n            // the whole path that leads to that module as affected, but do not descend into its\n            // imports, since we already examined them before.\n            if (affectedModules.has(value)) {\n              path.forEach((item) => affectedModules.add(item));\n            }\n            continue;\n          }\n          seenModules.add(value);\n          if (this.overriddenModules.has(value)) {\n            path.forEach((item) => affectedModules.add(item));\n          }\n          // Examine module imports recursively to look for overridden modules.\n          const moduleDef = (value as any)[NG_MOD_DEF];\n          calcAffectedModulesRecur(maybeUnwrapFn(moduleDef.imports), path.concat(value));\n        }\n      }\n    };\n    calcAffectedModulesRecur(arr, []);\n    return affectedModules;\n  }\n\n  /**\n   * Preserve an original def (such as ɵmod, ɵinj, etc) before applying an override.\n   * Note: one class may have multiple defs (for example: ɵmod and ɵinj in case of\n   * an NgModule). If there is a def in a set already, don't override it, since\n   * an original one should be restored at the end of a test.\n   */\n  private maybeStoreNgDef(prop: string, type: Type<any>) {\n    if (!this.initialNgDefs.has(type)) {\n      this.initialNgDefs.set(type, new Map());\n    }\n    const currentDefs = this.initialNgDefs.get(type)!;\n    if (!currentDefs.has(prop)) {\n      const currentDef = Object.getOwnPropertyDescriptor(type, prop);\n      currentDefs.set(prop, currentDef);\n    }\n  }\n\n  private storeFieldOfDefOnType(type: Type<any>, defField: string, fieldName: string): void {\n    const def: any = (type as any)[defField];\n    const originalValue: any = def[fieldName];\n    this.defCleanupOps.push({object: def, fieldName, originalValue});\n  }\n\n  /**\n   * Clears current components resolution queue, but stores the state of the queue, so we can\n   * restore it later. Clearing the queue is required before we try to compile components (via\n   * `TestBed.compileComponents`), so that component defs are in sync with the resolution queue.\n   */\n  private clearComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue === null) {\n      this.originalComponentResolutionQueue = new Map();\n    }\n    ɵclearResolutionOfComponentResourcesQueue().forEach((value, key) =>\n      this.originalComponentResolutionQueue!.set(key, value),\n    );\n  }\n\n  /*\n   * Restores component resolution queue to the previously saved state. This operation is performed\n   * as a part of restoring the state after completion of the current set of tests (that might\n   * potentially mutate the state).\n   */\n  private restoreComponentResolutionQueue() {\n    if (this.originalComponentResolutionQueue !== null) {\n      ɵrestoreComponentResolutionQueue(this.originalComponentResolutionQueue);\n      this.originalComponentResolutionQueue = null;\n    }\n  }\n\n  restoreOriginalState(): void {\n    // Process cleanup ops in reverse order so the field's original value is restored correctly (in\n    // case there were multiple overrides for the same field).\n    forEachRight(this.defCleanupOps, (op: CleanupOperation) => {\n      op.object[op.fieldName] = op.originalValue;\n    });\n    // Restore initial component/directive/pipe defs\n    this.initialNgDefs.forEach(\n      (defs: Map<string, PropertyDescriptor | undefined>, type: Type<any>) => {\n        depsTracker.clearScopeCacheFor(type);\n        defs.forEach((descriptor, prop) => {\n          if (!descriptor) {\n            // Delete operations are generally undesirable since they have performance\n            // implications on objects they were applied to. In this particular case, situations\n            // where this code is invoked should be quite rare to cause any noticeable impact,\n            // since it's applied only to some test cases (for example when class with no\n            // annotations extends some @Component) when we need to clear 'ɵcmp' field on a given\n            // class to restore its original state (before applying overrides and running tests).\n            delete (type as any)[prop];\n          } else {\n            Object.defineProperty(type, prop, descriptor);\n          }\n        });\n      },\n    );\n    this.initialNgDefs.clear();\n    this.scopesWithOverriddenProviders.clear();\n    this.restoreComponentResolutionQueue();\n    // Restore the locale ID to the default value, this shouldn't be necessary but we never know\n    setLocaleId(DEFAULT_LOCALE_ID);\n  }\n\n  private compileTestModule(): void {\n    class RootScopeModule {}\n    compileNgModuleDefs(RootScopeModule as NgModuleType<any>, {\n      providers: [\n        ...this.rootProviderOverrides,\n        internalProvideZoneChangeDetection({}),\n        TestBedApplicationErrorHandler,\n        {provide: ChangeDetectionScheduler, useExisting: ChangeDetectionSchedulerImpl},\n        {\n          provide: ENVIRONMENT_INITIALIZER,\n          multi: true,\n          useValue: () => {\n            inject(ErrorHandler);\n          },\n        },\n      ],\n    });\n\n    const providers = [\n      {provide: Compiler, useFactory: () => new R3TestCompiler(this)},\n      {provide: DEFER_BLOCK_CONFIG, useValue: {behavior: this.deferBlockBehavior}},\n      {\n        provide: INTERNAL_APPLICATION_ERROR_HANDLER,\n        useFactory: () => {\n          if (this.rethrowApplicationTickErrors) {\n            const handler = inject(TestBedApplicationErrorHandler);\n            return (e: unknown) => {\n              handler.handleError(e);\n            };\n          } else {\n            const userErrorHandler = inject(ErrorHandler);\n            const ngZone = inject(NgZone);\n            return (e: unknown) => ngZone.runOutsideAngular(() => userErrorHandler.handleError(e));\n          }\n        },\n      },\n      ...this.providers,\n      ...this.providerOverrides,\n    ];\n    const imports = [RootScopeModule, this.additionalModuleTypes, this.imports || []];\n\n    compileNgModuleDefs(\n      this.testModuleType,\n      {\n        declarations: this.declarations,\n        imports,\n        schemas: this.schemas,\n        providers,\n      },\n      /* allowDuplicateDeclarationsInRoot */ true,\n    );\n\n    this.applyProviderOverridesInScope(this.testModuleType);\n  }\n\n  get injector(): Injector {\n    if (this._injector !== null) {\n      return this._injector;\n    }\n\n    const providers: StaticProvider[] = [];\n    const compilerOptions = this.platform.injector.get(COMPILER_OPTIONS, []);\n    compilerOptions.forEach((opts) => {\n      if (opts.providers) {\n        providers.push(opts.providers);\n      }\n    });\n    if (this.compilerProviders !== null) {\n      providers.push(...(this.compilerProviders as StaticProvider[]));\n    }\n\n    this._injector = Injector.create({providers, parent: this.platform.injector});\n    return this._injector;\n  }\n\n  // get overrides for a specific provider (if any)\n  private getSingleProviderOverrides(provider: Provider): Provider | null {\n    const token = getProviderToken(provider);\n    return this.providerOverridesByToken.get(token) || null;\n  }\n\n  private getProviderOverrides(\n    providers?: Array<Provider | InternalEnvironmentProviders>,\n  ): Provider[] {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n    // There are two flattening operations here. The inner flattenProviders() operates on the\n    // metadata's providers and applies a mapping function which retrieves overrides for each\n    // incoming provider. The outer flatten() then flattens the produced overrides array. If this is\n    // not done, the array can contain other empty arrays (e.g. `[[], []]`) which leak into the\n    // providers array and contaminate any error messages that might be generated.\n    return flatten(\n      flattenProviders(\n        providers,\n        (provider: Provider) => this.getSingleProviderOverrides(provider) || [],\n      ),\n    );\n  }\n\n  private getOverriddenProviders(\n    providers?: Array<Provider | InternalEnvironmentProviders>,\n  ): Provider[] {\n    if (!providers || !providers.length || this.providerOverridesByToken.size === 0) return [];\n\n    const flattenedProviders = flattenProviders(providers);\n    const overrides = this.getProviderOverrides(flattenedProviders);\n    const overriddenProviders = [...flattenedProviders, ...overrides];\n    const final: Provider[] = [];\n    const seenOverriddenProviders = new Set<Provider>();\n\n    // We iterate through the list of providers in reverse order to make sure provider overrides\n    // take precedence over the values defined in provider list. We also filter out all providers\n    // that have overrides, keeping overridden values only. This is needed, since presence of a\n    // provider with `ngOnDestroy` hook will cause this hook to be registered and invoked later.\n    forEachRight(overriddenProviders, (provider: any) => {\n      const token: any = getProviderToken(provider);\n      if (this.providerOverridesByToken.has(token)) {\n        if (!seenOverriddenProviders.has(token)) {\n          seenOverriddenProviders.add(token);\n          // Treat all overridden providers as `{multi: false}` (even if it's a multi-provider) to\n          // make sure that provided override takes highest precedence and is not combined with\n          // other instances of the same multi provider.\n          final.unshift({...provider, multi: false});\n        }\n      } else {\n        final.unshift(provider);\n      }\n    });\n    return final;\n  }\n\n  private hasProviderOverrides(\n    providers?: Array<Provider | InternalEnvironmentProviders>,\n  ): boolean {\n    return this.getProviderOverrides(providers).length > 0;\n  }\n\n  private patchDefWithProviderOverrides(declaration: Type<any>, field: string): void {\n    const def = (declaration as any)[field];\n    if (def && def.providersResolver) {\n      this.maybeStoreNgDef(field, declaration);\n\n      const resolver = def.providersResolver;\n      const processProvidersFn = (providers: Provider[]) => this.getOverriddenProviders(providers);\n      this.storeFieldOfDefOnType(declaration, field, 'providersResolver');\n      def.providersResolver = (ngDef: DirectiveDef<any>) => resolver(ngDef, processProvidersFn);\n    }\n  }\n}\n\nfunction initResolvers(): Resolvers {\n  return {\n    module: new NgModuleResolver(),\n    component: new ComponentResolver(),\n    directive: new DirectiveResolver(),\n    pipe: new PipeResolver(),\n  };\n}\n\nfunction isStandaloneComponent<T>(value: Type<T>): value is ComponentType<T> {\n  const def = getComponentDef(value);\n  return !!def?.standalone;\n}\n\nfunction getComponentDef(value: ComponentType<unknown>): ComponentDef<unknown>;\nfunction getComponentDef(value: Type<unknown>): ComponentDef<unknown> | null;\nfunction getComponentDef(value: Type<unknown>): ComponentDef<unknown> | null {\n  return (value as any).ɵcmp ?? null;\n}\n\nfunction hasNgModuleDef<T>(value: Type<T>): value is NgModuleType<T> {\n  return value.hasOwnProperty('ɵmod');\n}\n\nfunction isNgModule<T>(value: Type<T>): boolean {\n  return hasNgModuleDef(value);\n}\n\nfunction maybeUnwrapFn<T>(maybeFn: (() => T) | T): T {\n  return maybeFn instanceof Function ? maybeFn() : maybeFn;\n}\n\nfunction flatten<T>(values: any[]): T[] {\n  const out: T[] = [];\n  values.forEach((value) => {\n    if (Array.isArray(value)) {\n      out.push(...flatten<T>(value));\n    } else {\n      out.push(value);\n    }\n  });\n  return out;\n}\n\nfunction identityFn<T>(value: T): T {\n  return value;\n}\n\nfunction flattenProviders<T>(\n  providers: Array<Provider | InternalEnvironmentProviders>,\n  mapFn: (provider: Provider) => T,\n): T[];\nfunction flattenProviders(providers: Array<Provider | InternalEnvironmentProviders>): Provider[];\nfunction flattenProviders(\n  providers: Array<Provider | InternalEnvironmentProviders>,\n  mapFn: (provider: Provider) => any = identityFn,\n): any[] {\n  const out: any[] = [];\n  for (let provider of providers) {\n    if (isEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      out.push(...flattenProviders(provider, mapFn));\n    } else {\n      out.push(mapFn(provider));\n    }\n  }\n  return out;\n}\n\nfunction getProviderField(provider: Provider, field: string) {\n  return provider && typeof provider === 'object' && (provider as any)[field];\n}\n\nfunction getProviderToken(provider: Provider) {\n  return getProviderField(provider, 'provide') || provider;\n}\n\nfunction isModuleWithProviders(value: any): value is ModuleWithProviders<any> {\n  return value.hasOwnProperty('ngModule');\n}\n\nfunction forEachRight<T>(values: T[], fn: (value: T, idx: number) => void): void {\n  for (let idx = values.length - 1; idx >= 0; idx--) {\n    fn(values[idx], idx);\n  }\n}\n\nfunction invalidTypeError(name: string, expectedType: string): Error {\n  return new Error(`${name} class doesn't have @${expectedType} decorator or is missing metadata.`);\n}\n\nclass R3TestCompiler implements Compiler {\n  constructor(private testBed: TestBedCompiler) {}\n\n  compileModuleSync<T>(moduleType: Type<T>): NgModuleFactory<T> {\n    this.testBed._compileNgModuleSync(moduleType);\n    return new R3NgModuleFactory(moduleType);\n  }\n\n  async compileModuleAsync<T>(moduleType: Type<T>): Promise<NgModuleFactory<T>> {\n    await this.testBed._compileNgModuleAsync(moduleType);\n    return new R3NgModuleFactory(moduleType);\n  }\n\n  compileModuleAndAllComponentsSync<T>(moduleType: Type<T>): ModuleWithComponentFactories<T> {\n    const ngModuleFactory = this.compileModuleSync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType as NgModuleType<T>);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n\n  async compileModuleAndAllComponentsAsync<T>(\n    moduleType: Type<T>,\n  ): Promise<ModuleWithComponentFactories<T>> {\n    const ngModuleFactory = await this.compileModuleAsync(moduleType);\n    const componentFactories = this.testBed._getComponentFactories(moduleType as NgModuleType<T>);\n    return new ModuleWithComponentFactories(ngModuleFactory, componentFactories);\n  }\n\n  clearCache(): void {}\n\n  clearCacheFor(type: Type<any>): void {}\n\n  getModuleId(moduleType: Type<any>): string | undefined {\n    const meta = this.testBed._getModuleResolver().resolve(moduleType);\n    return (meta && meta.id) || undefined;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// The formatter and CI disagree on how this import statement should be formatted. Both try to keep\n// it on one line, too, which has gotten very hard to read & manage. So disable the formatter for\n// this statement only.\n\nimport {\n  ApplicationRef,\n  Component,\n  ɵRender3ComponentFactory as ComponentFactory,\n  ComponentRef,\n  ɵDeferBlockBehavior as DeferBlockBehavior,\n  Directive,\n  EnvironmentInjector,\n  ɵflushModuleScopingQueueAsMuchAsPossible as flushModuleScopingQueueAsMuchAsPossible,\n  ɵgetAsyncClassMetadataFn as getAsyncClassMetadataFn,\n  ɵgetUnknownElementStrictMode as getUnknownElementStrictMode,\n  ɵgetUnknownPropertyStrictMode as getUnknownPropertyStrictMode,\n  InjectOptions,\n  Injector,\n  NgModule,\n  ɵRender3NgModuleRef as NgModuleRef,\n  NgZone,\n  Pipe,\n  PlatformRef,\n  ProviderToken,\n  ɵresetCompiledComponents as resetCompiledComponents,\n  runInInjectionContext,\n  ɵsetAllowDuplicateNgModuleIdsForTest as setAllowDuplicateNgModuleIdsForTest,\n  ɵsetUnknownElementStrictMode as setUnknownElementStrictMode,\n  ɵsetUnknownPropertyStrictMode as setUnknownPropertyStrictMode,\n  ɵstringify as stringify,\n  Type,\n} from '../../src/core';\n\nimport {ComponentFixture} from './component_fixture';\nimport {MetadataOverride} from './metadata_override';\nimport {\n  ComponentFixtureNoNgZone,\n  DEFER_BLOCK_DEFAULT_BEHAVIOR,\n  ModuleTeardownOptions,\n  TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT,\n  TestComponentRenderer,\n  TestEnvironmentOptions,\n  TestModuleMetadata,\n  THROW_ON_UNKNOWN_ELEMENTS_DEFAULT,\n  THROW_ON_UNKNOWN_PROPERTIES_DEFAULT,\n} from './test_bed_common';\nimport {TestBedCompiler} from './test_bed_compiler';\n\n/**\n * Static methods implemented by the `TestBed`.\n *\n * @publicApi\n */\nexport interface TestBedStatic extends TestBed {\n  new (...args: any[]): TestBed;\n}\n\n/**\n * @publicApi\n */\nexport interface TestBed {\n  get platform(): PlatformRef;\n\n  get ngModule(): Type<any> | Type<any>[];\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   */\n  initTestEnvironment(\n    ngModule: Type<any> | Type<any>[],\n    platform: PlatformRef,\n    options?: TestEnvironmentOptions,\n  ): void;\n\n  /**\n   * Reset the providers for the test injector.\n   */\n  resetTestEnvironment(): void;\n\n  resetTestingModule(): TestBed;\n\n  configureCompiler(config: {providers?: any[]; useJit?: boolean}): void;\n\n  configureTestingModule(moduleDef: TestModuleMetadata): TestBed;\n\n  compileComponents(): Promise<any>;\n\n  inject<T>(\n    token: ProviderToken<T>,\n    notFoundValue: undefined,\n    options: InjectOptions & {\n      optional?: false;\n    },\n  ): T;\n  inject<T>(\n    token: ProviderToken<T>,\n    notFoundValue: null | undefined,\n    options: InjectOptions,\n  ): T | null;\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link https://angular.dev/api/core/EnvironmentInjector#runInContext}\n   */\n  runInInjectionContext<T>(fn: () => T): T;\n\n  execute(tokens: any[], fn: Function, context?: any): any;\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): TestBed;\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): TestBed;\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): TestBed;\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): TestBed;\n\n  overrideTemplate(component: Type<any>, template: string): TestBed;\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(\n    token: any,\n    provider: {useFactory: Function; deps: any[]; multi?: boolean},\n  ): TestBed;\n  overrideProvider(token: any, provider: {useValue: any; multi?: boolean}): TestBed;\n  overrideProvider(\n    token: any,\n    provider: {useFactory?: Function; useValue?: any; deps?: any[]; multi?: boolean},\n  ): TestBed;\n\n  overrideTemplateUsingTestingModule(component: Type<any>, template: string): TestBed;\n\n  createComponent<T>(component: Type<T>): ComponentFixture<T>;\n\n  /**\n   * Execute any pending effects.\n   *\n   * @deprecated use `TestBed.tick()` instead\n   */\n  flushEffects(): void;\n\n  /**\n   * Execute any pending work required to synchronize model to the UI.\n   *\n   * @publicApi 20.0\n   */\n  tick(): void;\n}\n\nlet _nextRootElementId = 0;\n\n/**\n * Returns a singleton of the `TestBed` class.\n *\n * @publicApi\n */\nexport function getTestBed(): TestBed {\n  return TestBedImpl.INSTANCE;\n}\n\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * TestBed is the primary api for writing unit tests for Angular applications and libraries.\n */\nexport class TestBedImpl implements TestBed {\n  private static _INSTANCE: TestBedImpl | null = null;\n\n  static get INSTANCE(): TestBedImpl {\n    return (TestBedImpl._INSTANCE = TestBedImpl._INSTANCE || new TestBedImpl());\n  }\n\n  /**\n   * Teardown options that have been configured at the environment level.\n   * Used as a fallback if no instance-level options have been provided.\n   */\n  private static _environmentTeardownOptions: ModuleTeardownOptions | undefined;\n\n  /**\n   * \"Error on unknown elements\" option that has been configured at the environment level.\n   * Used as a fallback if no instance-level option has been provided.\n   */\n  private static _environmentErrorOnUnknownElementsOption: boolean | undefined;\n\n  /**\n   * \"Error on unknown properties\" option that has been configured at the environment level.\n   * Used as a fallback if no instance-level option has been provided.\n   */\n  private static _environmentErrorOnUnknownPropertiesOption: boolean | undefined;\n\n  /**\n   * Teardown options that have been configured at the `TestBed` instance level.\n   * These options take precedence over the environment-level ones.\n   */\n  private _instanceTeardownOptions: ModuleTeardownOptions | undefined;\n\n  /**\n   * Defer block behavior option that specifies whether defer blocks will be triggered manually\n   * or set to play through.\n   */\n  private _instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n\n  /**\n   * \"Error on unknown elements\" option that has been configured at the `TestBed` instance level.\n   * This option takes precedence over the environment-level one.\n   */\n  private _instanceErrorOnUnknownElementsOption: boolean | undefined;\n\n  /**\n   * \"Error on unknown properties\" option that has been configured at the `TestBed` instance level.\n   * This option takes precedence over the environment-level one.\n   */\n  private _instanceErrorOnUnknownPropertiesOption: boolean | undefined;\n\n  /**\n   * Stores the previous \"Error on unknown elements\" option value,\n   * allowing to restore it in the reset testing module logic.\n   */\n  private _previousErrorOnUnknownElementsOption: boolean | undefined;\n\n  /**\n   * Stores the previous \"Error on unknown properties\" option value,\n   * allowing to restore it in the reset testing module logic.\n   */\n  private _previousErrorOnUnknownPropertiesOption: boolean | undefined;\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  static initTestEnvironment(\n    ngModule: Type<any> | Type<any>[],\n    platform: PlatformRef,\n    options?: TestEnvironmentOptions,\n  ): TestBed {\n    const testBed = TestBedImpl.INSTANCE;\n    testBed.initTestEnvironment(ngModule, platform, options);\n    return testBed;\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  static resetTestEnvironment(): void {\n    TestBedImpl.INSTANCE.resetTestEnvironment();\n  }\n\n  static configureCompiler(config: {providers?: any[]; useJit?: boolean}): TestBed {\n    return TestBedImpl.INSTANCE.configureCompiler(config);\n  }\n\n  /**\n   * Allows overriding default providers, directives, pipes, modules of the test injector,\n   * which are defined in test_injector.js\n   */\n  static configureTestingModule(moduleDef: TestModuleMetadata): TestBed {\n    return TestBedImpl.INSTANCE.configureTestingModule(moduleDef);\n  }\n\n  /**\n   * Compile components with a `templateUrl` for the test's NgModule.\n   * It is necessary to call this function\n   * as fetching urls is asynchronous.\n   */\n  static compileComponents(): Promise<any> {\n    return TestBedImpl.INSTANCE.compileComponents();\n  }\n\n  static overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): TestBed {\n    return TestBedImpl.INSTANCE.overrideModule(ngModule, override);\n  }\n\n  static overrideComponent(component: Type<any>, override: MetadataOverride<Component>): TestBed {\n    return TestBedImpl.INSTANCE.overrideComponent(component, override);\n  }\n\n  static overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): TestBed {\n    return TestBedImpl.INSTANCE.overrideDirective(directive, override);\n  }\n\n  static overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): TestBed {\n    return TestBedImpl.INSTANCE.overridePipe(pipe, override);\n  }\n\n  static overrideTemplate(component: Type<any>, template: string): TestBed {\n    return TestBedImpl.INSTANCE.overrideTemplate(component, template);\n  }\n\n  /**\n   * Overrides the template of the given component, compiling the template\n   * in the context of the TestingModule.\n   *\n   * Note: This works for JIT and AOTed components as well.\n   */\n  static overrideTemplateUsingTestingModule(component: Type<any>, template: string): TestBed {\n    return TestBedImpl.INSTANCE.overrideTemplateUsingTestingModule(component, template);\n  }\n\n  static overrideProvider(\n    token: any,\n    provider: {\n      useFactory: Function;\n      deps: any[];\n    },\n  ): TestBed;\n  static overrideProvider(token: any, provider: {useValue: any}): TestBed;\n  static overrideProvider(\n    token: any,\n    provider: {\n      useFactory?: Function;\n      useValue?: any;\n      deps?: any[];\n    },\n  ): TestBed {\n    return TestBedImpl.INSTANCE.overrideProvider(token, provider);\n  }\n\n  static inject<T>(\n    token: ProviderToken<T>,\n    notFoundValue: undefined,\n    options: InjectOptions & {\n      optional?: false;\n    },\n  ): T;\n  static inject<T>(\n    token: ProviderToken<T>,\n    notFoundValue: null | undefined,\n    options: InjectOptions,\n  ): T | null;\n  static inject<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n  static inject<T>(\n    token: ProviderToken<T>,\n    notFoundValue?: T | null,\n    options?: InjectOptions,\n  ): T | null {\n    return TestBedImpl.INSTANCE.inject(token, notFoundValue, options);\n  }\n\n  /**\n   * Runs the given function in the `EnvironmentInjector` context of `TestBed`.\n   *\n   * @see {@link https://angular.dev/api/core/EnvironmentInjector#runInContext}\n   */\n  static runInInjectionContext<T>(fn: () => T): T {\n    return TestBedImpl.INSTANCE.runInInjectionContext(fn);\n  }\n\n  static createComponent<T>(component: Type<T>): ComponentFixture<T> {\n    return TestBedImpl.INSTANCE.createComponent(component);\n  }\n\n  static resetTestingModule(): TestBed {\n    return TestBedImpl.INSTANCE.resetTestingModule();\n  }\n\n  static execute(tokens: any[], fn: Function, context?: any): any {\n    return TestBedImpl.INSTANCE.execute(tokens, fn, context);\n  }\n\n  static get platform(): PlatformRef {\n    return TestBedImpl.INSTANCE.platform;\n  }\n\n  static get ngModule(): Type<any> | Type<any>[] {\n    return TestBedImpl.INSTANCE.ngModule;\n  }\n\n  static flushEffects(): void {\n    return TestBedImpl.INSTANCE.tick();\n  }\n\n  static tick(): void {\n    return TestBedImpl.INSTANCE.tick();\n  }\n\n  // Properties\n\n  platform: PlatformRef = null!;\n  ngModule: Type<any> | Type<any>[] = null!;\n\n  private _compiler: TestBedCompiler | null = null;\n  private _testModuleRef: NgModuleRef<any> | null = null;\n\n  private _activeFixtures: ComponentFixture<any>[] = [];\n\n  /**\n   * Internal-only flag to indicate whether a module\n   * scoping queue has been checked and flushed already.\n   * @docs-private\n   */\n  globalCompilationChecked = false;\n\n  /**\n   * Initialize the environment for testing with a compiler factory, a PlatformRef, and an\n   * angular module. These are common to every test in the suite.\n   *\n   * This may only be called once, to set up the common providers for the current test\n   * suite on the current platform. If you absolutely need to change the providers,\n   * first use `resetTestEnvironment`.\n   *\n   * Test modules and platforms for individual platforms are available from\n   * '@angular/<platform_name>/testing'.\n   *\n   * @publicApi\n   */\n  initTestEnvironment(\n    ngModule: Type<any> | Type<any>[],\n    platform: PlatformRef,\n    options?: TestEnvironmentOptions,\n  ): void {\n    if (this.platform || this.ngModule) {\n      throw new Error('Cannot set base providers because it has already been called');\n    }\n\n    TestBedImpl._environmentTeardownOptions = options?.teardown;\n\n    TestBedImpl._environmentErrorOnUnknownElementsOption = options?.errorOnUnknownElements;\n\n    TestBedImpl._environmentErrorOnUnknownPropertiesOption = options?.errorOnUnknownProperties;\n\n    this.platform = platform;\n    this.ngModule = ngModule;\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n\n    // TestBed does not have an API which can reliably detect the start of a test, and thus could be\n    // used to track the state of the NgModule registry and reset it correctly. Instead, when we\n    // know we're in a testing scenario, we disable the check for duplicate NgModule registration\n    // completely.\n    setAllowDuplicateNgModuleIdsForTest(true);\n  }\n\n  /**\n   * Reset the providers for the test injector.\n   *\n   * @publicApi\n   */\n  resetTestEnvironment(): void {\n    this.resetTestingModule();\n    this._compiler = null;\n    this.platform = null!;\n    this.ngModule = null!;\n    TestBedImpl._environmentTeardownOptions = undefined;\n    setAllowDuplicateNgModuleIdsForTest(false);\n  }\n\n  resetTestingModule(): this {\n    this.checkGlobalCompilationFinished();\n    resetCompiledComponents();\n    if (this._compiler !== null) {\n      this.compiler.restoreOriginalState();\n    }\n    this._compiler = new TestBedCompiler(this.platform, this.ngModule);\n    // Restore the previous value of the \"error on unknown elements\" option\n    setUnknownElementStrictMode(\n      this._previousErrorOnUnknownElementsOption ?? THROW_ON_UNKNOWN_ELEMENTS_DEFAULT,\n    );\n    // Restore the previous value of the \"error on unknown properties\" option\n    setUnknownPropertyStrictMode(\n      this._previousErrorOnUnknownPropertiesOption ?? THROW_ON_UNKNOWN_PROPERTIES_DEFAULT,\n    );\n\n    // We have to chain a couple of try/finally blocks, because each step can\n    // throw errors and we don't want it to interrupt the next step and we also\n    // want an error to be thrown at the end.\n    try {\n      this.destroyActiveFixtures();\n    } finally {\n      try {\n        if (this.shouldTearDownTestingModule()) {\n          this.tearDownTestingModule();\n        }\n      } finally {\n        this._testModuleRef = null;\n        this._instanceTeardownOptions = undefined;\n        this._instanceErrorOnUnknownElementsOption = undefined;\n        this._instanceErrorOnUnknownPropertiesOption = undefined;\n        this._instanceDeferBlockBehavior = DEFER_BLOCK_DEFAULT_BEHAVIOR;\n      }\n    }\n    return this;\n  }\n\n  configureCompiler(config: {providers?: any[]; useJit?: boolean}): this {\n    if (config.useJit != null) {\n      throw new Error('JIT compiler is not configurable via TestBed APIs.');\n    }\n\n    if (config.providers !== undefined) {\n      this.compiler.setCompilerProviders(config.providers);\n    }\n    return this;\n  }\n\n  configureTestingModule(moduleDef: TestModuleMetadata): this {\n    this.assertNotInstantiated('TestBed.configureTestingModule', 'configure the test module');\n\n    // Trigger module scoping queue flush before executing other TestBed operations in a test.\n    // This is needed for the first test invocation to ensure that globally declared modules have\n    // their components scoped properly. See the `checkGlobalCompilationFinished` function\n    // description for additional info.\n    this.checkGlobalCompilationFinished();\n\n    // Always re-assign the options, even if they're undefined.\n    // This ensures that we don't carry them between tests.\n    this._instanceTeardownOptions = moduleDef.teardown;\n    this._instanceErrorOnUnknownElementsOption = moduleDef.errorOnUnknownElements;\n    this._instanceErrorOnUnknownPropertiesOption = moduleDef.errorOnUnknownProperties;\n    this._instanceDeferBlockBehavior = moduleDef.deferBlockBehavior ?? DEFER_BLOCK_DEFAULT_BEHAVIOR;\n    // Store the current value of the strict mode option,\n    // so we can restore it later\n    this._previousErrorOnUnknownElementsOption = getUnknownElementStrictMode();\n    setUnknownElementStrictMode(this.shouldThrowErrorOnUnknownElements());\n    this._previousErrorOnUnknownPropertiesOption = getUnknownPropertyStrictMode();\n    setUnknownPropertyStrictMode(this.shouldThrowErrorOnUnknownProperties());\n    this.compiler.configureTestingModule(moduleDef);\n    return this;\n  }\n\n  compileComponents(): Promise<any> {\n    return this.compiler.compileComponents();\n  }\n\n  inject<T>(\n    token: ProviderToken<T>,\n    notFoundValue: undefined,\n    options: InjectOptions & {\n      optional: true;\n    },\n  ): T | null;\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n  inject<T>(token: ProviderToken<T>, notFoundValue: null, options?: InjectOptions): T | null;\n  inject<T>(token: ProviderToken<T>, notFoundValue?: T | null, options?: InjectOptions): T | null {\n    if ((token as unknown) === TestBed) {\n      return this as any;\n    }\n    const UNDEFINED = {} as unknown as T;\n    const result = this.testModuleRef.injector.get(token, UNDEFINED, options);\n    return result === UNDEFINED\n      ? (this.compiler.injector.get(token, notFoundValue, options) as any)\n      : result;\n  }\n\n  runInInjectionContext<T>(fn: () => T): T {\n    return runInInjectionContext(this.inject(EnvironmentInjector), fn);\n  }\n\n  execute(tokens: any[], fn: Function, context?: any): any {\n    const params = tokens.map((t) => this.inject(t));\n    return fn.apply(context, params);\n  }\n\n  overrideModule(ngModule: Type<any>, override: MetadataOverride<NgModule>): this {\n    this.assertNotInstantiated('overrideModule', 'override module metadata');\n    this.compiler.overrideModule(ngModule, override);\n    return this;\n  }\n\n  overrideComponent(component: Type<any>, override: MetadataOverride<Component>): this {\n    this.assertNotInstantiated('overrideComponent', 'override component metadata');\n    this.compiler.overrideComponent(component, override);\n    return this;\n  }\n\n  overrideTemplateUsingTestingModule(component: Type<any>, template: string): this {\n    this.assertNotInstantiated(\n      'TestBed.overrideTemplateUsingTestingModule',\n      'Cannot override template when the test module has already been instantiated',\n    );\n    this.compiler.overrideTemplateUsingTestingModule(component, template);\n    return this;\n  }\n\n  overrideDirective(directive: Type<any>, override: MetadataOverride<Directive>): this {\n    this.assertNotInstantiated('overrideDirective', 'override directive metadata');\n    this.compiler.overrideDirective(directive, override);\n    return this;\n  }\n\n  overridePipe(pipe: Type<any>, override: MetadataOverride<Pipe>): this {\n    this.assertNotInstantiated('overridePipe', 'override pipe metadata');\n    this.compiler.overridePipe(pipe, override);\n    return this;\n  }\n\n  /**\n   * Overwrites all providers for the given token with the given provider definition.\n   */\n  overrideProvider(\n    token: any,\n    provider: {useFactory?: Function; useValue?: any; deps?: any[]},\n  ): this {\n    this.assertNotInstantiated('overrideProvider', 'override provider');\n    this.compiler.overrideProvider(token, provider);\n    return this;\n  }\n\n  overrideTemplate(component: Type<any>, template: string): TestBed {\n    return this.overrideComponent(component, {set: {template, templateUrl: null!}});\n  }\n\n  createComponent<T>(type: Type<T>): ComponentFixture<T> {\n    const testComponentRenderer = this.inject(TestComponentRenderer);\n    const rootElId = `root${_nextRootElementId++}`;\n    testComponentRenderer.insertRootElement(rootElId);\n\n    if (getAsyncClassMetadataFn(type)) {\n      throw new Error(\n        `Component '${type.name}' has unresolved metadata. ` +\n          `Please call \\`await TestBed.compileComponents()\\` before running this test.`,\n      );\n    }\n\n    const componentDef = (type as any).ɵcmp;\n\n    if (!componentDef) {\n      throw new Error(`It looks like '${stringify(type)}' has not been compiled.`);\n    }\n\n    const componentFactory = new ComponentFactory(componentDef);\n    const initComponent = () => {\n      const componentRef = componentFactory.create(\n        Injector.NULL,\n        [],\n        `#${rootElId}`,\n        this.testModuleRef,\n      ) as ComponentRef<T>;\n      return this.runInInjectionContext(() => new ComponentFixture(componentRef));\n    };\n    const noNgZone = this.inject(ComponentFixtureNoNgZone, false);\n    const ngZone = noNgZone ? null : this.inject(NgZone, null);\n    const fixture = ngZone ? ngZone.run(initComponent) : initComponent();\n    this._activeFixtures.push(fixture);\n    return fixture;\n  }\n\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  private get compiler(): TestBedCompiler {\n    if (this._compiler === null) {\n      throw new Error(`Need to call TestBed.initTestEnvironment() first`);\n    }\n    return this._compiler;\n  }\n\n  /**\n   * @internal strip this from published d.ts files due to\n   * https://github.com/microsoft/TypeScript/issues/36216\n   */\n  private get testModuleRef(): NgModuleRef<any> {\n    if (this._testModuleRef === null) {\n      this._testModuleRef = this.compiler.finalize();\n    }\n    return this._testModuleRef;\n  }\n\n  private assertNotInstantiated(methodName: string, methodDescription: string) {\n    if (this._testModuleRef !== null) {\n      throw new Error(\n        `Cannot ${methodDescription} when the test module has already been instantiated. ` +\n          `Make sure you are not using \\`inject\\` before \\`${methodName}\\`.`,\n      );\n    }\n  }\n\n  /**\n   * Check whether the module scoping queue should be flushed, and flush it if needed.\n   *\n   * When the TestBed is reset, it clears the JIT module compilation queue, cancelling any\n   * in-progress module compilation. This creates a potential hazard - the very first time the\n   * TestBed is initialized (or if it's reset without being initialized), there may be pending\n   * compilations of modules declared in global scope. These compilations should be finished.\n   *\n   * To ensure that globally declared modules have their components scoped properly, this function\n   * is called whenever TestBed is initialized or reset. The _first_ time that this happens, prior\n   * to any other operations, the scoping queue is flushed.\n   */\n  private checkGlobalCompilationFinished(): void {\n    // Checking _testNgModuleRef is null should not be necessary, but is left in as an additional\n    // guard that compilations queued in tests (after instantiation) are never flushed accidentally.\n    if (!this.globalCompilationChecked && this._testModuleRef === null) {\n      flushModuleScopingQueueAsMuchAsPossible();\n    }\n    this.globalCompilationChecked = true;\n  }\n\n  private destroyActiveFixtures(): void {\n    let errorCount = 0;\n    this._activeFixtures.forEach((fixture) => {\n      try {\n        fixture.destroy();\n      } catch (e) {\n        errorCount++;\n        console.error('Error during cleanup of component', {\n          component: fixture.componentInstance,\n          stacktrace: e,\n        });\n      }\n    });\n    this._activeFixtures = [];\n\n    if (errorCount > 0 && this.shouldRethrowTeardownErrors()) {\n      throw Error(\n        `${errorCount} ${errorCount === 1 ? 'component' : 'components'} ` +\n          `threw errors during cleanup`,\n      );\n    }\n  }\n\n  shouldRethrowTeardownErrors(): boolean {\n    const instanceOptions = this._instanceTeardownOptions;\n    const environmentOptions = TestBedImpl._environmentTeardownOptions;\n\n    // If the new teardown behavior hasn't been configured, preserve the old behavior.\n    if (!instanceOptions && !environmentOptions) {\n      return TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT;\n    }\n\n    // Otherwise use the configured behavior or default to rethrowing.\n    return (\n      instanceOptions?.rethrowErrors ??\n      environmentOptions?.rethrowErrors ??\n      this.shouldTearDownTestingModule()\n    );\n  }\n\n  shouldThrowErrorOnUnknownElements(): boolean {\n    // Check if a configuration has been provided to throw when an unknown element is found\n    return (\n      this._instanceErrorOnUnknownElementsOption ??\n      TestBedImpl._environmentErrorOnUnknownElementsOption ??\n      THROW_ON_UNKNOWN_ELEMENTS_DEFAULT\n    );\n  }\n\n  shouldThrowErrorOnUnknownProperties(): boolean {\n    // Check if a configuration has been provided to throw when an unknown property is found\n    return (\n      this._instanceErrorOnUnknownPropertiesOption ??\n      TestBedImpl._environmentErrorOnUnknownPropertiesOption ??\n      THROW_ON_UNKNOWN_PROPERTIES_DEFAULT\n    );\n  }\n\n  shouldTearDownTestingModule(): boolean {\n    return (\n      this._instanceTeardownOptions?.destroyAfterEach ??\n      TestBedImpl._environmentTeardownOptions?.destroyAfterEach ??\n      TEARDOWN_TESTING_MODULE_ON_DESTROY_DEFAULT\n    );\n  }\n\n  getDeferBlockBehavior(): DeferBlockBehavior {\n    return this._instanceDeferBlockBehavior;\n  }\n\n  tearDownTestingModule() {\n    // If the module ref has already been destroyed, we won't be able to get a test renderer.\n    if (this._testModuleRef === null) {\n      return;\n    }\n    // Resolve the renderer ahead of time, because we want to remove the root elements as the very\n    // last step, but the injector will be destroyed as a part of the module ref destruction.\n    const testRenderer = this.inject(TestComponentRenderer);\n    try {\n      this._testModuleRef.destroy();\n    } catch (e) {\n      if (this.shouldRethrowTeardownErrors()) {\n        throw e;\n      } else {\n        console.error('Error during cleanup of a testing module', {\n          component: this._testModuleRef.instance,\n          stacktrace: e,\n        });\n      }\n    } finally {\n      testRenderer.removeAllRootElements?.();\n    }\n  }\n\n  /**\n   * Execute any pending effects by executing any pending work required to synchronize model to the UI.\n   *\n   * @deprecated use `TestBed.tick()` instead\n   */\n  flushEffects(): void {\n    this.tick();\n  }\n\n  /**\n   * Execute any pending work required to synchronize model to the UI.\n   *\n   * @publicApi\n   */\n  tick(): void {\n    const appRef = this.inject(ApplicationRef);\n    try {\n      // TODO(atscott): ApplicationRef.tick should set includeAllTestViews to true itself rather than doing this here and in ComponentFixture\n      // The behavior should be that TestBed.tick, ComponentFixture.detectChanges, and ApplicationRef.tick all result in the test fixtures\n      // getting synchronized, regardless of whether they are autoDetect: true.\n      // Automatic scheduling (zone or zoneless) will call _tick which will _not_ include fixtures with autoDetect: false\n      (appRef as any).includeAllTestViews = true;\n      appRef.tick();\n    } finally {\n      (appRef as any).includeAllTestViews = false;\n    }\n  }\n}\n\n/**\n * @description\n * Configures and initializes environment for unit testing and provides methods for\n * creating components and services in unit tests.\n *\n * `TestBed` is the primary api for writing unit tests for Angular applications and libraries.\n *\n * @publicApi\n */\nexport const TestBed: TestBedStatic = TestBedImpl;\n\n/**\n * Allows injecting dependencies in `beforeEach()` and `it()`. Note: this function\n * (imported from the `@angular/core/testing` package) can **only** be used to inject dependencies\n * in tests. To inject dependencies in your application code, use the [`inject`](api/core/inject)\n * function from the `@angular/core` package instead.\n *\n * Example:\n *\n * ```ts\n * beforeEach(inject([Dependency, AClass], (dep, object) => {\n *   // some code that uses `dep` and `object`\n *   // ...\n * }));\n *\n * it('...', inject([AClass], (object) => {\n *   object.doSomething();\n *   expect(...);\n * })\n * ```\n *\n * @publicApi\n */\nexport function inject(tokens: any[], fn: Function): () => any {\n  const testBed = TestBedImpl.INSTANCE;\n  // Not using an arrow function to preserve context passed from call site\n  return function (this: unknown) {\n    return testBed.execute(tokens, fn, this);\n  };\n}\n\n/**\n * @publicApi\n */\nexport class InjectSetupWrapper {\n  constructor(private _moduleDef: () => TestModuleMetadata) {}\n\n  private _addModule() {\n    const moduleDef = this._moduleDef();\n    if (moduleDef) {\n      TestBedImpl.configureTestingModule(moduleDef);\n    }\n  }\n\n  inject(tokens: any[], fn: Function): () => any {\n    const self = this;\n    // Not using an arrow function to preserve context passed from call site\n    return function (this: unknown) {\n      self._addModule();\n      return inject(tokens, fn).call(this);\n    };\n  }\n}\n\n/**\n * @publicApi\n */\nexport function withModule(moduleDef: TestModuleMetadata): InjectSetupWrapper;\nexport function withModule(moduleDef: TestModuleMetadata, fn: Function): () => any;\nexport function withModule(\n  moduleDef: TestModuleMetadata,\n  fn?: Function | null,\n): (() => any) | InjectSetupWrapper {\n  if (fn) {\n    // Not using an arrow function to preserve context passed from call site\n    return function (this: unknown) {\n      const testBed = TestBedImpl.INSTANCE;\n      if (moduleDef) {\n        testBed.configureTestingModule(moduleDef);\n      }\n      return fn.apply(this);\n    };\n  }\n  return new InjectSetupWrapper(() => moduleDef);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  NavigationNavigateOptions,\n  NavigationTypeString,\n  NavigationOptions,\n  NavigateEvent,\n  NavigationCurrentEntryChangeEvent,\n  NavigationTransition,\n  NavigationUpdateCurrentEntryOptions,\n  NavigationReloadOptions,\n  NavigationResult,\n  NavigationHistoryEntry,\n  NavigationInterceptOptions,\n  NavigationDestination,\n  Navigation,\n} from '../src/navigation_types';\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nexport class FakeNavigation implements Navigation {\n  /**\n   * The fake implementation of an entries array. Only same-document entries\n   * allowed.\n   */\n  private readonly entriesArr: FakeNavigationHistoryEntry[] = [];\n\n  /**\n   * The current active entry index into `entriesArr`.\n   */\n  private currentEntryIndex = 0;\n\n  /**\n   * The current navigate event.\n   * @internal\n   */\n  navigateEvent: InternalFakeNavigateEvent | null = null;\n\n  /**\n   * A Map of pending traversals, so that traversals to the same entry can be\n   * re-used.\n   */\n  private readonly traversalQueue = new Map<string, InternalNavigationResult>();\n\n  /**\n   * A Promise that resolves when the previous traversals have finished. Used to\n   * simulate the cross-process communication necessary for traversals.\n   */\n  private nextTraversal = Promise.resolve();\n\n  /**\n   * A prospective current active entry index, which includes unresolved\n   * traversals. Used by `go` to determine where navigations are intended to go.\n   */\n  private prospectiveEntryIndex = 0;\n\n  /**\n   * A test-only option to make traversals synchronous, rather than emulate\n   * cross-process communication.\n   */\n  private synchronousTraversals = false;\n\n  /** Whether to allow a call to setInitialEntryForTesting. */\n  private canSetInitialEntry = true;\n\n  /**\n   * `EventTarget` to dispatch events.\n   * @internal\n   */\n  eventTarget: EventTarget;\n\n  /** The next unique id for created entries. Replace recreates this id. */\n  private nextId = 0;\n\n  /** The next unique key for created entries. Replace inherits this id. */\n  private nextKey = 0;\n\n  /** Whether this fake is disposed. */\n  private disposed = false;\n\n  /** Equivalent to `navigation.currentEntry`. */\n  get currentEntry(): FakeNavigationHistoryEntry {\n    return this.entriesArr[this.currentEntryIndex];\n  }\n\n  get canGoBack(): boolean {\n    return this.currentEntryIndex > 0;\n  }\n\n  get canGoForward(): boolean {\n    return this.currentEntryIndex < this.entriesArr.length - 1;\n  }\n\n  private readonly createEventTarget: () => EventTarget;\n  private readonly _window: Pick<\n    Window,\n    'addEventListener' | 'removeEventListener' | 'dispatchEvent'\n  >;\n  get window(): Pick<Window, 'addEventListener' | 'removeEventListener'> {\n    return this._window;\n  }\n\n  constructor(doc: Document, startURL: `http${string}`) {\n    this.createEventTarget = () => {\n      try {\n        // `document.createElement` because NodeJS `EventTarget` is\n        // incompatible with Domino's `Event`. That is, attempting to\n        // dispatch an event created by Domino's patched `Event` will\n        // throw an error since it is not an instance of a real Node\n        // `Event`.\n        return doc.createElement('div');\n      } catch {\n        // Fallback to a basic EventTarget if `document.createElement`\n        // fails. This can happen with tests that pass in a value for document\n        // that is stubbed.\n        return new EventTarget();\n      }\n    };\n    this._window = document.defaultView ?? this.createEventTarget();\n    this.eventTarget = this.createEventTarget();\n    // First entry.\n    this.setInitialEntryForTesting(startURL);\n  }\n\n  /**\n   * Sets the initial entry.\n   */\n  setInitialEntryForTesting(\n    url: `http${string}`,\n    options: {historyState: unknown; state?: unknown} = {historyState: null},\n  ): void {\n    if (!this.canSetInitialEntry) {\n      throw new Error(\n        'setInitialEntryForTesting can only be called before any ' + 'navigation has occurred',\n      );\n    }\n    const currentInitialEntry = this.entriesArr[0];\n    this.entriesArr[0] = new FakeNavigationHistoryEntry(this.eventTarget, new URL(url).toString(), {\n      index: 0,\n      key: currentInitialEntry?.key ?? String(this.nextKey++),\n      id: currentInitialEntry?.id ?? String(this.nextId++),\n      sameDocument: true,\n      historyState: options?.historyState,\n      state: options.state,\n    });\n  }\n\n  /** Returns whether the initial entry is still eligible to be set. */\n  canSetInitialEntryForTesting(): boolean {\n    return this.canSetInitialEntry;\n  }\n\n  /**\n   * Sets whether to emulate traversals as synchronous rather than\n   * asynchronous.\n   */\n  setSynchronousTraversalsForTesting(synchronousTraversals: boolean): void {\n    this.synchronousTraversals = synchronousTraversals;\n  }\n\n  /** Equivalent to `navigation.entries()`. */\n  entries(): FakeNavigationHistoryEntry[] {\n    return this.entriesArr.slice();\n  }\n\n  /** Equivalent to `navigation.navigate()`. */\n  navigate(url: string, options?: NavigationNavigateOptions): FakeNavigationResult {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const toUrl = new URL(url, this.currentEntry.url!);\n\n    let navigationType: NavigationTypeString;\n    if (!options?.history || options.history === 'auto') {\n      // Auto defaults to push, but if the URLs are the same, is a replace.\n      if (fromUrl.toString() === toUrl.toString()) {\n        navigationType = 'replace';\n      } else {\n        navigationType = 'push';\n      }\n    } else {\n      navigationType = options.history;\n    }\n\n    const hashChange = isHashChange(fromUrl, toUrl);\n\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      state: options?.state,\n      sameDocument: hashChange,\n      historyState: null,\n    });\n    const result = new InternalNavigationResult(this);\n\n    const intercepted = this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for navigate().\n      userInitiated: false,\n      hashChange,\n      info: options?.info,\n    });\n    if (!intercepted) {\n      this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent!);\n    }\n\n    return {\n      committed: result.committed,\n      finished: result.finished,\n    };\n  }\n\n  /** Equivalent to `history.pushState()`. */\n  pushState(data: unknown, title: string, url?: string): void {\n    this.pushOrReplaceState('push', data, title, url);\n  }\n\n  /** Equivalent to `history.replaceState()`. */\n  replaceState(data: unknown, title: string, url?: string): void {\n    this.pushOrReplaceState('replace', data, title, url);\n  }\n\n  private pushOrReplaceState(\n    navigationType: NavigationTypeString,\n    data: unknown,\n    _title: string,\n    url?: string,\n  ): void {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const toUrl = url ? new URL(url, this.currentEntry.url!) : fromUrl;\n\n    const hashChange = isHashChange(fromUrl, toUrl);\n\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      sameDocument: true, // history.pushState/replaceState are always same-document\n      historyState: data,\n      state: undefined, // No Navigation API state directly from history.pushState\n    });\n    const result = new InternalNavigationResult(this);\n\n    const intercepted = this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for pushState() or replaceState().\n      userInitiated: false,\n      hashChange,\n    });\n    if (intercepted) {\n      return;\n    }\n    this.updateNavigationEntriesForSameDocumentNavigation(this.navigateEvent!);\n  }\n\n  /** Equivalent to `navigation.traverseTo()`. */\n  traverseTo(key: string, options?: NavigationOptions): FakeNavigationResult {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const entry = this.findEntry(key);\n    if (!entry) {\n      const domException = new DOMException('Invalid key', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    if (entry === this.currentEntry) {\n      return {\n        committed: Promise.resolve(this.currentEntry),\n        finished: Promise.resolve(this.currentEntry),\n      };\n    }\n    if (this.traversalQueue.has(entry.key)) {\n      const existingResult = this.traversalQueue.get(entry.key)!;\n      return {\n        committed: existingResult.committed,\n        finished: existingResult.finished,\n      };\n    }\n\n    const hashChange = isHashChange(fromUrl, new URL(entry.url!, this.currentEntry.url!));\n    const destination = new FakeNavigationDestination({\n      url: entry.url!,\n      state: entry.getState(),\n      historyState: entry.getHistoryState(),\n      key: entry.key,\n      id: entry.id,\n      index: entry.index,\n      sameDocument: entry.sameDocument,\n    });\n    this.prospectiveEntryIndex = entry.index;\n    const result = new InternalNavigationResult(this);\n    this.traversalQueue.set(entry.key, result);\n    this.runTraversal(() => {\n      this.traversalQueue.delete(entry.key);\n      const intercepted = this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for traverseTo().\n        userInitiated: false,\n        hashChange,\n        info: options?.info,\n      });\n      if (!intercepted) {\n        this.userAgentTraverse(this.navigateEvent!);\n      }\n    });\n    return {\n      committed: result.committed,\n      finished: result.finished,\n    };\n  }\n\n  /** Equivalent to `navigation.back()`. */\n  back(options?: NavigationOptions): FakeNavigationResult {\n    if (this.currentEntryIndex === 0) {\n      const domException = new DOMException('Cannot go back', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex - 1];\n    return this.traverseTo(entry.key, options);\n  }\n\n  /** Equivalent to `navigation.forward()`. */\n  forward(options?: NavigationOptions): FakeNavigationResult {\n    if (this.currentEntryIndex === this.entriesArr.length - 1) {\n      const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex + 1];\n    return this.traverseTo(entry.key, options);\n  }\n\n  /**\n   * Equivalent to `history.go()`.\n   * Note that this method does not actually work precisely to how Chrome\n   * does, instead choosing a simpler model with less unexpected behavior.\n   * Chrome has a few edge case optimizations, for instance with repeated\n   * `back(); forward()` chains it collapses certain traversals.\n   */\n  go(direction: number): void {\n    const targetIndex = this.prospectiveEntryIndex + direction;\n    if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n      return;\n    }\n    this.prospectiveEntryIndex = targetIndex;\n    this.runTraversal(() => {\n      // Check again that destination is in the entries array.\n      if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n        return;\n      }\n      const fromUrl = new URL(this.currentEntry.url!);\n      const entry = this.entriesArr[targetIndex];\n      const hashChange = isHashChange(fromUrl, new URL(entry.url!, this.currentEntry.url!));\n      const destination = new FakeNavigationDestination({\n        url: entry.url!,\n        state: entry.getState(),\n        historyState: entry.getHistoryState(),\n        key: entry.key,\n        id: entry.id,\n        index: entry.index,\n        sameDocument: entry.sameDocument,\n      });\n      const result = new InternalNavigationResult(this);\n      const intercepted = this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for go().\n        userInitiated: false,\n        hashChange,\n      });\n      if (!intercepted) {\n        this.userAgentTraverse(this.navigateEvent!);\n      }\n    });\n  }\n\n  /** Runs a traversal synchronously or asynchronously */\n  private runTraversal(traversal: () => void) {\n    if (this.synchronousTraversals) {\n      traversal();\n      return;\n    }\n\n    // Each traversal occupies a single timeout resolution.\n    // This means that Promises added to commit and finish should resolve\n    // before the next traversal.\n    this.nextTraversal = this.nextTraversal.then(() => {\n      return new Promise<void>((resolve) => {\n        setTimeout(() => {\n          resolve();\n          traversal();\n        });\n      });\n    });\n  }\n\n  /** Equivalent to `navigation.addEventListener()`. */\n  addEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: AddEventListenerOptions | boolean,\n  ): void {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n\n  /** Equivalent to `navigation.removeEventListener()`. */\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: EventListenerOptions | boolean,\n  ): void {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n\n  /** Equivalent to `navigation.dispatchEvent()` */\n  dispatchEvent(event: Event): boolean {\n    return this.eventTarget.dispatchEvent(event);\n  }\n\n  /** Cleans up resources. */\n  dispose(): void {\n    // Recreate eventTarget to release current listeners.\n    this.eventTarget = this.createEventTarget();\n    this.disposed = true;\n  }\n\n  /** Returns whether this fake is disposed. */\n  isDisposed(): boolean {\n    return this.disposed;\n  }\n\n  abortOngoingNavigation(eventToAbort: InternalFakeNavigateEvent, reason?: Error) {\n    if (this.navigateEvent !== eventToAbort) {\n      return;\n    }\n    if (this.navigateEvent.abortController.signal.aborted) {\n      return;\n    }\n    const abortReason = reason ?? new DOMException('Navigation aborted', 'AbortError');\n    this.navigateEvent.cancel(abortReason);\n  }\n\n  /**\n   * Implementation for all navigations and traversals.\n   * @returns true if the event was intercepted, otherwise false\n   */\n  private userAgentNavigate(\n    destination: FakeNavigationDestination,\n    result: InternalNavigationResult,\n    options: InternalNavigateOptions,\n  ): boolean {\n    // The first navigation should disallow any future calls to set the initial\n    // entry.\n    this.canSetInitialEntry = false;\n    if (this.navigateEvent) {\n      this.abortOngoingNavigation(\n        this.navigateEvent,\n        new DOMException('Navigation superseded by a new navigation.', 'AbortError'),\n      );\n    }\n    // TODO(atscott): Disposing doesn't really do much because new requests are still processed\n    // if (this.disposed) {\n    //   return false;\n    // }\n    const dispatchResultIsTrueIfNoInterception = dispatchNavigateEvent({\n      navigationType: options.navigationType,\n      cancelable: options.cancelable,\n      canIntercept: options.canIntercept,\n      userInitiated: options.userInitiated,\n      hashChange: options.hashChange,\n      destination,\n      info: options.info,\n      sameDocument: destination.sameDocument,\n      result,\n    });\n    return !dispatchResultIsTrueIfNoInterception;\n  }\n\n  /**\n   * Implementation for a push or replace navigation.\n   * https://whatpr.org/html/10919/browsing-the-web.html#url-and-history-update-steps\n   * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  urlAndHistoryUpdateSteps(navigateEvent: InternalFakeNavigateEvent) {\n    this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n  }\n\n  /**\n   * Implementation for a traverse navigation.\n   *\n   * https://whatpr.org/html/10919/browsing-the-web.html#apply-the-traverse-history-step\n   * ...\n   * > Let updateDocument be an algorithm step which performs update document for history step application given targetEntry's document, targetEntry, changingNavigableContinuation's update-only, scriptHistoryLength, scriptHistoryIndex, navigationType, entriesForNavigationAPI, and previousEntry.\n   * > If targetEntry's document is equal to displayedDocument, then perform updateDocument.\n   * https://whatpr.org/html/10919/browsing-the-web.html#update-document-for-history-step-application\n   * which then goes to https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  userAgentTraverse(navigateEvent: InternalFakeNavigateEvent) {\n    const oldUrl = this.currentEntry.url!;\n    this.updateNavigationEntriesForSameDocumentNavigation(navigateEvent);\n    // Happens as part of \"updating the document\" steps https://whatpr.org/html/10919/browsing-the-web.html#updating-the-document\n    const popStateEvent = createPopStateEvent({\n      state: navigateEvent.destination.getHistoryState(),\n    });\n    this._window.dispatchEvent(popStateEvent);\n    if (navigateEvent.hashChange) {\n      const hashchangeEvent = createHashChangeEvent(oldUrl, this.currentEntry.url!);\n      this._window.dispatchEvent(hashchangeEvent);\n    }\n  }\n\n  /**\n   * https://whatpr.org/html/10919/nav-history-apis.html#update-the-navigation-api-entries-for-a-same-document-navigation\n   * @internal\n   */\n  updateNavigationEntriesForSameDocumentNavigation({\n    destination,\n    navigationType,\n    result,\n  }: InternalFakeNavigateEvent) {\n    const oldCurrentNHE = this.currentEntry;\n    const disposedNHEs = [];\n    if (navigationType === 'traverse') {\n      this.currentEntryIndex = destination.index;\n      if (this.currentEntryIndex === -1) {\n        throw new Error('unexpected current entry index');\n      }\n    } else if (navigationType === 'push') {\n      this.currentEntryIndex++;\n      this.prospectiveEntryIndex = this.currentEntryIndex; // prospectiveEntryIndex isn't in the spec but is an implementation detail\n      disposedNHEs.push(...this.entriesArr.splice(this.currentEntryIndex));\n    } else if (navigationType === 'replace') {\n      disposedNHEs.push(oldCurrentNHE);\n    }\n    if (navigationType === 'push' || navigationType === 'replace') {\n      const index = this.currentEntryIndex;\n      const key =\n        navigationType === 'push'\n          ? String(this.nextKey++)\n          : (oldCurrentNHE?.key ?? String(this.nextKey++));\n      const newNHE = new FakeNavigationHistoryEntry(this.eventTarget, destination.url, {\n        id: String(this.nextId++),\n        key,\n        index,\n        sameDocument: true,\n        state: destination.getState(),\n        historyState: destination.getHistoryState(),\n      });\n      this.entriesArr[this.currentEntryIndex] = newNHE;\n    }\n    result.committedResolve(this.currentEntry);\n    const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n      from: oldCurrentNHE,\n      navigationType: navigationType,\n    });\n    this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n    for (const disposedNHE of disposedNHEs) {\n      disposedNHE.dispose();\n    }\n  }\n\n  /** Utility method for finding entries with the given `key`. */\n  private findEntry(key: string) {\n    for (const entry of this.entriesArr) {\n      if (entry.key === key) return entry;\n    }\n    return undefined;\n  }\n\n  set onnavigate(\n    // tslint:disable-next-line:no-any\n    _handler: ((this: Navigation, ev: NavigateEvent) => any) | null,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  // tslint:disable-next-line:no-any\n  get onnavigate(): ((this: Navigation, ev: NavigateEvent) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  set oncurrententrychange(\n    _handler: // tslint:disable-next-line:no-any\n    ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any) | null,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  get oncurrententrychange(): // tslint:disable-next-line:no-any\n  ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  set onnavigatesuccess(\n    // tslint:disable-next-line:no-any\n    _handler: ((this: Navigation, ev: Event) => any) | null,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  // tslint:disable-next-line:no-any\n  get onnavigatesuccess(): ((this: Navigation, ev: Event) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  set onnavigateerror(\n    // tslint:disable-next-line:no-any\n    _handler: ((this: Navigation, ev: ErrorEvent) => any) | null,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  // tslint:disable-next-line:no-any\n  get onnavigateerror(): ((this: Navigation, ev: ErrorEvent) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  private _transition: NavigationTransition | null = null;\n  /** @internal */\n  set transition(t: NavigationTransition | null) {\n    this._transition = t;\n  }\n  get transition(): NavigationTransition | null {\n    return this._transition;\n  }\n\n  updateCurrentEntry(_options: NavigationUpdateCurrentEntryOptions): void {\n    throw new Error('unimplemented');\n  }\n\n  reload(_options?: NavigationReloadOptions): NavigationResult {\n    throw new Error('unimplemented');\n  }\n}\n\n/**\n * Fake equivalent of the `NavigationResult` interface with\n * `FakeNavigationHistoryEntry`.\n */\ninterface FakeNavigationResult extends NavigationResult {\n  readonly committed: Promise<FakeNavigationHistoryEntry>;\n  readonly finished: Promise<FakeNavigationHistoryEntry>;\n}\n\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nexport class FakeNavigationHistoryEntry implements NavigationHistoryEntry {\n  readonly sameDocument: boolean;\n\n  readonly id: string;\n  readonly key: string;\n  readonly index: number;\n  private readonly state: unknown;\n  private readonly historyState: unknown;\n\n  // tslint:disable-next-line:no-any\n  ondispose: ((this: NavigationHistoryEntry, ev: Event) => any) | null = null;\n\n  constructor(\n    private eventTarget: EventTarget,\n    readonly url: string | null,\n    {\n      id,\n      key,\n      index,\n      sameDocument,\n      state,\n      historyState,\n    }: {\n      id: string;\n      key: string;\n      index: number;\n      sameDocument: boolean;\n      historyState: unknown;\n      state?: unknown;\n    },\n  ) {\n    this.id = id;\n    this.key = key;\n    this.index = index;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n  }\n\n  getState(): unknown {\n    // Budget copy.\n    return this.state ? (JSON.parse(JSON.stringify(this.state)) as unknown) : this.state;\n  }\n\n  getHistoryState(): unknown {\n    // Budget copy.\n    return this.historyState\n      ? (JSON.parse(JSON.stringify(this.historyState)) as unknown)\n      : this.historyState;\n  }\n\n  addEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: AddEventListenerOptions | boolean,\n  ): void {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: EventListenerOptions | boolean,\n  ): void {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n\n  dispatchEvent(event: Event): boolean {\n    return this.eventTarget.dispatchEvent(event);\n  }\n\n  /** internal */\n  dispose() {\n    const disposeEvent = new Event('disposed');\n    this.dispatchEvent(disposeEvent);\n    // release current listeners\n    this.eventTarget = null!;\n  }\n}\n\n/** `NavigationInterceptOptions` with experimental commit option. */\nexport interface ExperimentalNavigationInterceptOptions extends NavigationInterceptOptions {\n  precommitHandler?: (controller: NavigationPrecommitController) => Promise<void>;\n}\n\nexport interface NavigationPrecommitController {\n  redirect: (url: string, options?: NavigationNavigateOptions) => void;\n}\n\nexport interface ExperimentalNavigateEvent extends NavigateEvent {\n  intercept(options?: ExperimentalNavigationInterceptOptions): void;\n\n  precommitHandler?: () => Promise<void>;\n}\n\n/**\n * Fake equivalent of `NavigateEvent`.\n */\nexport interface FakeNavigateEvent extends ExperimentalNavigateEvent {\n  readonly destination: FakeNavigationDestination;\n}\n\ninterface InternalFakeNavigateEvent extends FakeNavigateEvent {\n  readonly sameDocument: boolean;\n  readonly result: InternalNavigationResult;\n  interceptionState: 'none' | 'intercepted' | 'committed' | 'scrolled' | 'finished';\n  scrollBehavior: 'after-transition' | 'manual' | null;\n  focusResetBehavior: 'after-transition' | 'manual' | null;\n\n  abortController: AbortController;\n  cancel(reason: Error): void;\n}\n\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n *\n * https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing\n */\nfunction dispatchNavigateEvent({\n  cancelable,\n  canIntercept,\n  userInitiated,\n  hashChange,\n  navigationType,\n  destination,\n  info,\n  sameDocument,\n  result,\n}: {\n  cancelable: boolean;\n  canIntercept: boolean;\n  userInitiated: boolean;\n  hashChange: boolean;\n  navigationType: NavigationTypeString;\n  destination: FakeNavigationDestination;\n  info: unknown;\n  sameDocument: boolean;\n  result: InternalNavigationResult;\n}) {\n  const {navigation} = result;\n\n  const eventAbortController = new AbortController();\n  const event = new Event('navigate', {bubbles: false, cancelable}) as {\n    -readonly [P in keyof InternalFakeNavigateEvent]: InternalFakeNavigateEvent[P];\n  };\n\n  event.navigationType = navigationType;\n  event.destination = destination;\n  event.canIntercept = canIntercept;\n  event.userInitiated = userInitiated;\n  event.hashChange = hashChange;\n  event.signal = eventAbortController.signal;\n  event.abortController = eventAbortController;\n  event.info = info;\n  event.focusResetBehavior = null;\n  event.scrollBehavior = null;\n  event.interceptionState = 'none';\n  event.downloadRequest = null;\n  event.formData = null;\n  event.result = result;\n  event.sameDocument = sameDocument;\n\n  let precommitHandlers: Array<(controller: NavigationPrecommitController) => Promise<void>> = [];\n  let handlers: Array<() => Promise<void>> = [];\n\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-intercept\n  event.intercept = function (\n    this: InternalFakeNavigateEvent,\n    options?: ExperimentalNavigationInterceptOptions,\n  ): void {\n    if (!this.canIntercept) {\n      throw new DOMException(`Cannot intercept when canIntercept is 'false'`, 'SecurityError');\n    }\n    this.interceptionState = 'intercepted';\n    event.sameDocument = true;\n    const precommitHandler = options?.precommitHandler;\n    if (precommitHandler) {\n      if (!this.cancelable) {\n        throw new DOMException(\n          `Cannot use precommitHandler when cancelable is 'false'`,\n          'InvalidStateError',\n        );\n      }\n      precommitHandlers.push(precommitHandler);\n    }\n    if (event.interceptionState !== 'none' && event.interceptionState !== 'intercepted') {\n      throw new Error('Event interceptionState should be \"none\" or \"intercepted\"');\n    }\n    event.interceptionState = 'intercepted';\n    const handler = options?.handler;\n    if (handler) {\n      handlers.push(handler);\n    }\n    // override old options with new ones. UA _may_ report a console warning if new options differ from previous\n    event.focusResetBehavior = options?.focusReset ?? event.focusResetBehavior;\n    event.scrollBehavior = options?.scroll ?? event.scrollBehavior;\n  };\n\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigateevent-scroll\n  event.scroll = function (this: InternalFakeNavigateEvent): void {\n    if (event.interceptionState !== 'committed') {\n      throw new DOMException(\n        `Failed to execute 'scroll' on 'NavigateEvent': scroll() must be ` +\n          `called after commit() and interception options must specify manual scroll.`,\n        'InvalidStateError',\n      );\n    }\n    processScrollBehavior(event);\n  };\n\n  // https://whatpr.org/html/10919/nav-history-apis.html#dom-navigationprecommitcontroller-redirect\n  function redirect(url: string, options: NavigationNavigateOptions = {}) {\n    if (event.interceptionState === 'none') {\n      throw new Error('cannot redirect when event is not intercepted');\n    }\n    if (event.interceptionState !== 'intercepted') {\n      throw new DOMException(\n        `cannot redirect when event is not in 'intercepted' state`,\n        'InvalidStateError',\n      );\n    }\n    if (event.navigationType !== 'push' && event.navigationType !== 'replace') {\n      throw new DOMException(\n        `cannot redirect when navigationType is not 'push' or 'replace`,\n        'InvalidStateError',\n      );\n    }\n    const destinationUrl = new URL(url, navigation.currentEntry.url!);\n    if (options.history === 'push' || options.history === 'replace') {\n      event.navigationType = options.history;\n    }\n    if (options.hasOwnProperty('state')) {\n      event.destination.state = options.state;\n    }\n    event.destination.url = destinationUrl.href;\n    if (options.hasOwnProperty('info')) {\n      event.info = options.info;\n    }\n  }\n\n  // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n  // \"Let commit be the following steps:\"\n  function commit() {\n    if (result.signal.aborted) {\n      return;\n    }\n    (navigation.transition as InternalNavigationTransition)?.committedResolve();\n    if (event.interceptionState === 'intercepted') {\n      event.interceptionState = 'committed';\n      switch (event.navigationType) {\n        case 'push':\n        case 'replace': {\n          navigation.urlAndHistoryUpdateSteps(event);\n          break;\n        }\n        case 'reload': {\n          navigation.updateNavigationEntriesForSameDocumentNavigation(event);\n          break;\n        }\n        case 'traverse': {\n          navigation.userAgentTraverse(event);\n          break;\n        }\n      }\n    }\n    const promisesList = handlers.map((handler) => handler());\n    if (promisesList.length === 0) {\n      promisesList.push(Promise.resolve());\n    }\n    Promise.all(promisesList)\n      .then(() => {\n        // Follows steps outlined under \"Wait for all of promisesList, with the following success steps:\"\n        // in the spec https://html.spec.whatwg.org/multipage/nav-history-apis.html#navigate-event-firing.\n        if (result.signal.aborted) {\n          return;\n        }\n        if (event !== navigation.navigateEvent) {\n          if (!result.signal.aborted && result.committedTo) {\n            result.finishedReject(\n              new DOMException('Navigation superseded before handler completion', 'AbortError'),\n            );\n          }\n          return;\n        }\n        navigation.navigateEvent = null;\n        finishNavigationEvent(event, true);\n        const navigatesuccessEvent = new Event('navigatesuccess', {\n          bubbles: false,\n          cancelable: false,\n        });\n        navigation.eventTarget.dispatchEvent(navigatesuccessEvent);\n        result.finishedResolve();\n        (navigation.transition as InternalNavigationTransition)?.finishedResolve();\n        navigation.transition = null;\n      })\n      .catch((reason) => {\n        if (!event.abortController.signal.aborted) {\n          event.cancel(reason);\n        }\n      });\n  }\n\n  // Internal only.\n  // https://whatpr.org/html/10919/nav-history-apis.html#inner-navigate-event-firing-algorithm\n  // \"Let cancel be the following steps given reason\"\n  event.cancel = function (this: InternalFakeNavigateEvent, reason: Error) {\n    if (result.signal.aborted) {\n      return;\n    }\n    this.abortController.abort(reason);\n    const isCurrentGlobalNavigationEvent = this === navigation.navigateEvent;\n    if (isCurrentGlobalNavigationEvent) {\n      navigation.navigateEvent = null;\n    }\n    if (this.interceptionState !== 'intercepted' && this.interceptionState !== 'finished') {\n      finishNavigationEvent(this, false);\n    } else if (this.interceptionState === 'intercepted') {\n      this.interceptionState = 'finished';\n    }\n    const navigateerrorEvent = new Event('navigateerror', {\n      bubbles: false,\n      cancelable,\n    }) as ErrorEvent;\n    (navigateerrorEvent as unknown as {error: Error}).error = reason;\n    navigation.eventTarget.dispatchEvent(navigateerrorEvent);\n    if (result.committedTo === null && !result.signal.aborted) {\n      result.committedReject(reason);\n    }\n    result.finishedReject(reason);\n    const transition = navigation.transition as InternalNavigationTransition | undefined;\n    transition?.committedReject(reason);\n    transition?.finishedReject(reason);\n    navigation.transition = null;\n  };\n\n  function dispatch() {\n    navigation.navigateEvent = event;\n    const dispatchResult = navigation.eventTarget.dispatchEvent(event);\n\n    if (event.interceptionState === 'intercepted') {\n      if (!navigation.currentEntry) {\n        event.cancel(\n          new DOMException(\n            'Cannot create transition without a currentEntry for intercepted navigation.',\n            'InvalidStateError',\n          ),\n        );\n        return;\n      }\n      const transition = new InternalNavigationTransition(navigation.currentEntry, navigationType);\n      navigation.transition = transition;\n      // Mark transition.finished as handled (Spec Step 33.4)\n      transition.finished.catch(() => {});\n      transition.committed.catch(() => {});\n    }\n    if (!dispatchResult && event.cancelable) {\n      if (!event.abortController.signal.aborted) {\n        event.cancel(\n          new DOMException('Navigation prevented by event.preventDefault()', 'AbortError'),\n        );\n      }\n    } else {\n      if (precommitHandlers.length === 0) {\n        commit();\n      } else {\n        const precommitController: NavigationPrecommitController = {redirect};\n        const precommitPromisesList = precommitHandlers.map((handler) => {\n          let p: Promise<void>;\n          try {\n            p = handler(precommitController);\n          } catch (e) {\n            p = Promise.reject(e);\n          }\n          p.catch(() => {});\n          return p;\n        });\n        Promise.all(precommitPromisesList)\n          .then(() => commit())\n          .catch((reason: Error) => {\n            if (event.abortController.signal.aborted) {\n              return;\n            }\n            if (navigation.transition) {\n              (navigation.transition as InternalNavigationTransition).committedReject(reason);\n            }\n            event.cancel(reason);\n          });\n      }\n    }\n  }\n\n  dispatch();\n  return event.interceptionState === 'none';\n}\n\n/** https://whatpr.org/html/10919/nav-history-apis.html#navigateevent-finish */\nfunction finishNavigationEvent(event: InternalFakeNavigateEvent, didFulfill: boolean) {\n  if (event.interceptionState === 'finished') {\n    throw new Error('Attempting to finish navigation event that was already finished');\n  }\n  if (event.interceptionState === 'intercepted') {\n    if (didFulfill === true) {\n      throw new Error('didFulfill should be false');\n    }\n    event.interceptionState = 'finished';\n    return;\n  }\n  if (event.interceptionState === 'none') {\n    return;\n  }\n  potentiallyResetFocus(event);\n  if (didFulfill) {\n    potentiallyResetScroll(event);\n  }\n  event.interceptionState = 'finished';\n}\n\n/** https://whatpr.org/html/10919/nav-history-apis.html#potentially-reset-the-focus */\nfunction potentiallyResetFocus(event: InternalFakeNavigateEvent) {\n  if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n    throw new Error('cannot reset focus if navigation event is not committed or scrolled');\n  }\n  if (event.focusResetBehavior === 'manual') {\n    return;\n  }\n  // TODO(atscott): the rest of the steps\n}\n\nfunction potentiallyResetScroll(event: InternalFakeNavigateEvent) {\n  if (event.interceptionState !== 'committed' && event.interceptionState !== 'scrolled') {\n    throw new Error('cannot reset scroll if navigation event is not committed or scrolled');\n  }\n  if (event.interceptionState === 'scrolled' || event.scrollBehavior === 'manual') {\n    return;\n  }\n  processScrollBehavior(event);\n}\n\n/* https://whatpr.org/html/10919/nav-history-apis.html#process-scroll-behavior */\nfunction processScrollBehavior(event: InternalFakeNavigateEvent) {\n  if (event.interceptionState !== 'committed') {\n    throw new Error('invalid event interception state when processing scroll behavior');\n  }\n  event.interceptionState = 'scrolled';\n  // TODO(atscott): the rest of the steps\n}\n\n/** Fake equivalent of `NavigationCurrentEntryChangeEvent`. */\nexport interface FakeNavigationCurrentEntryChangeEvent extends NavigationCurrentEntryChangeEvent {\n  readonly from: FakeNavigationHistoryEntry;\n}\n\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({\n  from,\n  navigationType,\n}: {\n  from: FakeNavigationHistoryEntry;\n  navigationType: NavigationTypeString;\n}) {\n  const event = new Event('currententrychange', {\n    bubbles: false,\n    cancelable: false,\n  }) as {\n    -readonly [P in keyof NavigationCurrentEntryChangeEvent]: NavigationCurrentEntryChangeEvent[P];\n  };\n  event.from = from;\n  event.navigationType = navigationType;\n  return event as FakeNavigationCurrentEntryChangeEvent;\n}\n\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({state}: {state: unknown}) {\n  const event = new Event('popstate', {\n    bubbles: false,\n    cancelable: false,\n  }) as {-readonly [P in keyof PopStateEvent]: PopStateEvent[P]};\n  event.state = state;\n  return event as PopStateEvent;\n}\n\nfunction createHashChangeEvent(newURL: string, oldURL: string) {\n  const event = new Event('hashchange', {\n    bubbles: false,\n    cancelable: false,\n  }) as {-readonly [P in keyof HashChangeEvent]: HashChangeEvent[P]};\n  event.newURL = newURL;\n  event.oldURL = oldURL;\n  return event as HashChangeEvent;\n}\n\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nexport class FakeNavigationDestination implements NavigationDestination {\n  url: string;\n  readonly sameDocument: boolean;\n  readonly key: string | null;\n  readonly id: string | null;\n  readonly index: number;\n\n  state?: unknown;\n  private readonly historyState: unknown;\n\n  constructor({\n    url,\n    sameDocument,\n    historyState,\n    state,\n    key = null,\n    id = null,\n    index = -1,\n  }: {\n    url: string;\n    sameDocument: boolean;\n    historyState: unknown;\n    state?: unknown;\n    key?: string | null;\n    id?: string | null;\n    index?: number;\n  }) {\n    this.url = url;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n    this.key = key;\n    this.id = id;\n    this.index = index;\n  }\n\n  getState(): unknown {\n    return this.state;\n  }\n\n  getHistoryState(): unknown {\n    return this.historyState;\n  }\n}\n\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from: URL, to: URL): boolean {\n  return (\n    to.hash !== from.hash &&\n    to.hostname === from.hostname &&\n    to.pathname === from.pathname &&\n    to.search === from.search\n  );\n}\n\nclass InternalNavigationTransition implements NavigationTransition {\n  readonly finished: Promise<void>;\n  readonly committed: Promise<void>;\n  finishedResolve!: () => void;\n  finishedReject!: (reason: Error) => void;\n  committedResolve!: () => void;\n  committedReject!: (reason: Error) => void;\n  constructor(\n    readonly from: NavigationHistoryEntry,\n    readonly navigationType: NavigationTypeString,\n  ) {\n    this.finished = new Promise<void>((resolve, reject) => {\n      this.finishedReject = reject;\n      this.finishedResolve = resolve;\n    });\n    this.committed = new Promise<void>((resolve, reject) => {\n      this.committedReject = reject;\n      this.committedResolve = resolve;\n    });\n    // All rejections are handled.\n    this.finished.catch(() => {});\n    this.committed.catch(() => {});\n  }\n}\n\n/**\n * Internal utility class for representing the result of a navigation.\n * Generally equivalent to the \"apiMethodTracker\" in the spec.\n */\nclass InternalNavigationResult {\n  committedTo: FakeNavigationHistoryEntry | null = null;\n  committedResolve!: (entry: FakeNavigationHistoryEntry) => void;\n  committedReject!: (reason: Error) => void;\n  finishedResolve!: () => void;\n  finishedReject!: (reason: Error) => void;\n  readonly committed: Promise<FakeNavigationHistoryEntry>;\n  readonly finished: Promise<FakeNavigationHistoryEntry>;\n  get signal(): AbortSignal {\n    return this.abortController.signal;\n  }\n  private readonly abortController = new AbortController();\n\n  constructor(readonly navigation: FakeNavigation) {\n    this.committed = new Promise<FakeNavigationHistoryEntry>((resolve, reject) => {\n      this.committedResolve = (entry) => {\n        this.committedTo = entry;\n        resolve(entry);\n      };\n      this.committedReject = reject;\n    });\n\n    this.finished = new Promise<FakeNavigationHistoryEntry>((resolve, reject) => {\n      this.finishedResolve = () => {\n        if (this.committedTo === null) {\n          throw new Error(\n            'NavigateEvent should have been committed before resolving finished promise.',\n          );\n        }\n        resolve(this.committedTo);\n      };\n      this.finishedReject = (reason: Error) => {\n        reject(reason);\n        this.abortController.abort(reason);\n      };\n    });\n    // All rejections are handled.\n    this.committed.catch(() => {});\n    this.finished.catch(() => {});\n  }\n}\n\n/** Internal options for performing a navigate. */\ninterface InternalNavigateOptions {\n  navigationType: NavigationTypeString;\n  cancelable: boolean;\n  canIntercept: boolean;\n  userInitiated: boolean;\n  hashChange: boolean;\n  info?: unknown;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Public Test Library for unit testing Angular applications. Assumes that you are running\n * with <PERSON>, <PERSON><PERSON>, or a similar framework which exports a beforeEach function and\n * allows tests to be asynchronous by either returning a promise or using a 'done' parameter.\n */\n\nimport {resetFakeAsyncZoneIfExists} from './fake_async';\nimport {TestBedImpl} from './test_bed';\n\n// Reset the test providers and the fake async zone before each test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// beforeEach is only defined when executing the tests\nglobalThis.beforeEach?.(getCleanupHook(false));\n\n// We provide both a `beforeEach` and `afterEach`, because the updated behavior for\n// tearing down the module is supposed to run after the test so that we can associate\n// teardown errors with the correct test.\n// We keep a guard because somehow this file can make it into a bundle and be executed\n// afterEach is only defined when executing the tests\nglobalThis.afterEach?.(getCleanupHook(true));\n\nexport function getCleanupHook(expectedTeardownValue: boolean): VoidFunction {\n  return () => {\n    const testBed = TestBedImpl.INSTANCE;\n    if (testBed.shouldTearDownTestingModule() === expectedTeardownValue) {\n      testBed.resetTestingModule();\n      resetFakeAsyncZoneIfExists();\n    }\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '../../src/core';\n\n@Injectable()\nexport class Log<T = string> {\n  logItems: T[];\n\n  constructor() {\n    this.logItems = [];\n  }\n\n  add(value: T): void {\n    this.logItems.push(value);\n  }\n\n  fn(value: T) {\n    return () => {\n      this.logItems.push(value);\n    };\n  }\n\n  clear(): void {\n    this.logItems = [];\n  }\n\n  result(): string {\n    return this.logItems.join('; ');\n  }\n}\n"], "names": ["inject", "ɵChangeDetectionScheduler", "ɵgenerateStandaloneInDeclarationsError", "ɵisComponentDefPendingResolution", "ɵresolveComponentResources", "ɵclearResolutionOfComponentResourcesQueue", "ɵrestoreComponentResolutionQueue", "R3NgModuleFactory", "setUnknownElementStrictMode", "setUnknownPropertyStrictMode", "getUnknownElementStrictMode", "getUnknownPropertyStrictMode"], "mappings": ";;;;;;;;;;;;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBG;AACG,SAAU,YAAY,CAAC,EAAY,EAAA;AACvC,IAAA,MAAM,KAAK,GAAQ,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI;IAC5D,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,YAAA;AACL,YAAA,OAAO,OAAO,CAAC,MAAM,CACnB,4EAA4E;AAC1E,gBAAA,yDAAyD,CAC5D;AACH,SAAC;;AAEH,IAAA,MAAM,SAAS,GAAG,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC/D,IAAA,IAAI,OAAO,SAAS,KAAK,UAAU,EAAE;AACnC,QAAA,OAAO,SAAS,CAAC,EAAE,CAAC;;IAEtB,OAAO,YAAA;AACL,QAAA,OAAO,OAAO,CAAC,MAAM,CACnB,gFAAgF;AAC9E,YAAA,iEAAiE,CACpE;AACH,KAAC;AACH;;AClCO,MAAM,kCAAkC,GAAG,IAAI;MAGzC,8BAA8B,CAAA;AACxB,IAAA,IAAI,GAAGA,QAAM,CAAC,MAAM,CAAC;AACrB,IAAA,QAAQ,GAAGA,QAAM,CAAC,mBAAmB,CAAC;AAC/C,IAAA,gBAAgB;AACf,IAAA,yBAAyB,GAA8B,IAAI,GAAG,EAAE;AAEzE,IAAA,WAAW,CAAC,CAAU,EAAA;AACpB,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAK;gBAC/B,IAAI,CAAC,gBAAgB,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC;AACzD,gBAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;AACtC,aAAC,CAAC;;QACF,OAAO,SAAkB,EAAE;YAC3B,CAAC,GAAG,SAAS;;;;;QAMf,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAG,CAAC,EAAE;YAC3C,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,EAAE;gBACxD,EAAE,CAAC,CAAC,CAAC;;AAEP,YAAA,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE;;aACjC;AACL,YAAA,MAAM,CAAC;;;wHAzBA,8BAA8B,GAAA,CAAA,EAAA;AAA9B,IAAA,OAAA,KAAA,iBAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,KAAA,EAAA,8BAA8B,WAA9B,8BAA8B,CAAA,IAAA,EAAA,CAAA;;iFAA9B,8BAA8B,EAAA,CAAA;cAD1C;;;ACOD;;;;AAIG;MACU,iBAAiB,CAAA;AAGlB,IAAA,KAAA;AACA,IAAA,gBAAA;;IAFV,WACU,CAAA,KAAwB,EACxB,gBAA2C,EAAA;QAD3C,IAAK,CAAA,KAAA,GAAL,KAAK;QACL,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;;AAG1B;;;AAGG;IACH,MAAM,MAAM,CAAC,KAAsB,EAAA;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;AACxC,YAAA,MAAM,aAAa,GAAG,8BAA8B,CAAC,KAAK,CAAC;AAC3D,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,0CAAA,EAA6C,aAAa,CAAY,UAAA,CAAA;AACpE,gBAAA,CAAA,kBAAA,EAAqB,aAAa,CAAC,WAAW,EAAE,CAAA,6BAAA,CAA+B,CAClF;;AAEH,QAAA,IAAI,KAAK,KAAK,eAAe,CAAC,QAAQ,EAAE;YACtC,MAAM,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;;;;QAIvF,MAAM,mBAAmB,GAAG,IAAI;AAChC,QAAA,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,mBAAmB,CAAC;AAC1F,QAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;;AAGvC;;;AAGG;IACH,cAAc,GAAA;QACZ,MAAM,WAAW,GAAwB,EAAE;;;;QAI3C,MAAM,kBAAkB,GAAG,EAAE;QAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,IAAI,uBAAuB,EAAE;YAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,uBAAuB,CAAC;AAC5D,YAAA,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC;AAClC,YAAA,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;AAC/B,gBAAA,kBAAkB,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;;;AAGhF,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;;AAE7C;AAED,SAAS,gBAAgB,CAAC,KAAsB,EAAE,KAAwB,EAAA;IACxE,QAAQ,KAAK;QACX,KAAK,eAAe,CAAC,WAAW;AAC9B,YAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,oBAAoB,KAAK,IAAI;QACrD,KAAK,eAAe,CAAC,OAAO;AAC1B,YAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,gBAAgB,KAAK,IAAI;QACjD,KAAK,eAAe,CAAC,KAAK;AACxB,YAAA,OAAO,KAAK,CAAC,QAAQ,CAAC,cAAc,KAAK,IAAI;QAC/C,KAAK,eAAe,CAAC,QAAQ;AAC3B,YAAA,OAAO,IAAI;AACb,QAAA;AACE,YAAA,OAAO,KAAK;;AAElB;AAEA,SAAS,8BAA8B,CAAC,KAAsB,EAAA;IAC5D,QAAQ,KAAK;QACX,KAAK,eAAe,CAAC,WAAW;AAC9B,YAAA,OAAO,aAAa;QACtB,KAAK,eAAe,CAAC,OAAO;AAC1B,YAAA,OAAO,SAAS;QAClB,KAAK,eAAe,CAAC,KAAK;AACxB,YAAA,OAAO,OAAO;AAChB,QAAA;AACE,YAAA,OAAO,MAAM;;AAEnB;;ACtFA;AACO,MAAM,0CAA0C,GAAG,IAAI;AAE9D;AACO,MAAM,iCAAiC,GAAG,KAAK;AAEtD;AACO,MAAM,mCAAmC,GAAG,KAAK;AAExD;AACO,MAAM,4BAA4B,GAAG,kBAAkB,CAAC,WAAW;AAE1E;;;;AAIG;MACU,qBAAqB,CAAA;IAChC,iBAAiB,CAAC,aAAqB,EAAA;AACvC,IAAA,qBAAqB;AACtB;AAED;;AAEG;MACU,0BAA0B,GAAG,IAAI,cAAc,CAAU,4BAA4B;AAElG;;AAEG;MACU,wBAAwB,GAAG,IAAI,cAAc,CAAU,0BAA0B;;ACJ9F;;;;AAIG;MACU,gBAAgB,CAAA;AAwDR,IAAA,YAAA;AAvDnB;;AAEG;AACH,IAAA,YAAY;AAEZ;;AAEG;AACH,IAAA,iBAAiB;AAEjB;;AAEG;AACH,IAAA,aAAa;AAEb;;AAEG;AACH,IAAA,UAAU;AAEV;;AAEG;AACH,IAAA,iBAAiB;AAET,IAAA,SAAS;IACT,YAAY,GAAY,KAAK;;IAElB,kBAAkB,GAAGA,QAAM,CAAC,wBAAwB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAEhF,IAAA,OAAO,GAAW,IAAI,CAAC,kBAAkB,GAAG,IAAI,UAAU,EAAE,GAAGA,QAAM,CAAC,MAAM,CAAC;;;;;;;;AAQpE,IAAA,OAAO,GAAGA,QAAM,CAAC,cAAc,CAAC;AAClC,IAAA,WAAW,GAAG,IAAI,CAAC,OAAgC;AACnD,IAAA,YAAY,GAAGA,QAAM,CAAC,oBAAoB,CAAC;AAC3C,IAAA,eAAe,GAAGA,QAAM,CAAC,8BAA8B,CAAC;AACxD,IAAA,eAAe,GAAGA,QAAM,CAAC,gBAAgB,CAAC;AAC1C,IAAA,SAAS,GAAGA,QAAM,CAACC,wBAAyB,CAAC;AAC7C,IAAA,mBAAmB,GAAGD,QAAM,CAAC,eAAe,CAAC;AAC7C,IAAA,iBAAiB,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,GAAG,KAAK;AAChE,IAAA,UAAU,GAChBA,QAAM,CAAC,0BAA0B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,IAAI,CAAC,iBAAiB;AAExE,IAAA,aAAa,GAAG,IAAI,YAAY,EAAE;;AAG1C,IAAA,MAAM,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO;;AAGtD,IAAA,WAAA,CAAmB,YAA6B,EAAA;QAA7B,IAAY,CAAA,YAAA,GAAZ,YAAY;AAC7B,QAAA,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,iBAAiB;AACvD,QAAA,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,QAAQ;QACvC,IAAI,CAAC,YAAY,GAAiB,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;AAC7E,QAAA,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,QAAQ;QAC9C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;AAClD,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY;AAEhC,QAAA,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC7D,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AACpE,YAAA,IAAI,CAAC,SAAS,EAAE,MAAM,0CAAkC;AACxD,YAAA,IAAI,CAAC,SAAS,EAAE,MAAM,uDAA+C;;QAEvE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAK;AACxC,YAAA,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AAChE,YAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AACzE,SAAC,CAAC;;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;AAC7B,gBAAA,IAAI,EAAE,CAAC,KAAU,KAAI;;;;;;;;;AASnB,oBAAA,IAAI,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAE;wBAC5E;;AAEF,oBAAA,MAAM,KAAK;iBACZ;AACF,aAAA,CAAC,CACH;AACH,SAAC,CAAC;;AAGJ;;AAEG;IACH,aAAa,CAAC,cAAc,GAAG,IAAI,EAAA;QACjC,MAAM,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,cAAc;AACjF,QAAA,IAAI;YACF,IAAI,CAAC,cAAc,EAAE;gBACnB,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,cAAc,GAAG,MAAO,GAAC;;AAG/D,YAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,gBAAA,IAAI;AACF,oBAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,IAAI;AAC3C,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;wBACX;AACR,oBAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,KAAK;;;iBAEzC;;;AAGL,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;;AAEpB,oBAAA,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE;AAChC,oBAAA,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE;oBACtC,IAAI,CAAC,cAAc,EAAE;AACvB,iBAAC,CAAC;;;gBAEI;YACR,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,cAAc,GAAG,sBAAsB;;;AAI/E;;AAEG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE;;IAmBzC,iBAAiB,CAAC,UAAU,GAAG,IAAI,EAAA;AACjC,QAAA,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,EAAE;AACvC,YAAA,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC;;QAEnF,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;AACpD,YAAA,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC;;QAGxF,IAAI,UAAU,EAAE;AACd,YAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;;aAC/D;AACL,YAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;;AAGzE,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU;QAC5B,IAAI,CAAC,aAAa,EAAE;;AAGtB;;;AAGG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe;;AAG3C;;;;;AAKG;IACH,UAAU,GAAA;AACR,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;AACnB,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;;QAG/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,GAAG,CAAC,MAAM,CAAC;YAC1D,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,MAAK;gBAClC,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,MAAM,CAAC,MAAM,CAAC;gBAC7D,OAAO,CAAC,IAAI,CAAC;AACf,aAAC,CAAC;AACJ,SAAC,CAAC;;AAGJ;;AAEG;IACH,cAAc,GAAA;QACZ,MAAM,WAAW,GAAwB,EAAE;QAC3C,MAAM,KAAK,GAAI,IAAI,CAAC,YAAY,CAAC,QAAgB,CAAC,QAAQ,CAAC;AAC3D,QAAA,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC;QAElC,MAAM,kBAAkB,GAAG,EAAE;AAC7B,QAAA,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;YAC/B,kBAAkB,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;;AAG7D,QAAA,OAAO,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC;;IAGpC,YAAY,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;AAChC,YAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC;;QAEzE,OAAO,IAAI,CAAC,SAAoC;;AAGlD;;AAEG;IACH,iBAAiB,GAAA;AACf,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE;AACpC,QAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,iBAAiB,EAAE;AAC1C,YAAA,OAAO,QAAQ,CAAC,iBAAiB,EAAE;;AAErC,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE;;AAG1B;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;AAChC,QAAA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AACvE,QAAA,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AAChE,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC3B,YAAA,IAAI,CAAC,YAAY,GAAG,IAAI;;;AAG7B;;ACvRD,MAAM,KAAK,GAAQ,OAAO,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG,IAAI;AAC5D,MAAM,mBAAmB,GAAG,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;AAE7E,MAAM,wCAAwC,GAAG,CAAA;wEACuB;AAExE;;;;;AAKG;SACa,kBAAkB,GAAA;IAChC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,kBAAkB,EAAE;;AAEjD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AAC3D;SAEgB,0BAA0B,GAAA;IACxC,IAAI,mBAAmB,IAAK,IAAY,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,EAAE;QACrE,mBAAmB,CAAC,kBAAkB,EAAE;;AAE5C;AAEA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACa,SAAA,SAAS,CAAC,EAAY,EAAE,OAA2B,EAAA;IACjE,IAAI,mBAAmB,EAAE;QACvB,OAAO,mBAAmB,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC;;AAEnD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AAC3D;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DG;SACa,IAAI,CAClB,MAAiB,GAAA,CAAC,EAClB,WAA4D,GAAA;AAC1D,IAAA,iCAAiC,EAAE,IAAI;AACxC,CAAA,EAAA;IAED,IAAI,mBAAmB,EAAE;QACvB,OAAO,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;;AAEtD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AAC3D;AAEA;;;;;;;;;;AAUG;AACG,SAAU,KAAK,CAAC,QAAiB,EAAA;IACrC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,KAAK,CAAC,QAAQ,CAAC;;AAE5C,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AAC3D;AAEA;;;;AAIG;SACa,oBAAoB,GAAA;IAClC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,oBAAoB,EAAE;;AAEnD,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AAC3D;AAEA;;;;AAIG;SACa,eAAe,GAAA;IAC7B,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,eAAe,EAAE;;AAE9C,IAAA,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC;AAC3D;;ACvKA,IAAI,gBAAgB,GAAG,CAAC;MAEX,iBAAiB,CAAA;AACpB,IAAA,WAAW,GAAG,IAAI,GAAG,EAAe;AAC5C;;;AAGG;AACH,IAAA,gBAAgB,CACd,aAAoC,EACpC,WAAc,EACd,QAA6B,EAAA;QAE7B,MAAM,KAAK,GAAc,EAAE;QAC3B,IAAI,WAAW,EAAE;YACf,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,GAAS,WAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;AAGtF,QAAA,IAAI,QAAQ,CAAC,GAAG,EAAE;YAChB,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,EAAE;gBACnC,MAAM,IAAI,KAAK,CAAC,CAA6B,0BAAA,EAAA,SAAS,CAAC,aAAa,CAAC,CAAoB,kBAAA,CAAA,CAAC;;AAE5F,YAAA,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC;;AAElC,QAAA,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,cAAc,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC;;AAE1D,QAAA,IAAI,QAAQ,CAAC,GAAG,EAAE;AAChB,YAAA,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC;;AAElC,QAAA,OAAO,IAAI,aAAa,CAAM,KAAK,CAAC;;AAEvC;AAED,SAAS,cAAc,CAAC,QAAmB,EAAE,MAAW,EAAE,UAA4B,EAAA;AACpF,IAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU;AACvC,IAAA,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE;AACzB,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC;AAChC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAC9B,YAAA,WAAW,CAAC,OAAO,CAAC,CAAC,KAAU,KAAI;AACjC,gBAAA,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC1D,aAAC,CAAC;;aACG;AACL,YAAA,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;;;AAIlE,IAAA,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE;AAC3B,QAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC;AAChC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;AAC5B,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAC/B,CAAC,KAAU,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAC1E;;aACI;AACL,YAAA,IAAI,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;AAChE,gBAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS;;;;AAIlC;AAEA,SAAS,WAAW,CAAC,QAAmB,EAAE,GAAQ,EAAA;AAChD,IAAA,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AACtB,QAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC;AAC1B,QAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC;QAChC,IAAI,SAAS,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACjD,QAAQ,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC;;aACtC;AACL,YAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ;;;AAG/B;AAEA,SAAS,WAAW,CAAC,QAAmB,EAAE,GAAQ,EAAA;AAChD,IAAA,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;QACtB,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC;;AAE9B;AAEA,SAAS,YAAY,CAAC,QAAa,EAAE,SAAc,EAAE,UAA4B,EAAA;IAC/E,IAAI,YAAY,GAAG,CAAC;AACpB,IAAA,MAAM,SAAS,GAAG,IAAI,GAAG,EAAkB;AAC3C,IAAA,MAAM,QAAQ,GAAG,CAAC,GAAQ,EAAE,KAAU,KAAI;QACxC,IAAI,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC/C,YAAA,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;;;;YAI7B,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,YAAY,EAAE,CAAE,CAAA,CAAC;;AAG9C,YAAA,OAAO,KAAK;;AACP,aAAA,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;AACtC,YAAA,KAAK,GAAG,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC;;AAEhD,QAAA,OAAO,KAAK;AACd,KAAC;AAED,IAAA,OAAO,CAAG,EAAA,QAAQ,CAAI,CAAA,EAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA,CAAE;AAC7D;AAEA,SAAS,mBAAmB,CAAC,GAAQ,EAAE,UAA4B,EAAA;IACjE,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;IAC5B,IAAI,CAAC,EAAE,EAAE;QACP,EAAE,GAAG,CAAG,EAAA,SAAS,CAAC,GAAG,CAAC,CAAG,EAAA,gBAAgB,EAAE,CAAA,CAAE;AAC7C,QAAA,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC;;AAEzB,IAAA,OAAO,EAAE;AACX;AAEA,SAAS,WAAW,CAAC,GAAQ,EAAA;IAC3B,MAAM,KAAK,GAAa,EAAE;;IAE1B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;QAChC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACzB,YAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEpB,KAAC,CAAC;;IAGF,IAAI,KAAK,GAAG,GAAG;IACf,QAAQ,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG;QAC7C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,SAAS,KAAI;YACvC,MAAM,IAAI,GAAG,MAAM,CAAC,wBAAwB,CAAC,KAAK,EAAE,SAAS,CAAC;AAC9D,YAAA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE;AACvD,gBAAA,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;;AAEzB,SAAC,CAAC;;AAEJ,IAAA,OAAO,KAAK;AACd;;AC/HA,MAAM,UAAU,GAAG,IAAI,sBAAsB,EAAE;AAW/C;;AAEG;AACH,MAAe,gBAAgB,CAAA;AACrB,IAAA,SAAS,GAAG,IAAI,GAAG,EAAoC;AACvD,IAAA,QAAQ,GAAG,IAAI,GAAG,EAAuB;IAIjD,WAAW,CAAC,IAAe,EAAE,QAA6B,EAAA;AACxD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE;AAChD,QAAA,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;QACxB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;AACnC,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;;AAG5B,IAAA,YAAY,CAAC,SAAkD,EAAA;AAC7D,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;QACtB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAI;AACrC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AAClC,SAAC,CAAC;;AAGJ,IAAA,aAAa,CAAC,IAAe,EAAA;QAC3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC;;;;;;AAMhD,QAAA,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,YAAA,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;AACjC,YAAA,MAAM,WAAW,GACf,UAAU,YAAY,SAAS;AAC/B,gBAAA,UAAU,YAAY,SAAS;AAC/B,gBAAA,UAAU,YAAY,IAAI;gBAC1B,UAAU,YAAY,QAAQ;YAChC,IAAI,WAAW,EAAE;AACf,gBAAA,OAAO,UAAU,YAAY,IAAI,CAAC,IAAI,GAAI,UAA2B,GAAG,IAAI;;;AAGhF,QAAA,OAAO,IAAI;;AAGb,IAAA,OAAO,CAAC,IAAe,EAAA;AACrB,QAAA,IAAI,QAAQ,GAAa,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;QAExD,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACnC,IAAI,QAAQ,EAAE;gBACZ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC1C,IAAI,SAAS,EAAE;AACb,oBAAA,MAAM,SAAS,GAAG,IAAI,iBAAiB,EAAE;AACzC,oBAAA,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAI;AAC7B,wBAAA,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAS,EAAE,QAAQ,CAAC;AACvE,qBAAC,CAAC;;;YAGN,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC;;AAGnC,QAAA,OAAO,QAAQ;;AAElB;AAEK,MAAO,iBAAkB,SAAQ,gBAA2B,CAAA;AAChE,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,SAAS;;AAEnB;AAEK,MAAO,iBAAkB,SAAQ,gBAA2B,CAAA;AAChE,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,SAAS;;AAEnB;AAEK,MAAO,YAAa,SAAQ,gBAAsB,CAAA;AACtD,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,IAAI;;AAEd;AAEK,MAAO,gBAAiB,SAAQ,gBAA0B,CAAA;AAC9D,IAAA,IAAa,IAAI,GAAA;AACf,QAAA,OAAO,QAAQ;;AAElB;;ACjCD,IAAK,qBAGJ;AAHD,CAAA,UAAK,qBAAqB,EAAA;AACxB,IAAA,qBAAA,CAAA,qBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;AACX,IAAA,qBAAA,CAAA,qBAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,mBAAiB;AACnB,CAAC,EAHI,qBAAqB,KAArB,qBAAqB,GAGzB,EAAA,CAAA,CAAA;AAED,SAAS,uBAAuB,CAAC,KAAc,EAAA;AAC7C,IAAA,QACE,KAAK,KAAK,qBAAqB,CAAC,WAAW,IAAI,KAAK,KAAK,qBAAqB,CAAC,iBAAiB;AAEpG;AAEA,SAAS,4BAA4B,CACnC,KAAkB,EAClB,QAAuB,EACvB,QAAgB,EAAA;AAEhB,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AACrB,QAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;YAClC,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;AACxC,YAAA,IAAI,SAAS,KAAK,SAAS,CAAC,UAAU,IAAI,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC,EAAE;gBACvE,MAAM,IAAI,KAAK,CAACE,qCAAsC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;;;AAG7E,KAAC,CAAC;AACJ;MAgBa,eAAe,CAAA;AAqEhB,IAAA,QAAA;AACA,IAAA,qBAAA;IArEF,gCAAgC,GAAqC,IAAI;;IAGzE,YAAY,GAAgB,EAAE;IAC9B,OAAO,GAAgB,EAAE;IACzB,SAAS,GAAe,EAAE;IAC1B,OAAO,GAAU,EAAE;;AAGnB,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAAa;AACxC,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAAa;AACxC,IAAA,YAAY,GAAG,IAAI,GAAG,EAAa;;;AAInC,IAAA,2BAA2B,GAAG,IAAI,GAAG,EAAiB;;AAGtD,IAAA,cAAc,GAAG,IAAI,GAAG,EAAa;AACrC,IAAA,cAAc,GAAG,IAAI,GAAG,EAAa;;AAGrC,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAAqB;;;AAIhD,IAAA,uBAAuB,GAAG,IAAI,GAAG,EAAuB;IAExD,SAAS,GAAc,aAAa,EAAE;;;;;;;;AAStC,IAAA,sBAAsB,GAAG,IAAI,GAAG,EAAuD;;;;;;AAOvF,IAAA,aAAa,GAAG,IAAI,GAAG,EAA0D;;;IAIjF,aAAa,GAAuB,EAAE;IAEtC,SAAS,GAAoB,IAAI;IACjC,iBAAiB,GAAsB,IAAI;IAE3C,iBAAiB,GAAe,EAAE;IAClC,qBAAqB,GAAe,EAAE;;;AAGtC,IAAA,yBAAyB,GAAG,IAAI,GAAG,EAAiC;AACpE,IAAA,wBAAwB,GAAG,IAAI,GAAG,EAAiB;AACnD,IAAA,6BAA6B,GAAG,IAAI,GAAG,EAAa;AAEpD,IAAA,cAAc;IACd,aAAa,GAA4B,IAAI;IAE7C,kBAAkB,GAAG,4BAA4B;IACjD,4BAA4B,GAAG,kCAAkC;IAEzE,WACU,CAAA,QAAqB,EACrB,qBAA8C,EAAA;QAD9C,IAAQ,CAAA,QAAA,GAAR,QAAQ;QACR,IAAqB,CAAA,qBAAA,GAArB,qBAAqB;AAE7B,QAAA,MAAM,iBAAiB,CAAA;AAAG;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,iBAAwB;;AAGhD,IAAA,oBAAoB,CAAC,SAA4B,EAAA;AAC/C,QAAA,IAAI,CAAC,iBAAiB,GAAG,SAAS;AAClC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGvB,IAAA,sBAAsB,CAAC,SAA6B,EAAA;;AAElD,QAAA,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS,EAAE;;AAExC,YAAA,4BAA4B,CAC1B,SAAS,CAAC,YAAY,EACtB,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,uCAAuC,CACxC;YACD,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,YAAY,EAAE,qBAAqB,CAAC,WAAW,CAAC;YAC9E,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC;;;AAInD,QAAA,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;AACnC,YAAA,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,OAAO,CAAC;YAClD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;;AAGzC,QAAA,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC;;AAG7C,QAAA,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;;QAGzC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,IAAI,4BAA4B;AACtF,QAAA,IAAI,CAAC,4BAA4B;AAC/B,YAAA,SAAS,CAAC,wBAAwB,IAAI,kCAAkC;;IAG5E,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAAA;AACtE,QAAA,WAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC;AACxC,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAA6B,CAAC;;QAGzD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACrD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACxD,QAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;YACrB,MAAM,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC;;AAGnD,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC;;;;AAK1C,QAAA,IAAI,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAG7C,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,+BAA+B,CAAC,SAAS,EAAE,QAAQ,CAAC;QACzD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC;AACzD,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC;;;AAIrC,QAAA,IAAI,CAAC,uCAAuC,CAAC,SAAS,CAAC;;IAGzD,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,+BAA+B,CAAC,SAAS,EAAE,QAAQ,CAAC;QACzD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC;AACzD,QAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC;;IAGvC,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAA;AAC5D,QAAA,IAAI,CAAC,+BAA+B,CAAC,IAAI,EAAE,QAAQ,CAAC;QACpD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC/C,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;;IAGrB,+BAA+B,CACrC,IAAe,EACf,QAAwD,EAAA;AAExD,QAAA,IACE,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,YAAY,CAAC;AAC1C,YAAA,QAAQ,CAAC,GAAG,EAAE,cAAc,CAAC,YAAY,CAAC;YAC1C,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,YAAY,CAAC,EAC7C;AACA,YAAA,MAAM,IAAI,KAAK,CACb,uBAAuB,IAAI,CAAC,IAAI,CAAsC,oCAAA,CAAA;AACpE,gBAAA,CAAA,wEAAA,CAA0E,CAC7E;;;IAIL,gBAAgB,CACd,KAAU,EACV,QAAgF,EAAA;AAEhF,QAAA,IAAI,WAAqB;AACzB,QAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,SAAS,EAAE;AACrC,YAAA,WAAW,GAAG;AACZ,gBAAA,OAAO,EAAE,KAAK;gBACd,UAAU,EAAE,QAAQ,CAAC,UAAU;AAC/B,gBAAA,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE;gBACzB,KAAK,EAAE,QAAQ,CAAC,KAAK;aACtB;;AACI,aAAA,IAAI,QAAQ,CAAC,QAAQ,KAAK,SAAS,EAAE;AAC1C,YAAA,WAAW,GAAG,EAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAC;;aAC7E;AACL,YAAA,WAAW,GAAG,EAAC,OAAO,EAAE,KAAK,EAAC;;AAGhC,QAAA,MAAM,aAAa,GACjB,OAAO,KAAK,KAAK,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,IAAI;AAC5D,QAAA,MAAM,UAAU,GAAG,aAAa,KAAK,IAAI,GAAG,IAAI,GAAG,iBAAiB,CAAC,aAAa,CAAC,UAAU,CAAC;AAC9F,QAAA,MAAM,eAAe,GACnB,UAAU,KAAK,MAAM,GAAG,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB;AAC7E,QAAA,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;;QAGjC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,CAAC;AACrD,QAAA,IAAI,aAAa,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YACnF,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,UAAU,CAAC;AACxE,YAAA,IAAI,iBAAiB,KAAK,SAAS,EAAE;AACnC,gBAAA,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC;;iBAC9B;gBACL,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,WAAW,CAAC,CAAC;;;;IAKnE,kCAAkC,CAAC,IAAe,EAAE,QAAgB,EAAA;AAClE,QAAA,MAAM,GAAG,GAAI,IAAY,CAAC,WAAW,CAAC;QACtC,MAAM,YAAY,GAAG,MAAc;AACjC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAe;AACrE,YAAA,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM;AAC5D,SAAC;AACD,QAAA,MAAM,iBAAiB,GAAG,CAAC,CAAC,GAAG,IAAI,CAACC,+BAAgC,CAAC,IAAI,CAAC,IAAI,YAAY,EAAE;;;;;;;;QAS5F,MAAM,QAAQ,GAAG;AACf,cAAE,EAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS;AAC3D,cAAE,EAAC,QAAQ,EAAC;QACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAC,GAAG,EAAE,QAAQ,EAAC,CAAC;AAE7C,QAAA,IAAI,iBAAiB,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5D,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC;;;QAIpD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,EAAE,qBAAqB,CAAC,iBAAiB,CAAC;;AAGxE,IAAA,MAAM,yCAAyC,GAAA;AACrD,QAAA,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,KAAK,CAAC;YAAE;QAEjD,MAAM,QAAQ,GAAG,EAAE;AACnB,QAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,2BAA2B,EAAE;AACxD,YAAA,MAAM,eAAe,GAAG,uBAAuB,CAAC,SAAS,CAAC;YAC1D,IAAI,eAAe,EAAE;AACnB,gBAAA,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;;;AAGpC,QAAA,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE;QAExC,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;QAChD,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7C,QAAA,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC;;;AAIjD,QAAA,KAAK,MAAM,SAAS,IAAI,gBAAgB,EAAE;AACxC,YAAA,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC;;;AAIjD,IAAA,MAAM,iBAAiB,GAAA;QACrB,IAAI,CAAC,6BAA6B,EAAE;;;;AAKpC,QAAA,MAAM,IAAI,CAAC,yCAAyC,EAAE;;;;;AAMtD,QAAA,4BAA4B,CAC1B,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,uCAAuC,CACxC;;AAGD,QAAA,IAAI,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,EAAE;;QAGjD,IAAI,mBAAmB,EAAE;AACvB,YAAA,IAAI,cAA8B;AAClC,YAAA,IAAI,QAAQ,GAAG,CAAC,GAAW,KAAqB;gBAC9C,IAAI,CAAC,cAAc,EAAE;oBACnB,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC;;gBAEpD,OAAO,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjD,aAAC;AACD,YAAA,MAAMC,yBAA0B,CAAC,QAAQ,CAAC;;;IAI9C,QAAQ,GAAA;;QAEN,IAAI,CAAC,gBAAgB,EAAE;;QAGvB,IAAI,CAAC,iBAAiB,EAAE;QAExB,IAAI,CAAC,qBAAqB,EAAE;QAE5B,IAAI,CAAC,sBAAsB,EAAE;;;QAI7B,IAAI,CAAC,iCAAiC,EAAE;;;AAIxC,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE;AAEnC,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ;AAC7C,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,cAAc,EAAE,EAAE,CAAC;;;AAI5E,QAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,qBAAqB,CAAS,CAAC,eAAe,EAAE;;;;AAKjF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC;QAC9E,WAAW,CAAC,QAAQ,CAAC;QAErB,OAAO,IAAI,CAAC,aAAa;;AAG3B;;AAEG;AACH,IAAA,oBAAoB,CAAC,UAAqB,EAAA;AACxC,QAAA,IAAI,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC;QAC7C,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC;QAC9C,IAAI,CAAC,qBAAqB,EAAE;;AAG9B;;AAEG;IACH,MAAM,qBAAqB,CAAC,UAAqB,EAAA;AAC/C,QAAA,IAAI,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC;AAC7C,QAAA,MAAM,IAAI,CAAC,iBAAiB,EAAE;QAC9B,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC;QAC9C,IAAI,CAAC,qBAAqB,EAAE;;AAG9B;;AAEG;IACH,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;;AAG9B;;AAEG;AACH,IAAA,sBAAsB,CAAC,UAAwB,EAAA;AAC7C,QAAA,OAAO,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,KAAI;AACnF,YAAA,MAAM,YAAY,GAAI,WAAmB,CAAC,IAAI;AAC9C,YAAA,YAAY,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,aAAc,CAAC,CAAC;AACvF,YAAA,OAAO,SAAS;SACjB,EAAE,EAA6B,CAAC;;IAG3B,gBAAgB,GAAA;;QAEtB,IAAI,mBAAmB,GAAG,KAAK;QAC/B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AAC7C,YAAA,IAAI,uBAAuB,CAAC,WAAW,CAAC,EAAE;AACxC,gBAAA,MAAM,IAAI,KAAK,CACb,cAAc,WAAW,CAAC,IAAI,CAA6B,2BAAA,CAAA;AACzD,oBAAA,CAAA,2EAAA,CAA6E,CAChF;;AAGH,YAAA,mBAAmB,GAAG,mBAAmB,IAAID,+BAAgC,CAAC,WAAW,CAAC;AAE1F,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;AAC9D,YAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,MAAM,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC;;AAGvD,YAAA,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC;AAC9C,YAAA,WAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC;AAC3C,YAAA,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC;AACzC,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;QAE9B,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AAC7C,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;AAC9D,YAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,MAAM,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC;;AAEvD,YAAA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,WAAW,CAAC;AAC7C,YAAA,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC;AACzC,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;QAE9B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AACxC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;AACzD,YAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,MAAM,gBAAgB,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC;;AAElD,YAAA,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,WAAW,CAAC;AAC9C,YAAA,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC;AACpC,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AAEzB,QAAA,OAAO,mBAAmB;;IAGpB,qBAAqB,GAAA;QAC3B,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,CAAC,EAAE;;;;YAInC,MAAM,gBAAgB,GAAI,IAAI,CAAC,cAAsB,CAAC,UAAU,CAAC;YACjE,MAAM,eAAe,GAAG,IAAI,CAAC,iCAAiC,CAAC,gBAAgB,CAAC,OAAO,CAAC;AACxF,YAAA,IAAI,eAAe,CAAC,IAAI,GAAG,CAAC,EAAE;AAC5B,gBAAA,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;AACrC,oBAAA,WAAW,CAAC,kBAAkB,CAAC,UAAU,CAAC;AAC5C,iBAAC,CAAC;;;AAIN,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAA+D;AAC5F,QAAA,MAAM,gBAAgB,GAAG,CACvB,UAA6C,KACjB;YAC5B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAClC,gBAAA,MAAM,eAAe,GAAG,uBAAuB,CAAC,UAAU,CAAC;AAC3D,gBAAA,MAAM,QAAQ,GAAG,eAAe,GAAG,IAAI,CAAC,cAAc,GAAI,UAAwB;gBAClF,aAAa,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;;AAE9D,YAAA,OAAO,aAAa,CAAC,GAAG,CAAC,UAAU,CAAE;AACvC,SAAC;QAED,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,aAAa,KAAI;AAChE,YAAA,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,gBAAA,MAAM,WAAW,GAAG,gBAAgB,CAAC,UAAU,CAAC;gBAChD,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,WAAW,EAAE,eAAe,CAAC;gBACvE,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,WAAW,EAAE,UAAU,CAAC;gBAClE,0BAA0B,CAAC,eAAe,CAAC,aAAa,CAAE,EAAE,WAAW,CAAC;;;;;;;;;YAS1E,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC;AACjE,SAAC,CAAC;AAEF,QAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE;;IAG7B,sBAAsB,GAAA;QAC5B,MAAM,mBAAmB,GAAG,CAAC,KAAa,KAAK,CAAC,IAAe,KAAI;YACjE,MAAM,QAAQ,GAAG,KAAK,KAAK,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS;YAC5F,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAE;YACxC,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AACjD,gBAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,EAAE,KAAK,CAAC;;AAEnD,SAAC;QACD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAC7D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;AAE5D,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;AAC3B,QAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;;AAG7B;;;AAGG;AACK,IAAA,6BAA6B,CAAC,IAAe,EAAA;QACnD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;;;;;AAMhE,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC7D;;AAEF,QAAA,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,IAAI,CAAC;;;;;;AAO5C,QAAA,MAAM,WAAW,GAAS,IAAY,CAAC,UAAU,CAAC;;AAGlD,QAAA,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAC;YAAE;AAE9C,QAAA,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;;AAE/B,YAAA,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC;YACjC,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;AAC1D,YAAA,KAAK,MAAM,UAAU,IAAI,YAAY,EAAE;AACrC,gBAAA,IAAI,CAAC,6BAA6B,CAAC,UAAU,CAAC;;;aAE3C;AACL,YAAA,MAAM,SAAS,GAAmD;gBAChE,GAAG,WAAW,CAAC,SAAS;gBACxB,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAyB,CAAC,IAAI,EAAE,CAAC;aACzE;AACD,YAAA,IAAI,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE;AACxC,gBAAA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,IAAI,CAAC;gBAEtC,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC;gBACzD,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC;;;AAIhE,YAAA,MAAM,SAAS,GAAI,IAAY,CAAC,UAAU,CAAC;YAC3C,MAAM,OAAO,GAAG,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC;AAChD,YAAA,KAAK,MAAM,cAAc,IAAI,OAAO,EAAE;AACpC,gBAAA,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC;;;;YAIpD,KAAK,MAAM,cAAc,IAAI,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE;AACzD,gBAAA,IAAI,qBAAqB,CAAC,cAAc,CAAC,EAAE;AACzC,oBAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AACtB,wBAAA,MAAM,EAAE,cAAc;AACtB,wBAAA,SAAS,EAAE,WAAW;wBACtB,aAAa,EAAE,cAAc,CAAC,SAAS;AACxC,qBAAA,CAAC;oBACF,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,sBAAsB,CACpD,cAAc,CAAC,SAA2D,CAC3E;;;;;IAMD,iCAAiC,GAAA;QACvC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAClC,CAAC,MAAM,EAAE,IAAI,MAAO,IAAY,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAC/D;AACD,QAAA,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE;;IAG9B,cAAc,CAAC,GAAU,EAAE,UAA6C,EAAA;AAC9E,QAAA,KAAK,MAAM,KAAK,IAAI,GAAG,EAAE;AACvB,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC;;iBACjC;AACL,gBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC;;;;IAK/B,iBAAiB,CAAC,QAAmB,EAAE,QAAkB,EAAA;;AAE/D,QAAA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC1C,QAAA,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC;AAE1C,QAAA,mBAAmB,CAAC,QAA6B,EAAE,QAAQ,CAAC;;AAGtD,IAAA,uCAAuC,CAAC,IAAmB,EAAA;AACjE,QAAA,MAAM,eAAe,GAAG,uBAAuB,CAAC,IAAI,CAAC;QACrD,IAAI,eAAe,EAAE;AACnB,YAAA,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC;;;IAItC,SAAS,CAAC,IAAe,EAAE,UAAoD,EAAA;;;AAGrF,QAAA,IAAI,CAAC,uCAAuC,CAAC,IAAI,CAAC;AAElD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;QACxD,IAAI,SAAS,EAAE;;;;AAIb,YAAA,IAAIA,+BAAgC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;AAC/E,gBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC;;AAElC,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;YAiB7B,IACE,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC;AACtC,gBAAA,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,qBAAqB,CAAC,WAAW,EAC3E;gBACA,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;;YAEnD;;AAGF,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;QACxD,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE;AACpC,gBAAA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC;;AAElC,YAAA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC;YAC7B;;AAGF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC9C,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;AAC7C,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC;YAC3B;;;AAII,IAAA,0BAA0B,CAAC,GAAU,EAAA;;;;;AAK3C,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAE;AAC/B,QAAA,MAAM,+BAA+B,GAAG,CAAC,GAAU,KAAU;AAC3D,YAAA,KAAK,MAAM,KAAK,IAAI,GAAG,EAAE;AACvB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;oBACxB,+BAA+B,CAAC,KAAK,CAAC;;AACjC,qBAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AAChC,oBAAA,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI;AACtB,oBAAA,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBAC1B;;AAEF,oBAAA,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC;;;AAGtB,oBAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,KAAK,CAAC;oBAC3D,+BAA+B,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBAC3D,+BAA+B,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;;AACtD,qBAAA,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;AACvC,oBAAA,+BAA+B,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;;AAC5C,qBAAA,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;AACvC,oBAAA,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;AAC3B,oBAAA,MAAM,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC;AAElC,oBAAA,IAAI,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBAC1B;;AAEF,oBAAA,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC;oBAEtB,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;AAC1D,oBAAA,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,KAAI;;;;;wBAKlC,IAAI,qBAAqB,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE;AACnE,4BAAA,+BAA+B,CAAC,CAAC,UAAU,CAAC,CAAC;;6BACxC;AACL,4BAAA,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC;;AAEpC,qBAAC,CAAC;;;AAGR,SAAC;QACD,+BAA+B,CAAC,GAAG,CAAC;;;;;;;;;AAU9B,IAAA,iCAAiC,CAAC,GAAU,EAAA;AAClD,QAAA,MAAM,WAAW,GAAG,IAAI,GAAG,EAAqB;AAChD,QAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAqB;AACpD,QAAA,MAAM,wBAAwB,GAAG,CAAC,GAAU,EAAE,IAAyB,KAAU;AAC/E,YAAA,KAAK,MAAM,KAAK,IAAI,GAAG,EAAE;AACvB,gBAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;;AAGxB,oBAAA,wBAAwB,CAAC,KAAK,EAAE,IAAI,CAAC;;AAChC,qBAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AAChC,oBAAA,IAAI,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;;;;AAI1B,wBAAA,IAAI,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC9B,4BAAA,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;wBAEnD;;AAEF,oBAAA,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC;oBACtB,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACrC,wBAAA,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;;;AAGnD,oBAAA,MAAM,SAAS,GAAI,KAAa,CAAC,UAAU,CAAC;AAC5C,oBAAA,wBAAwB,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;;;AAGpF,SAAC;AACD,QAAA,wBAAwB,CAAC,GAAG,EAAE,EAAE,CAAC;AACjC,QAAA,OAAO,eAAe;;AAGxB;;;;;AAKG;IACK,eAAe,CAAC,IAAY,EAAE,IAAe,EAAA;QACnD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC;;QAEzC,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAE;QACjD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC1B,MAAM,UAAU,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,IAAI,CAAC;AAC9D,YAAA,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;;;AAI7B,IAAA,qBAAqB,CAAC,IAAe,EAAE,QAAgB,EAAE,SAAiB,EAAA;AAChF,QAAA,MAAM,GAAG,GAAS,IAAY,CAAC,QAAQ,CAAC;AACxC,QAAA,MAAM,aAAa,GAAQ,GAAG,CAAC,SAAS,CAAC;AACzC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,aAAa,EAAC,CAAC;;AAGlE;;;;AAIG;IACK,6BAA6B,GAAA;AACnC,QAAA,IAAI,IAAI,CAAC,gCAAgC,KAAK,IAAI,EAAE;AAClD,YAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI,GAAG,EAAE;;QAEnDE,wCAAyC,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,KAC7D,IAAI,CAAC,gCAAiC,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CACvD;;AAGH;;;;AAIG;IACK,+BAA+B,GAAA;AACrC,QAAA,IAAI,IAAI,CAAC,gCAAgC,KAAK,IAAI,EAAE;AAClD,YAAAC,+BAAgC,CAAC,IAAI,CAAC,gCAAgC,CAAC;AACvE,YAAA,IAAI,CAAC,gCAAgC,GAAG,IAAI;;;IAIhD,oBAAoB,GAAA;;;QAGlB,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,EAAoB,KAAI;YACxD,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,aAAa;AAC5C,SAAC,CAAC;;QAEF,IAAI,CAAC,aAAa,CAAC,OAAO,CACxB,CAAC,IAAiD,EAAE,IAAe,KAAI;AACrE,YAAA,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACpC,IAAI,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,IAAI,KAAI;gBAChC,IAAI,CAAC,UAAU,EAAE;;;;;;;AAOf,oBAAA,OAAQ,IAAY,CAAC,IAAI,CAAC;;qBACrB;oBACL,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC;;AAEjD,aAAC,CAAC;AACJ,SAAC,CACF;AACD,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;AAC1B,QAAA,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE;QAC1C,IAAI,CAAC,+BAA+B,EAAE;;QAEtC,WAAW,CAAC,iBAAiB,CAAC;;IAGxB,iBAAiB,GAAA;AACvB,QAAA,MAAM,eAAe,CAAA;AAAG;QACxB,mBAAmB,CAAC,eAAoC,EAAE;AACxD,YAAA,SAAS,EAAE;gBACT,GAAG,IAAI,CAAC,qBAAqB;gBAC7B,kCAAkC,CAAC,EAAE,CAAC;gBACtC,8BAA8B;AAC9B,gBAAA,EAAC,OAAO,EAAE,wBAAwB,EAAE,WAAW,EAAE,4BAA4B,EAAC;AAC9E,gBAAA;AACE,oBAAA,OAAO,EAAE,uBAAuB;AAChC,oBAAA,KAAK,EAAE,IAAI;oBACX,QAAQ,EAAE,MAAK;wBACbN,QAAM,CAAC,YAAY,CAAC;qBACrB;AACF,iBAAA;AACF,aAAA;AACF,SAAA,CAAC;AAEF,QAAA,MAAM,SAAS,GAAG;AAChB,YAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,IAAI,cAAc,CAAC,IAAI,CAAC,EAAC;AAC/D,YAAA,EAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,EAAC,QAAQ,EAAE,IAAI,CAAC,kBAAkB,EAAC,EAAC;AAC5E,YAAA;AACE,gBAAA,OAAO,EAAE,kCAAkC;gBAC3C,UAAU,EAAE,MAAK;AACf,oBAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE;AACrC,wBAAA,MAAM,OAAO,GAAGA,QAAM,CAAC,8BAA8B,CAAC;wBACtD,OAAO,CAAC,CAAU,KAAI;AACpB,4BAAA,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AACxB,yBAAC;;yBACI;AACL,wBAAA,MAAM,gBAAgB,GAAGA,QAAM,CAAC,YAAY,CAAC;AAC7C,wBAAA,MAAM,MAAM,GAAGA,QAAM,CAAC,MAAM,CAAC;AAC7B,wBAAA,OAAO,CAAC,CAAU,KAAK,MAAM,CAAC,iBAAiB,CAAC,MAAM,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;;iBAEzF;AACF,aAAA;YACD,GAAG,IAAI,CAAC,SAAS;YACjB,GAAG,IAAI,CAAC,iBAAiB;SAC1B;AACD,QAAA,MAAM,OAAO,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;AAEjF,QAAA,mBAAmB,CACjB,IAAI,CAAC,cAAc,EACnB;YACE,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,OAAO;YACP,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS;AACV,SAAA;+CACsC,IAAI,CAC5C;AAED,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGzD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;YAC3B,OAAO,IAAI,CAAC,SAAS;;QAGvB,MAAM,SAAS,GAAqB,EAAE;AACtC,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC;AACxE,QAAA,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;AAC/B,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,gBAAA,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;;AAElC,SAAC,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE;YACnC,SAAS,CAAC,IAAI,CAAC,GAAI,IAAI,CAAC,iBAAsC,CAAC;;AAGjE,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,EAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAC,CAAC;QAC7E,OAAO,IAAI,CAAC,SAAS;;;AAIf,IAAA,0BAA0B,CAAC,QAAkB,EAAA;AACnD,QAAA,MAAM,KAAK,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QACxC,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI;;AAGjD,IAAA,oBAAoB,CAC1B,SAA0D,EAAA;AAE1D,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAC;AAAE,YAAA,OAAO,EAAE;;;;;;QAM1F,OAAO,OAAO,CACZ,gBAAgB,CACd,SAAS,EACT,CAAC,QAAkB,KAAK,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,IAAI,EAAE,CACxE,CACF;;AAGK,IAAA,sBAAsB,CAC5B,SAA0D,EAAA;AAE1D,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAC;AAAE,YAAA,OAAO,EAAE;AAE1F,QAAA,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,SAAS,CAAC;QACtD,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAAC;QAC/D,MAAM,mBAAmB,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,SAAS,CAAC;QACjE,MAAM,KAAK,GAAe,EAAE;AAC5B,QAAA,MAAM,uBAAuB,GAAG,IAAI,GAAG,EAAY;;;;;AAMnD,QAAA,YAAY,CAAC,mBAAmB,EAAE,CAAC,QAAa,KAAI;AAClD,YAAA,MAAM,KAAK,GAAQ,gBAAgB,CAAC,QAAQ,CAAC;YAC7C,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC5C,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACvC,oBAAA,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC;;;;AAIlC,oBAAA,KAAK,CAAC,OAAO,CAAC,EAAC,GAAG,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAC,CAAC;;;iBAEvC;AACL,gBAAA,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;;AAE3B,SAAC,CAAC;AACF,QAAA,OAAO,KAAK;;AAGN,IAAA,oBAAoB,CAC1B,SAA0D,EAAA;QAE1D,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,CAAC;;IAGhD,6BAA6B,CAAC,WAAsB,EAAE,KAAa,EAAA;AACzE,QAAA,MAAM,GAAG,GAAI,WAAmB,CAAC,KAAK,CAAC;AACvC,QAAA,IAAI,GAAG,IAAI,GAAG,CAAC,iBAAiB,EAAE;AAChC,YAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC;AAExC,YAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,iBAAiB;AACtC,YAAA,MAAM,kBAAkB,GAAG,CAAC,SAAqB,KAAK,IAAI,CAAC,sBAAsB,CAAC,SAAS,CAAC;YAC5F,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,KAAK,EAAE,mBAAmB,CAAC;AACnE,YAAA,GAAG,CAAC,iBAAiB,GAAG,CAAC,KAAwB,KAAK,QAAQ,CAAC,KAAK,EAAE,kBAAkB,CAAC;;;AAG9F;AAED,SAAS,aAAa,GAAA;IACpB,OAAO;QACL,MAAM,EAAE,IAAI,gBAAgB,EAAE;QAC9B,SAAS,EAAE,IAAI,iBAAiB,EAAE;QAClC,SAAS,EAAE,IAAI,iBAAiB,EAAE;QAClC,IAAI,EAAE,IAAI,YAAY,EAAE;KACzB;AACH;AAEA,SAAS,qBAAqB,CAAI,KAAc,EAAA;AAC9C,IAAA,MAAM,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC;AAClC,IAAA,OAAO,CAAC,CAAC,GAAG,EAAE,UAAU;AAC1B;AAIA,SAAS,eAAe,CAAC,KAAoB,EAAA;AAC3C,IAAA,OAAQ,KAAa,CAAC,IAAI,IAAI,IAAI;AACpC;AAEA,SAAS,cAAc,CAAI,KAAc,EAAA;AACvC,IAAA,OAAO,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC;AACrC;AAEA,SAAS,UAAU,CAAI,KAAc,EAAA;AACnC,IAAA,OAAO,cAAc,CAAC,KAAK,CAAC;AAC9B;AAEA,SAAS,aAAa,CAAI,OAAsB,EAAA;AAC9C,IAAA,OAAO,OAAO,YAAY,QAAQ,GAAG,OAAO,EAAE,GAAG,OAAO;AAC1D;AAEA,SAAS,OAAO,CAAI,MAAa,EAAA;IAC/B,MAAM,GAAG,GAAQ,EAAE;AACnB,IAAA,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACvB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,CAAI,KAAK,CAAC,CAAC;;aACzB;AACL,YAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;;AAEnB,KAAC,CAAC;AACF,IAAA,OAAO,GAAG;AACZ;AAEA,SAAS,UAAU,CAAI,KAAQ,EAAA;AAC7B,IAAA,OAAO,KAAK;AACd;AAOA,SAAS,gBAAgB,CACvB,SAAyD,EACzD,QAAqC,UAAU,EAAA;IAE/C,MAAM,GAAG,GAAU,EAAE;AACrB,IAAA,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;AAC9B,QAAA,IAAI,sBAAsB,CAAC,QAAQ,CAAC,EAAE;AACpC,YAAA,QAAQ,GAAG,QAAQ,CAAC,UAAU;;AAEhC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,GAAG,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;;aACzC;YACL,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;;;AAG7B,IAAA,OAAO,GAAG;AACZ;AAEA,SAAS,gBAAgB,CAAC,QAAkB,EAAE,KAAa,EAAA;IACzD,OAAO,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAK,QAAgB,CAAC,KAAK,CAAC;AAC7E;AAEA,SAAS,gBAAgB,CAAC,QAAkB,EAAA;IAC1C,OAAO,gBAAgB,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,QAAQ;AAC1D;AAEA,SAAS,qBAAqB,CAAC,KAAU,EAAA;AACvC,IAAA,OAAO,KAAK,CAAC,cAAc,CAAC,UAAU,CAAC;AACzC;AAEA,SAAS,YAAY,CAAI,MAAW,EAAE,EAAmC,EAAA;AACvE,IAAA,KAAK,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;QACjD,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;;AAExB;AAEA,SAAS,gBAAgB,CAAC,IAAY,EAAE,YAAoB,EAAA;IAC1D,OAAO,IAAI,KAAK,CAAC,CAAA,EAAG,IAAI,CAAwB,qBAAA,EAAA,YAAY,CAAoC,kCAAA,CAAA,CAAC;AACnG;AAEA,MAAM,cAAc,CAAA;AACE,IAAA,OAAA;AAApB,IAAA,WAAA,CAAoB,OAAwB,EAAA;QAAxB,IAAO,CAAA,OAAA,GAAP,OAAO;;AAE3B,IAAA,iBAAiB,CAAI,UAAmB,EAAA;AACtC,QAAA,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,UAAU,CAAC;AAC7C,QAAA,OAAO,IAAIO,eAAiB,CAAC,UAAU,CAAC;;IAG1C,MAAM,kBAAkB,CAAI,UAAmB,EAAA;QAC7C,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,UAAU,CAAC;AACpD,QAAA,OAAO,IAAIA,eAAiB,CAAC,UAAU,CAAC;;AAG1C,IAAA,iCAAiC,CAAI,UAAmB,EAAA;QACtD,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC;QAC1D,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAA6B,CAAC;AAC7F,QAAA,OAAO,IAAI,4BAA4B,CAAC,eAAe,EAAE,kBAAkB,CAAC;;IAG9E,MAAM,kCAAkC,CACtC,UAAmB,EAAA;QAEnB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;QACjE,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,UAA6B,CAAC;AAC7F,QAAA,OAAO,IAAI,4BAA4B,CAAC,eAAe,EAAE,kBAAkB,CAAC;;AAG9E,IAAA,UAAU;IAEV,aAAa,CAAC,IAAe,EAAA;AAE7B,IAAA,WAAW,CAAC,UAAqB,EAAA;AAC/B,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC;QAClE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS;;AAExC;;AC3qCD;AACA;AACA;AA8JA,IAAI,kBAAkB,GAAG,CAAC;AAE1B;;;;AAIG;SACa,UAAU,GAAA;IACxB,OAAO,WAAW,CAAC,QAAQ;AAC7B;AAEA;;;;;;AAMG;MACU,WAAW,CAAA;AACd,IAAA,OAAO,SAAS,GAAuB,IAAI;AAEnD,IAAA,WAAW,QAAQ,GAAA;AACjB,QAAA,QAAQ,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,IAAI,WAAW,EAAE;;AAG5E;;;AAGG;IACK,OAAO,2BAA2B;AAE1C;;;AAGG;IACK,OAAO,wCAAwC;AAEvD;;;AAGG;IACK,OAAO,0CAA0C;AAEzD;;;AAGG;AACK,IAAA,wBAAwB;AAEhC;;;AAGG;IACK,2BAA2B,GAAG,4BAA4B;AAElE;;;AAGG;AACK,IAAA,qCAAqC;AAE7C;;;AAGG;AACK,IAAA,uCAAuC;AAE/C;;;AAGG;AACK,IAAA,qCAAqC;AAE7C;;;AAGG;AACK,IAAA,uCAAuC;AAE/C;;;;;;;;;;;;AAYG;AACH,IAAA,OAAO,mBAAmB,CACxB,QAAiC,EACjC,QAAqB,EACrB,OAAgC,EAAA;AAEhC,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ;QACpC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC;AACxD,QAAA,OAAO,OAAO;;AAGhB;;;;AAIG;AACH,IAAA,OAAO,oBAAoB,GAAA;AACzB,QAAA,WAAW,CAAC,QAAQ,CAAC,oBAAoB,EAAE;;IAG7C,OAAO,iBAAiB,CAAC,MAA6C,EAAA;QACpE,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC;;AAGvD;;;AAGG;IACH,OAAO,sBAAsB,CAAC,SAA6B,EAAA;QACzD,OAAO,WAAW,CAAC,QAAQ,CAAC,sBAAsB,CAAC,SAAS,CAAC;;AAG/D;;;;AAIG;AACH,IAAA,OAAO,iBAAiB,GAAA;AACtB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,EAAE;;AAGjD,IAAA,OAAO,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAAA;QAC7E,OAAO,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC;;AAGhE,IAAA,OAAO,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;QAClF,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC;;AAGpE,IAAA,OAAO,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;QAClF,OAAO,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC;;AAGpE,IAAA,OAAO,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAA;QACnE,OAAO,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC;;AAG1D,IAAA,OAAO,gBAAgB,CAAC,SAAoB,EAAE,QAAgB,EAAA;QAC5D,OAAO,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC;;AAGnE;;;;;AAKG;AACH,IAAA,OAAO,kCAAkC,CAAC,SAAoB,EAAE,QAAgB,EAAA;QAC9E,OAAO,WAAW,CAAC,QAAQ,CAAC,kCAAkC,CAAC,SAAS,EAAE,QAAQ,CAAC;;AAWrF,IAAA,OAAO,gBAAgB,CACrB,KAAU,EACV,QAIC,EAAA;QAED,OAAO,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC;;AAgB/D,IAAA,OAAO,MAAM,CACX,KAAuB,EACvB,aAAwB,EACxB,OAAuB,EAAA;AAEvB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO,CAAC;;AAGnE;;;;AAIG;IACH,OAAO,qBAAqB,CAAI,EAAW,EAAA;QACzC,OAAO,WAAW,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,CAAC;;IAGvD,OAAO,eAAe,CAAI,SAAkB,EAAA;QAC1C,OAAO,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC;;AAGxD,IAAA,OAAO,kBAAkB,GAAA;AACvB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,kBAAkB,EAAE;;AAGlD,IAAA,OAAO,OAAO,CAAC,MAAa,EAAE,EAAY,EAAE,OAAa,EAAA;AACvD,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,OAAO,CAAC;;AAG1D,IAAA,WAAW,QAAQ,GAAA;AACjB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ;;AAGtC,IAAA,WAAW,QAAQ,GAAA;AACjB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,QAAQ;;AAGtC,IAAA,OAAO,YAAY,GAAA;AACjB,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE;;AAGpC,IAAA,OAAO,IAAI,GAAA;AACT,QAAA,OAAO,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE;;;IAKpC,QAAQ,GAAgB,IAAK;IAC7B,QAAQ,GAA4B,IAAK;IAEjC,SAAS,GAA2B,IAAI;IACxC,cAAc,GAA4B,IAAI;IAE9C,eAAe,GAA4B,EAAE;AAErD;;;;AAIG;IACH,wBAAwB,GAAG,KAAK;AAEhC;;;;;;;;;;;;AAYG;AACH,IAAA,mBAAmB,CACjB,QAAiC,EACjC,QAAqB,EACrB,OAAgC,EAAA;QAEhC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC;;AAGjF,QAAA,WAAW,CAAC,2BAA2B,GAAG,OAAO,EAAE,QAAQ;AAE3D,QAAA,WAAW,CAAC,wCAAwC,GAAG,OAAO,EAAE,sBAAsB;AAEtF,QAAA,WAAW,CAAC,0CAA0C,GAAG,OAAO,EAAE,wBAAwB;AAE1F,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;;;;;QAMlE,mCAAmC,CAAC,IAAI,CAAC;;AAG3C;;;;AAIG;IACH,oBAAoB,GAAA;QAClB,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAK;AACrB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAK;AACrB,QAAA,WAAW,CAAC,2BAA2B,GAAG,SAAS;QACnD,mCAAmC,CAAC,KAAK,CAAC;;IAG5C,kBAAkB,GAAA;QAChB,IAAI,CAAC,8BAA8B,EAAE;AACrC,QAAA,uBAAuB,EAAE;AACzB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,CAAC,oBAAoB,EAAE;;AAEtC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC;;AAElE,QAAAC,4BAA2B,CACzB,IAAI,CAAC,qCAAqC,IAAI,iCAAiC,CAChF;;AAED,QAAAC,6BAA4B,CAC1B,IAAI,CAAC,uCAAuC,IAAI,mCAAmC,CACpF;;;;AAKD,QAAA,IAAI;YACF,IAAI,CAAC,qBAAqB,EAAE;;gBACpB;AACR,YAAA,IAAI;AACF,gBAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE;oBACtC,IAAI,CAAC,qBAAqB,EAAE;;;oBAEtB;AACR,gBAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,gBAAA,IAAI,CAAC,wBAAwB,GAAG,SAAS;AACzC,gBAAA,IAAI,CAAC,qCAAqC,GAAG,SAAS;AACtD,gBAAA,IAAI,CAAC,uCAAuC,GAAG,SAAS;AACxD,gBAAA,IAAI,CAAC,2BAA2B,GAAG,4BAA4B;;;AAGnE,QAAA,OAAO,IAAI;;AAGb,IAAA,iBAAiB,CAAC,MAA6C,EAAA;AAC7D,QAAA,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;AACzB,YAAA,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC;;AAGvE,QAAA,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC;;AAEtD,QAAA,OAAO,IAAI;;AAGb,IAAA,sBAAsB,CAAC,SAA6B,EAAA;AAClD,QAAA,IAAI,CAAC,qBAAqB,CAAC,gCAAgC,EAAE,2BAA2B,CAAC;;;;;QAMzF,IAAI,CAAC,8BAA8B,EAAE;;;AAIrC,QAAA,IAAI,CAAC,wBAAwB,GAAG,SAAS,CAAC,QAAQ;AAClD,QAAA,IAAI,CAAC,qCAAqC,GAAG,SAAS,CAAC,sBAAsB;AAC7E,QAAA,IAAI,CAAC,uCAAuC,GAAG,SAAS,CAAC,wBAAwB;QACjF,IAAI,CAAC,2BAA2B,GAAG,SAAS,CAAC,kBAAkB,IAAI,4BAA4B;;;AAG/F,QAAA,IAAI,CAAC,qCAAqC,GAAGC,4BAA2B,EAAE;AAC1E,QAAAF,4BAA2B,CAAC,IAAI,CAAC,iCAAiC,EAAE,CAAC;AACrE,QAAA,IAAI,CAAC,uCAAuC,GAAGG,6BAA4B,EAAE;AAC7E,QAAAF,6BAA4B,CAAC,IAAI,CAAC,mCAAmC,EAAE,CAAC;AACxE,QAAA,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,SAAS,CAAC;AAC/C,QAAA,OAAO,IAAI;;IAGb,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;;AAY1C,IAAA,MAAM,CAAI,KAAuB,EAAE,aAAwB,EAAE,OAAuB,EAAA;AAClF,QAAA,IAAK,KAAiB,KAAK,OAAO,EAAE;AAClC,YAAA,OAAO,IAAW;;QAEpB,MAAM,SAAS,GAAG,EAAkB;AACpC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC;QACzE,OAAO,MAAM,KAAK;AAChB,cAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,OAAO;cACzD,MAAM;;AAGZ,IAAA,qBAAqB,CAAI,EAAW,EAAA;QAClC,OAAO,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,EAAE,CAAC;;AAGpE,IAAA,OAAO,CAAC,MAAa,EAAE,EAAY,EAAE,OAAa,EAAA;AAChD,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAChD,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;;IAGlC,cAAc,CAAC,QAAmB,EAAE,QAAoC,EAAA;AACtE,QAAA,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC;QACxE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAChD,QAAA,OAAO,IAAI;;IAGb,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;QAC9E,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC;AACpD,QAAA,OAAO,IAAI;;IAGb,kCAAkC,CAAC,SAAoB,EAAE,QAAgB,EAAA;AACvE,QAAA,IAAI,CAAC,qBAAqB,CACxB,4CAA4C,EAC5C,6EAA6E,CAC9E;QACD,IAAI,CAAC,QAAQ,CAAC,kCAAkC,CAAC,SAAS,EAAE,QAAQ,CAAC;AACrE,QAAA,OAAO,IAAI;;IAGb,iBAAiB,CAAC,SAAoB,EAAE,QAAqC,EAAA;AAC3E,QAAA,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,6BAA6B,CAAC;QAC9E,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC;AACpD,QAAA,OAAO,IAAI;;IAGb,YAAY,CAAC,IAAe,EAAE,QAAgC,EAAA;AAC5D,QAAA,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,wBAAwB,CAAC;QACpE,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC1C,QAAA,OAAO,IAAI;;AAGb;;AAEG;IACH,gBAAgB,CACd,KAAU,EACV,QAA+D,EAAA;AAE/D,QAAA,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC;QACnE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC/C,QAAA,OAAO,IAAI;;IAGb,gBAAgB,CAAC,SAAoB,EAAE,QAAgB,EAAA;AACrD,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,EAAC,GAAG,EAAE,EAAC,QAAQ,EAAE,WAAW,EAAE,IAAK,EAAC,EAAC,CAAC;;AAGjF,IAAA,eAAe,CAAI,IAAa,EAAA;QAC9B,MAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;AAChE,QAAA,MAAM,QAAQ,GAAG,CAAA,IAAA,EAAO,kBAAkB,EAAE,EAAE;AAC9C,QAAA,qBAAqB,CAAC,iBAAiB,CAAC,QAAQ,CAAC;AAEjD,QAAA,IAAI,uBAAuB,CAAC,IAAI,CAAC,EAAE;AACjC,YAAA,MAAM,IAAI,KAAK,CACb,cAAc,IAAI,CAAC,IAAI,CAA6B,2BAAA,CAAA;AAClD,gBAAA,CAAA,2EAAA,CAA6E,CAChF;;AAGH,QAAA,MAAM,YAAY,GAAI,IAAY,CAAC,IAAI;QAEvC,IAAI,CAAC,YAAY,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,CAAkB,eAAA,EAAA,SAAS,CAAC,IAAI,CAAC,CAA0B,wBAAA,CAAA,CAAC;;AAG9E,QAAA,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,YAAY,CAAC;QAC3D,MAAM,aAAa,GAAG,MAAK;YACzB,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAC1C,QAAQ,CAAC,IAAI,EACb,EAAE,EACF,CAAA,CAAA,EAAI,QAAQ,CAAE,CAAA,EACd,IAAI,CAAC,aAAa,CACA;AACpB,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,IAAI,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC7E,SAAC;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAC7D,QAAA,MAAM,MAAM,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC;AAC1D,QAAA,MAAM,OAAO,GAAG,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,aAAa,EAAE;AACpE,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC;AAClC,QAAA,OAAO,OAAO;;AAGhB;;;AAGG;AACH,IAAA,IAAY,QAAQ,GAAA;AAClB,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,gDAAA,CAAkD,CAAC;;QAErE,OAAO,IAAI,CAAC,SAAS;;AAGvB;;;AAGG;AACH,IAAA,IAAY,aAAa,GAAA;AACvB,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;;QAEhD,OAAO,IAAI,CAAC,cAAc;;IAGpB,qBAAqB,CAAC,UAAkB,EAAE,iBAAyB,EAAA;AACzE,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAChC,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,OAAA,EAAU,iBAAiB,CAAuD,qDAAA,CAAA;gBAChF,CAAmD,gDAAA,EAAA,UAAU,CAAK,GAAA,CAAA,CACrE;;;AAIL;;;;;;;;;;;AAWG;IACK,8BAA8B,GAAA;;;QAGpC,IAAI,CAAC,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;AAClE,YAAA,uCAAuC,EAAE;;AAE3C,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI;;IAG9B,qBAAqB,GAAA;QAC3B,IAAI,UAAU,GAAG,CAAC;QAClB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,KAAI;AACvC,YAAA,IAAI;gBACF,OAAO,CAAC,OAAO,EAAE;;YACjB,OAAO,CAAC,EAAE;AACV,gBAAA,UAAU,EAAE;AACZ,gBAAA,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE;oBACjD,SAAS,EAAE,OAAO,CAAC,iBAAiB;AACpC,oBAAA,UAAU,EAAE,CAAC;AACd,iBAAA,CAAC;;AAEN,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,eAAe,GAAG,EAAE;QAEzB,IAAI,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE;AACxD,YAAA,MAAM,KAAK,CACT,CAAA,EAAG,UAAU,CAAA,CAAA,EAAI,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,YAAY,CAAG,CAAA,CAAA;AAC/D,gBAAA,CAAA,2BAAA,CAA6B,CAChC;;;IAIL,2BAA2B,GAAA;AACzB,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB;AACrD,QAAA,MAAM,kBAAkB,GAAG,WAAW,CAAC,2BAA2B;;AAGlE,QAAA,IAAI,CAAC,eAAe,IAAI,CAAC,kBAAkB,EAAE;AAC3C,YAAA,OAAO,0CAA0C;;;QAInD,QACE,eAAe,EAAE,aAAa;AAC9B,YAAA,kBAAkB,EAAE,aAAa;AACjC,YAAA,IAAI,CAAC,2BAA2B,EAAE;;IAItC,iCAAiC,GAAA;;QAE/B,QACE,IAAI,CAAC,qCAAqC;AAC1C,YAAA,WAAW,CAAC,wCAAwC;AACpD,YAAA,iCAAiC;;IAIrC,mCAAmC,GAAA;;QAEjC,QACE,IAAI,CAAC,uCAAuC;AAC5C,YAAA,WAAW,CAAC,0CAA0C;AACtD,YAAA,mCAAmC;;IAIvC,2BAA2B,GAAA;AACzB,QAAA,QACE,IAAI,CAAC,wBAAwB,EAAE,gBAAgB;YAC/C,WAAW,CAAC,2BAA2B,EAAE,gBAAgB;AACzD,YAAA,0CAA0C;;IAI9C,qBAAqB,GAAA;QACnB,OAAO,IAAI,CAAC,2BAA2B;;IAGzC,qBAAqB,GAAA;;AAEnB,QAAA,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,EAAE;YAChC;;;;QAIF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;AACvD,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;;QAC7B,OAAO,CAAC,EAAE;AACV,YAAA,IAAI,IAAI,CAAC,2BAA2B,EAAE,EAAE;AACtC,gBAAA,MAAM,CAAC;;iBACF;AACL,gBAAA,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE;AACxD,oBAAA,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;AACvC,oBAAA,UAAU,EAAE,CAAC;AACd,iBAAA,CAAC;;;gBAEI;AACR,YAAA,YAAY,CAAC,qBAAqB,IAAI;;;AAI1C;;;;AAIG;IACH,YAAY,GAAA;QACV,IAAI,CAAC,IAAI,EAAE;;AAGb;;;;AAIG;IACH,IAAI,GAAA;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;AAC1C,QAAA,IAAI;;;;;AAKD,YAAA,MAAc,CAAC,mBAAmB,GAAG,IAAI;YAC1C,MAAM,CAAC,IAAI,EAAE;;gBACL;AACP,YAAA,MAAc,CAAC,mBAAmB,GAAG,KAAK;;;;AAKjD;;;;;;;;AAQG;AACI,MAAM,OAAO,GAAkB;AAEtC;;;;;;;;;;;;;;;;;;;;;AAqBG;AACa,SAAA,MAAM,CAAC,MAAa,EAAE,EAAY,EAAA;AAChD,IAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ;;IAEpC,OAAO,YAAA;QACL,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC;AAC1C,KAAC;AACH;AAEA;;AAEG;MACU,kBAAkB,CAAA;AACT,IAAA,UAAA;AAApB,IAAA,WAAA,CAAoB,UAAoC,EAAA;QAApC,IAAU,CAAA,UAAA,GAAV,UAAU;;IAEtB,UAAU,GAAA;AAChB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE;QACnC,IAAI,SAAS,EAAE;AACb,YAAA,WAAW,CAAC,sBAAsB,CAAC,SAAS,CAAC;;;IAIjD,MAAM,CAAC,MAAa,EAAE,EAAY,EAAA;QAChC,MAAM,IAAI,GAAG,IAAI;;QAEjB,OAAO,YAAA;YACL,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,SAAC;;AAEJ;AAOe,SAAA,UAAU,CACxB,SAA6B,EAC7B,EAAoB,EAAA;IAEpB,IAAI,EAAE,EAAE;;QAEN,OAAO,YAAA;AACL,YAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ;YACpC,IAAI,SAAS,EAAE;AACb,gBAAA,OAAO,CAAC,sBAAsB,CAAC,SAAS,CAAC;;AAE3C,YAAA,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;AACvB,SAAC;;IAEH,OAAO,IAAI,kBAAkB,CAAC,MAAM,SAAS,CAAC;AAChD;;ACt4BA;;;;AAIG;MACU,cAAc,CAAA;AACzB;;;AAGG;IACc,UAAU,GAAiC,EAAE;AAE9D;;AAEG;IACK,iBAAiB,GAAG,CAAC;AAE7B;;;AAGG;IACH,aAAa,GAAqC,IAAI;AAEtD;;;AAGG;AACc,IAAA,cAAc,GAAG,IAAI,GAAG,EAAoC;AAE7E;;;AAGG;AACK,IAAA,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE;AAEzC;;;AAGG;IACK,qBAAqB,GAAG,CAAC;AAEjC;;;AAGG;IACK,qBAAqB,GAAG,KAAK;;IAG7B,kBAAkB,GAAG,IAAI;AAEjC;;;AAGG;AACH,IAAA,WAAW;;IAGH,MAAM,GAAG,CAAC;;IAGV,OAAO,GAAG,CAAC;;IAGX,QAAQ,GAAG,KAAK;;AAGxB,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC;;AAGhD,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,CAAC;;AAGnC,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;;AAG3C,IAAA,iBAAiB;AACjB,IAAA,OAAO;AAIxB,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAGrB,WAAY,CAAA,GAAa,EAAE,QAAyB,EAAA;AAClD,QAAA,IAAI,CAAC,iBAAiB,GAAG,MAAK;AAC5B,YAAA,IAAI;;;;;;AAMF,gBAAA,OAAO,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC;;AAC/B,YAAA,MAAM;;;;gBAIN,OAAO,IAAI,WAAW,EAAE;;AAE5B,SAAC;QACD,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC/D,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE;;AAE3C,QAAA,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC;;AAG1C;;AAEG;IACH,yBAAyB,CACvB,GAAoB,EACpB,OAAA,GAAoD,EAAC,YAAY,EAAE,IAAI,EAAC,EAAA;AAExE,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AAC5B,YAAA,MAAM,IAAI,KAAK,CACb,0DAA0D,GAAG,yBAAyB,CACvF;;QAEH,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;AAC7F,YAAA,KAAK,EAAE,CAAC;YACR,GAAG,EAAE,mBAAmB,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,EAAE,EAAE,mBAAmB,EAAE,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACpD,YAAA,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,OAAO,EAAE,YAAY;YACnC,KAAK,EAAE,OAAO,CAAC,KAAK;AACrB,SAAA,CAAC;;;IAIJ,4BAA4B,GAAA;QAC1B,OAAO,IAAI,CAAC,kBAAkB;;AAGhC;;;AAGG;AACH,IAAA,kCAAkC,CAAC,qBAA8B,EAAA;AAC/D,QAAA,IAAI,CAAC,qBAAqB,GAAG,qBAAqB;;;IAIpD,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;;;IAIhC,QAAQ,CAAC,GAAW,EAAE,OAAmC,EAAA;QACvD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC;AAC/C,QAAA,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC;AAElD,QAAA,IAAI,cAAoC;QACxC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM,EAAE;;YAEnD,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,EAAE;gBAC3C,cAAc,GAAG,SAAS;;iBACrB;gBACL,cAAc,GAAG,MAAM;;;aAEpB;AACL,YAAA,cAAc,GAAG,OAAO,CAAC,OAAO;;QAGlC,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;AAE/C,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;AAChD,YAAA,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE;YACrB,KAAK,EAAE,OAAO,EAAE,KAAK;AACrB,YAAA,YAAY,EAAE,UAAU;AACxB,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC;QAEjD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;YAC9D,cAAc;AACd,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,aAAa,EAAE,KAAK;YACpB,UAAU;YACV,IAAI,EAAE,OAAO,EAAE,IAAI;AACpB,SAAA,CAAC;QACF,IAAI,CAAC,WAAW,EAAE;AAChB,YAAA,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,aAAc,CAAC;;QAG5E,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B;;;AAIH,IAAA,SAAS,CAAC,IAAa,EAAE,KAAa,EAAE,GAAY,EAAA;QAClD,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;;;AAInD,IAAA,YAAY,CAAC,IAAa,EAAE,KAAa,EAAE,GAAY,EAAA;QACrD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;;AAG9C,IAAA,kBAAkB,CACxB,cAAoC,EACpC,IAAa,EACb,MAAc,EACd,GAAY,EAAA;QAEZ,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC;QAC/C,MAAM,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,GAAG,OAAO;QAElE,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;AAE/C,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;AAChD,YAAA,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE;YACrB,YAAY,EAAE,IAAI;AAClB,YAAA,YAAY,EAAE,IAAI;YAClB,KAAK,EAAE,SAAS;AACjB,SAAA,CAAC;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC;QAEjD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;YAC9D,cAAc;AACd,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,aAAa,EAAE,KAAK;YACpB,UAAU;AACX,SAAA,CAAC;QACF,IAAI,WAAW,EAAE;YACf;;AAEF,QAAA,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,aAAc,CAAC;;;IAI5E,UAAU,CAAC,GAAW,EAAE,OAA2B,EAAA;QACjD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,aAAa,EAAE,mBAAmB,CAAC;YACzE,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;YAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC;YACzB,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC;YACxB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT;;AAEH,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE;YAC/B,OAAO;gBACL,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;gBAC7C,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;aAC7C;;QAEH,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACtC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAE;YAC1D,OAAO;gBACL,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,QAAQ,EAAE,cAAc,CAAC,QAAQ;aAClC;;QAGH,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC;AACrF,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;YAChD,GAAG,EAAE,KAAK,CAAC,GAAI;AACf,YAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACvB,YAAA,YAAY,EAAE,KAAK,CAAC,eAAe,EAAE;YACrC,GAAG,EAAE,KAAK,CAAC,GAAG;YACd,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,KAAK;AACxC,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC;QACjD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC;AAC1C,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;YACrC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;AAC9D,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,YAAY,EAAE,IAAI;;AAElB,gBAAA,aAAa,EAAE,KAAK;gBACpB,UAAU;gBACV,IAAI,EAAE,OAAO,EAAE,IAAI;AACpB,aAAA,CAAC;YACF,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAc,CAAC;;AAE/C,SAAC,CAAC;QACF,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B;;;AAIH,IAAA,IAAI,CAAC,OAA2B,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;YAChC,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;YAC5E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;YAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC;YACzB,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC;YACxB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT;;AAEH,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC;;;AAI5C,IAAA,OAAO,CAAC,OAA2B,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,mBAAmB,EAAE,mBAAmB,CAAC;YAC/E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;YAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC;YACzB,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC;YACxB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT;;AAEH,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC;;AAG5C;;;;;;AAMG;AACH,IAAA,EAAE,CAAC,SAAiB,EAAA;AAClB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,GAAG,SAAS;AAC1D,QAAA,IAAI,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;YAC5D;;AAEF,QAAA,IAAI,CAAC,qBAAqB,GAAG,WAAW;AACxC,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;;AAErB,YAAA,IAAI,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;gBAC5D;;YAEF,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC;YAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;YAC1C,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC;AACrF,YAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;gBAChD,GAAG,EAAE,KAAK,CAAC,GAAI;AACf,gBAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACvB,gBAAA,YAAY,EAAE,KAAK,CAAC,eAAe,EAAE;gBACrC,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,aAAA,CAAC;AACF,YAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,CAAC,IAAI,CAAC;YACjD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;AAC9D,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,YAAY,EAAE,IAAI;;AAElB,gBAAA,aAAa,EAAE,KAAK;gBACpB,UAAU;AACX,aAAA,CAAC;YACF,IAAI,CAAC,WAAW,EAAE;AAChB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAc,CAAC;;AAE/C,SAAC,CAAC;;;AAII,IAAA,YAAY,CAAC,SAAqB,EAAA;AACxC,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,SAAS,EAAE;YACX;;;;;QAMF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAK;AAChD,YAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;gBACnC,UAAU,CAAC,MAAK;AACd,oBAAA,OAAO,EAAE;AACT,oBAAA,SAAS,EAAE;AACb,iBAAC,CAAC;AACJ,aAAC,CAAC;AACJ,SAAC,CAAC;;;AAIJ,IAAA,gBAAgB,CACd,IAAY,EACZ,QAA4C,EAC5C,OAA2C,EAAA;QAE3C,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;;;AAI5D,IAAA,mBAAmB,CACjB,IAAY,EACZ,QAA4C,EAC5C,OAAwC,EAAA;QAExC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;;;AAI/D,IAAA,aAAa,CAAC,KAAY,EAAA;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC;;;IAI9C,OAAO,GAAA;;AAEL,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE;AAC3C,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI;;;IAItB,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ;;IAGtB,sBAAsB,CAAC,YAAuC,EAAE,MAAc,EAAA;AAC5E,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,YAAY,EAAE;YACvC;;QAEF,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE;YACrD;;QAEF,MAAM,WAAW,GAAG,MAAM,IAAI,IAAI,YAAY,CAAC,oBAAoB,EAAE,YAAY,CAAC;AAClF,QAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC;;AAGxC;;;AAGG;AACK,IAAA,iBAAiB,CACvB,WAAsC,EACtC,MAAgC,EAChC,OAAgC,EAAA;;;AAIhC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;AAC/B,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,sBAAsB,CACzB,IAAI,CAAC,aAAa,EAClB,IAAI,YAAY,CAAC,4CAA4C,EAAE,YAAY,CAAC,CAC7E;;;;;;QAMH,MAAM,oCAAoC,GAAG,qBAAqB,CAAC;YACjE,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,WAAW;YACX,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,MAAM;AACP,SAAA,CAAC;QACF,OAAO,CAAC,oCAAoC;;AAG9C;;;;;AAKG;AACH,IAAA,wBAAwB,CAAC,aAAwC,EAAA;AAC/D,QAAA,IAAI,CAAC,gDAAgD,CAAC,aAAa,CAAC;;AAGtE;;;;;;;;;;AAUG;AACH,IAAA,iBAAiB,CAAC,aAAwC,EAAA;AACxD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAI;AACrC,QAAA,IAAI,CAAC,gDAAgD,CAAC,aAAa,CAAC;;QAEpE,MAAM,aAAa,GAAG,mBAAmB,CAAC;AACxC,YAAA,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC,eAAe,EAAE;AACnD,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC;AACzC,QAAA,IAAI,aAAa,CAAC,UAAU,EAAE;AAC5B,YAAA,MAAM,eAAe,GAAG,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC;AAC7E,YAAA,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC;;;AAI/C;;;AAGG;AACH,IAAA,gDAAgD,CAAC,EAC/C,WAAW,EACX,cAAc,EACd,MAAM,GACoB,EAAA;AAC1B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY;QACvC,MAAM,YAAY,GAAG,EAAE;AACvB,QAAA,IAAI,cAAc,KAAK,UAAU,EAAE;AACjC,YAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,KAAK;AAC1C,YAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,CAAC,EAAE;AACjC,gBAAA,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC;;;AAE9C,aAAA,IAAI,cAAc,KAAK,MAAM,EAAE;YACpC,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACpD,YAAA,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;;AAC/D,aAAA,IAAI,cAAc,KAAK,SAAS,EAAE;AACvC,YAAA,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC;;QAElC,IAAI,cAAc,KAAK,MAAM,IAAI,cAAc,KAAK,SAAS,EAAE;AAC7D,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB;AACpC,YAAA,MAAM,GAAG,GACP,cAAc,KAAK;AACjB,kBAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;AACvB,mBAAG,aAAa,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AACpD,YAAA,MAAM,MAAM,GAAG,IAAI,0BAA0B,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,GAAG,EAAE;AAC/E,gBAAA,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACzB,GAAG;gBACH,KAAK;AACL,gBAAA,YAAY,EAAE,IAAI;AAClB,gBAAA,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;AAC7B,gBAAA,YAAY,EAAE,WAAW,CAAC,eAAe,EAAE;AAC5C,aAAA,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,MAAM;;AAElD,QAAA,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC;QAC1C,MAAM,uBAAuB,GAAG,2CAA2C,CAAC;AAC1E,YAAA,IAAI,EAAE,aAAa;AACnB,YAAA,cAAc,EAAE,cAAc;AAC/B,SAAA,CAAC;AACF,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC;AACvD,QAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;YACtC,WAAW,CAAC,OAAO,EAAE;;;;AAKjB,IAAA,SAAS,CAAC,GAAW,EAAA;AAC3B,QAAA,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;AAAE,gBAAA,OAAO,KAAK;;AAErC,QAAA,OAAO,SAAS;;AAGlB,IAAA,IAAI,UAAU;;IAEZ,QAA+D,EAAA;AAE/D,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;;AAIlC,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;IAGlC,IAAI,oBAAoB,CACtB,QACyE,EAAA;AAEzE,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC,IAAA,IAAI,oBAAoB,GAAA;AAEtB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC,IAAA,IAAI,iBAAiB;;IAEnB,QAAuD,EAAA;AAEvD,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;;AAIlC,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC,IAAA,IAAI,eAAe;;IAEjB,QAA4D,EAAA;AAE5D,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;;AAIlC,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;IAG1B,WAAW,GAAgC,IAAI;;IAEvD,IAAI,UAAU,CAAC,CAA8B,EAAA;AAC3C,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC;;AAEtB,IAAA,IAAI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;AAGzB,IAAA,kBAAkB,CAAC,QAA6C,EAAA;AAC9D,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAGlC,IAAA,MAAM,CAAC,QAAkC,EAAA;AACvC,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC;;AAEnC;AAWD;;AAEG;MACU,0BAA0B,CAAA;AAa3B,IAAA,WAAA;AACC,IAAA,GAAA;AAbF,IAAA,YAAY;AAEZ,IAAA,EAAE;AACF,IAAA,GAAG;AACH,IAAA,KAAK;AACG,IAAA,KAAK;AACL,IAAA,YAAY;;IAG7B,SAAS,GAA8D,IAAI;AAE3E,IAAA,WAAA,CACU,WAAwB,EACvB,GAAkB,EAC3B,EACE,EAAE,EACF,GAAG,EACH,KAAK,EACL,YAAY,EACZ,KAAK,EACL,YAAY,GAQb,EAAA;QAhBO,IAAW,CAAA,WAAA,GAAX,WAAW;QACV,IAAG,CAAA,GAAA,GAAH,GAAG;AAiBZ,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE;AACZ,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG;AACd,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY;AAChC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY;;IAGlC,QAAQ,GAAA;;QAEN,OAAO,IAAI,CAAC,KAAK,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAa,GAAG,IAAI,CAAC,KAAK;;IAGtF,eAAe,GAAA;;QAEb,OAAO,IAAI,CAAC;AACV,cAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC;AAC/C,cAAE,IAAI,CAAC,YAAY;;AAGvB,IAAA,gBAAgB,CACd,IAAY,EACZ,QAA4C,EAC5C,OAA2C,EAAA;QAE3C,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;;AAG5D,IAAA,mBAAmB,CACjB,IAAY,EACZ,QAA4C,EAC5C,OAAwC,EAAA;QAExC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC;;AAG/D,IAAA,aAAa,CAAC,KAAY,EAAA;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC;;;IAI9C,OAAO,GAAA;AACL,QAAA,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,UAAU,CAAC;AAC1C,QAAA,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC;;AAEhC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAK;;AAE3B;AAmCD;;;;;AAKG;AACH,SAAS,qBAAqB,CAAC,EAC7B,UAAU,EACV,YAAY,EACZ,aAAa,EACb,UAAU,EACV,cAAc,EACd,WAAW,EACX,IAAI,EACJ,YAAY,EACZ,MAAM,GAWP,EAAA;AACC,IAAA,MAAM,EAAC,UAAU,EAAC,GAAG,MAAM;AAE3B,IAAA,MAAM,oBAAoB,GAAG,IAAI,eAAe,EAAE;AAClD,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAC,CAE/D;AAED,IAAA,KAAK,CAAC,cAAc,GAAG,cAAc;AACrC,IAAA,KAAK,CAAC,WAAW,GAAG,WAAW;AAC/B,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY;AACjC,IAAA,KAAK,CAAC,aAAa,GAAG,aAAa;AACnC,IAAA,KAAK,CAAC,UAAU,GAAG,UAAU;AAC7B,IAAA,KAAK,CAAC,MAAM,GAAG,oBAAoB,CAAC,MAAM;AAC1C,IAAA,KAAK,CAAC,eAAe,GAAG,oBAAoB;AAC5C,IAAA,KAAK,CAAC,IAAI,GAAG,IAAI;AACjB,IAAA,KAAK,CAAC,kBAAkB,GAAG,IAAI;AAC/B,IAAA,KAAK,CAAC,cAAc,GAAG,IAAI;AAC3B,IAAA,KAAK,CAAC,iBAAiB,GAAG,MAAM;AAChC,IAAA,KAAK,CAAC,eAAe,GAAG,IAAI;AAC5B,IAAA,KAAK,CAAC,QAAQ,GAAG,IAAI;AACrB,IAAA,KAAK,CAAC,MAAM,GAAG,MAAM;AACrB,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY;IAEjC,IAAI,iBAAiB,GAAwE,EAAE;IAC/F,IAAI,QAAQ,GAA+B,EAAE;;AAG7C,IAAA,KAAK,CAAC,SAAS,GAAG,UAEhB,OAAgD,EAAA;AAEhD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,MAAM,IAAI,YAAY,CAAC,+CAA+C,EAAE,eAAe,CAAC;;AAE1F,QAAA,IAAI,CAAC,iBAAiB,GAAG,aAAa;AACtC,QAAA,KAAK,CAAC,YAAY,GAAG,IAAI;AACzB,QAAA,MAAM,gBAAgB,GAAG,OAAO,EAAE,gBAAgB;QAClD,IAAI,gBAAgB,EAAE;AACpB,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,gBAAA,MAAM,IAAI,YAAY,CACpB,wDAAwD,EACxD,mBAAmB,CACpB;;AAEH,YAAA,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAE1C,QAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,MAAM,IAAI,KAAK,CAAC,iBAAiB,KAAK,aAAa,EAAE;AACnF,YAAA,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC;;AAE9E,QAAA,KAAK,CAAC,iBAAiB,GAAG,aAAa;AACvC,QAAA,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO;QAChC,IAAI,OAAO,EAAE;AACX,YAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;;;QAGxB,KAAK,CAAC,kBAAkB,GAAG,OAAO,EAAE,UAAU,IAAI,KAAK,CAAC,kBAAkB;QAC1E,KAAK,CAAC,cAAc,GAAG,OAAO,EAAE,MAAM,IAAI,KAAK,CAAC,cAAc;AAChE,KAAC;;IAGD,KAAK,CAAC,MAAM,GAAG,YAAA;AACb,QAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,WAAW,EAAE;YAC3C,MAAM,IAAI,YAAY,CACpB,CAAkE,gEAAA,CAAA;gBAChE,CAA4E,0EAAA,CAAA,EAC9E,mBAAmB,CACpB;;QAEH,qBAAqB,CAAC,KAAK,CAAC;AAC9B,KAAC;;AAGD,IAAA,SAAS,QAAQ,CAAC,GAAW,EAAE,UAAqC,EAAE,EAAA;AACpE,QAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,MAAM,EAAE;AACtC,YAAA,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC;;AAElE,QAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,aAAa,EAAE;AAC7C,YAAA,MAAM,IAAI,YAAY,CACpB,0DAA0D,EAC1D,mBAAmB,CACpB;;AAEH,QAAA,IAAI,KAAK,CAAC,cAAc,KAAK,MAAM,IAAI,KAAK,CAAC,cAAc,KAAK,SAAS,EAAE;AACzE,YAAA,MAAM,IAAI,YAAY,CACpB,+DAA+D,EAC/D,mBAAmB,CACpB;;AAEH,QAAA,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,YAAY,CAAC,GAAI,CAAC;AACjE,QAAA,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;AAC/D,YAAA,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO;;AAExC,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;YACnC,KAAK,CAAC,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;;QAEzC,KAAK,CAAC,WAAW,CAAC,GAAG,GAAG,cAAc,CAAC,IAAI;AAC3C,QAAA,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AAClC,YAAA,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;;;;;AAM7B,IAAA,SAAS,MAAM,GAAA;AACb,QAAA,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACzB;;AAED,QAAA,UAAU,CAAC,UAA2C,EAAE,gBAAgB,EAAE;AAC3E,QAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,aAAa,EAAE;AAC7C,YAAA,KAAK,CAAC,iBAAiB,GAAG,WAAW;AACrC,YAAA,QAAQ,KAAK,CAAC,cAAc;AAC1B,gBAAA,KAAK,MAAM;gBACX,KAAK,SAAS,EAAE;AACd,oBAAA,UAAU,CAAC,wBAAwB,CAAC,KAAK,CAAC;oBAC1C;;gBAEF,KAAK,QAAQ,EAAE;AACb,oBAAA,UAAU,CAAC,gDAAgD,CAAC,KAAK,CAAC;oBAClE;;gBAEF,KAAK,UAAU,EAAE;AACf,oBAAA,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBACnC;;;;AAIN,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;AACzD,QAAA,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;;AAEtC,QAAA,OAAO,CAAC,GAAG,CAAC,YAAY;aACrB,IAAI,CAAC,MAAK;;;AAGT,YAAA,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;gBACzB;;AAEF,YAAA,IAAI,KAAK,KAAK,UAAU,CAAC,aAAa,EAAE;gBACtC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,WAAW,EAAE;oBAChD,MAAM,CAAC,cAAc,CACnB,IAAI,YAAY,CAAC,iDAAiD,EAAE,YAAY,CAAC,CAClF;;gBAEH;;AAEF,YAAA,UAAU,CAAC,aAAa,GAAG,IAAI;AAC/B,YAAA,qBAAqB,CAAC,KAAK,EAAE,IAAI,CAAC;AAClC,YAAA,MAAM,oBAAoB,GAAG,IAAI,KAAK,CAAC,iBAAiB,EAAE;AACxD,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,UAAU,EAAE,KAAK;AAClB,aAAA,CAAC;AACF,YAAA,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAC1D,MAAM,CAAC,eAAe,EAAE;AACvB,YAAA,UAAU,CAAC,UAA2C,EAAE,eAAe,EAAE;AAC1E,YAAA,UAAU,CAAC,UAAU,GAAG,IAAI;AAC9B,SAAC;AACA,aAAA,KAAK,CAAC,CAAC,MAAM,KAAI;YAChB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE;AACzC,gBAAA,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;;AAExB,SAAC,CAAC;;;;;AAMN,IAAA,KAAK,CAAC,MAAM,GAAG,UAA2C,MAAa,EAAA;AACrE,QAAA,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACzB;;AAEF,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC;AAClC,QAAA,MAAM,8BAA8B,GAAG,IAAI,KAAK,UAAU,CAAC,aAAa;QACxE,IAAI,8BAA8B,EAAE;AAClC,YAAA,UAAU,CAAC,aAAa,GAAG,IAAI;;AAEjC,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,aAAa,IAAI,IAAI,CAAC,iBAAiB,KAAK,UAAU,EAAE;AACrF,YAAA,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAC;;AAC7B,aAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,aAAa,EAAE;AACnD,YAAA,IAAI,CAAC,iBAAiB,GAAG,UAAU;;AAErC,QAAA,MAAM,kBAAkB,GAAG,IAAI,KAAK,CAAC,eAAe,EAAE;AACpD,YAAA,OAAO,EAAE,KAAK;YACd,UAAU;AACX,SAAA,CAAe;AACf,QAAA,kBAAgD,CAAC,KAAK,GAAG,MAAM;AAChE,QAAA,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,kBAAkB,CAAC;AACxD,QAAA,IAAI,MAAM,CAAC,WAAW,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;AACzD,YAAA,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC;;AAEhC,QAAA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC;AAC7B,QAAA,MAAM,UAAU,GAAG,UAAU,CAAC,UAAsD;AACpF,QAAA,UAAU,EAAE,eAAe,CAAC,MAAM,CAAC;AACnC,QAAA,UAAU,EAAE,cAAc,CAAC,MAAM,CAAC;AAClC,QAAA,UAAU,CAAC,UAAU,GAAG,IAAI;AAC9B,KAAC;AAED,IAAA,SAAS,QAAQ,GAAA;AACf,QAAA,UAAU,CAAC,aAAa,GAAG,KAAK;QAChC,MAAM,cAAc,GAAG,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC;AAElE,QAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,aAAa,EAAE;AAC7C,YAAA,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;gBAC5B,KAAK,CAAC,MAAM,CACV,IAAI,YAAY,CACd,6EAA6E,EAC7E,mBAAmB,CACpB,CACF;gBACD;;YAEF,MAAM,UAAU,GAAG,IAAI,4BAA4B,CAAC,UAAU,CAAC,YAAY,EAAE,cAAc,CAAC;AAC5F,YAAA,UAAU,CAAC,UAAU,GAAG,UAAU;;YAElC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC;YACnC,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC;;AAEtC,QAAA,IAAI,CAAC,cAAc,IAAI,KAAK,CAAC,UAAU,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE;gBACzC,KAAK,CAAC,MAAM,CACV,IAAI,YAAY,CAAC,gDAAgD,EAAE,YAAY,CAAC,CACjF;;;aAEE;AACL,YAAA,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;AAClC,gBAAA,MAAM,EAAE;;iBACH;AACL,gBAAA,MAAM,mBAAmB,GAAkC,EAAC,QAAQ,EAAC;gBACrE,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO,KAAI;AAC9D,oBAAA,IAAI,CAAgB;AACpB,oBAAA,IAAI;AACF,wBAAA,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC;;oBAChC,OAAO,CAAC,EAAE;AACV,wBAAA,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;;oBAEvB,CAAC,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC;AACjB,oBAAA,OAAO,CAAC;AACV,iBAAC,CAAC;AACF,gBAAA,OAAO,CAAC,GAAG,CAAC,qBAAqB;AAC9B,qBAAA,IAAI,CAAC,MAAM,MAAM,EAAE;AACnB,qBAAA,KAAK,CAAC,CAAC,MAAa,KAAI;oBACvB,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,EAAE;wBACxC;;AAEF,oBAAA,IAAI,UAAU,CAAC,UAAU,EAAE;AACxB,wBAAA,UAAU,CAAC,UAA2C,CAAC,eAAe,CAAC,MAAM,CAAC;;AAEjF,oBAAA,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AACtB,iBAAC,CAAC;;;;AAKV,IAAA,QAAQ,EAAE;AACV,IAAA,OAAO,KAAK,CAAC,iBAAiB,KAAK,MAAM;AAC3C;AAEA;AACA,SAAS,qBAAqB,CAAC,KAAgC,EAAE,UAAmB,EAAA;AAClF,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,UAAU,EAAE;AAC1C,QAAA,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC;;AAEpF,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,aAAa,EAAE;AAC7C,QAAA,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,YAAA,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC;;AAE/C,QAAA,KAAK,CAAC,iBAAiB,GAAG,UAAU;QACpC;;AAEF,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,MAAM,EAAE;QACtC;;IAEF,qBAAqB,CAAC,KAAK,CAAC;IAC5B,IAAI,UAAU,EAAE;QACd,sBAAsB,CAAC,KAAK,CAAC;;AAE/B,IAAA,KAAK,CAAC,iBAAiB,GAAG,UAAU;AACtC;AAEA;AACA,SAAS,qBAAqB,CAAC,KAAgC,EAAA;AAC7D,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,WAAW,IAAI,KAAK,CAAC,iBAAiB,KAAK,UAAU,EAAE;AACrF,QAAA,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC;;AAExF,IAAA,IAAI,KAAK,CAAC,kBAAkB,KAAK,QAAQ,EAAE;QACzC;;;AAGJ;AAEA,SAAS,sBAAsB,CAAC,KAAgC,EAAA;AAC9D,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,WAAW,IAAI,KAAK,CAAC,iBAAiB,KAAK,UAAU,EAAE;AACrF,QAAA,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC;;AAEzF,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,UAAU,IAAI,KAAK,CAAC,cAAc,KAAK,QAAQ,EAAE;QAC/E;;IAEF,qBAAqB,CAAC,KAAK,CAAC;AAC9B;AAEA;AACA,SAAS,qBAAqB,CAAC,KAAgC,EAAA;AAC7D,IAAA,IAAI,KAAK,CAAC,iBAAiB,KAAK,WAAW,EAAE;AAC3C,QAAA,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC;;AAErF,IAAA,KAAK,CAAC,iBAAiB,GAAG,UAAU;;AAEtC;AAOA;;;AAGG;AACH,SAAS,2CAA2C,CAAC,EACnD,IAAI,EACJ,cAAc,GAIf,EAAA;AACC,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,EAAE;AAC5C,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA,CAEA;AACD,IAAA,KAAK,CAAC,IAAI,GAAG,IAAI;AACjB,IAAA,KAAK,CAAC,cAAc,GAAG,cAAc;AACrC,IAAA,OAAO,KAA8C;AACvD;AAEA;;;AAGG;AACH,SAAS,mBAAmB,CAAC,EAAC,KAAK,EAAmB,EAAA;AACpD,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE;AAClC,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA,CAA6D;AAC9D,IAAA,KAAK,CAAC,KAAK,GAAG,KAAK;AACnB,IAAA,OAAO,KAAsB;AAC/B;AAEA,SAAS,qBAAqB,CAAC,MAAc,EAAE,MAAc,EAAA;AAC3D,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,YAAY,EAAE;AACpC,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA,CAAiE;AAClE,IAAA,KAAK,CAAC,MAAM,GAAG,MAAM;AACrB,IAAA,KAAK,CAAC,MAAM,GAAG,MAAM;AACrB,IAAA,OAAO,KAAwB;AACjC;AAEA;;AAEG;MACU,yBAAyB,CAAA;AACpC,IAAA,GAAG;AACM,IAAA,YAAY;AACZ,IAAA,GAAG;AACH,IAAA,EAAE;AACF,IAAA,KAAK;AAEd,IAAA,KAAK;AACY,IAAA,YAAY;IAE7B,WAAY,CAAA,EACV,GAAG,EACH,YAAY,EACZ,YAAY,EACZ,KAAK,EACL,GAAG,GAAG,IAAI,EACV,EAAE,GAAG,IAAI,EACT,KAAK,GAAG,CAAC,CAAC,GASX,EAAA;AACC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG;AACd,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY;AAChC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY;AAChC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG;AACd,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE;AACZ,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;IAGpB,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAGnB,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;AAE3B;AAED;AACA,SAAS,YAAY,CAAC,IAAS,EAAE,EAAO,EAAA;AACtC,IAAA,QACE,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;AACrB,QAAA,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;AAC7B,QAAA,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;AAC7B,QAAA,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM;AAE7B;AAEA,MAAM,4BAA4B,CAAA;AAQrB,IAAA,IAAA;AACA,IAAA,cAAA;AARF,IAAA,QAAQ;AACR,IAAA,SAAS;AAClB,IAAA,eAAe;AACf,IAAA,cAAc;AACd,IAAA,gBAAgB;AAChB,IAAA,eAAe;IACf,WACW,CAAA,IAA4B,EAC5B,cAAoC,EAAA;QADpC,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAc,CAAA,cAAA,GAAd,cAAc;QAEvB,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;AACpD,YAAA,IAAI,CAAC,cAAc,GAAG,MAAM;AAC5B,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO;AAChC,SAAC,CAAC;QACF,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,KAAI;AACrD,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC7B,YAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO;AACjC,SAAC,CAAC;;QAEF,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC;QAC7B,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC;;AAEjC;AAED;;;AAGG;AACH,MAAM,wBAAwB,CAAA;AAaP,IAAA,UAAA;IAZrB,WAAW,GAAsC,IAAI;AACrD,IAAA,gBAAgB;AAChB,IAAA,eAAe;AACf,IAAA,eAAe;AACf,IAAA,cAAc;AACL,IAAA,SAAS;AACT,IAAA,QAAQ;AACjB,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM;;AAEnB,IAAA,eAAe,GAAG,IAAI,eAAe,EAAE;AAExD,IAAA,WAAA,CAAqB,UAA0B,EAAA;QAA1B,IAAU,CAAA,UAAA,GAAV,UAAU;QAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAA6B,CAAC,OAAO,EAAE,MAAM,KAAI;AAC3E,YAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,KAAI;AAChC,gBAAA,IAAI,CAAC,WAAW,GAAG,KAAK;gBACxB,OAAO,CAAC,KAAK,CAAC;AAChB,aAAC;AACD,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM;AAC/B,SAAC,CAAC;QAEF,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAA6B,CAAC,OAAO,EAAE,MAAM,KAAI;AAC1E,YAAA,IAAI,CAAC,eAAe,GAAG,MAAK;AAC1B,gBAAA,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;AAC7B,oBAAA,MAAM,IAAI,KAAK,CACb,6EAA6E,CAC9E;;AAEH,gBAAA,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;AAC3B,aAAC;AACD,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,MAAa,KAAI;gBACtC,MAAM,CAAC,MAAM,CAAC;AACd,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC;AACpC,aAAC;AACH,SAAC,CAAC;;QAEF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC;;AAEhC;;AC/wCD;;;;AAIG;AAKH;AACA;AACA;AACA,UAAU,CAAC,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AAE9C;AACA;AACA;AACA;AACA;AACA,UAAU,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;AAEtC,SAAU,cAAc,CAAC,qBAA8B,EAAA;AAC3D,IAAA,OAAO,MAAK;AACV,QAAA,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ;AACpC,QAAA,IAAI,OAAO,CAAC,2BAA2B,EAAE,KAAK,qBAAqB,EAAE;YACnE,OAAO,CAAC,kBAAkB,EAAE;AAC5B,YAAA,0BAA0B,EAAE;;AAEhC,KAAC;AACH;;MC1Ba,GAAG,CAAA;AACd,IAAA,QAAQ;AAER,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;AAGpB,IAAA,GAAG,CAAC,KAAQ,EAAA;AACV,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;AAG3B,IAAA,EAAE,CAAC,KAAQ,EAAA;AACT,QAAA,OAAO,MAAK;AACV,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AAC3B,SAAC;;IAGH,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE;;IAGpB,MAAM,GAAA;QACJ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;;6FAtBtB,GAAG,GAAA,CAAA,EAAA;AAAH,IAAA,OAAA,KAAA,iBAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,KAAA,EAAA,GAAG,WAAH,GAAG,CAAA,IAAA,EAAA,CAAA;;iFAAH,GAAG,EAAA,CAAA;cADf;;;;;"}