import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-contact',
  imports: [CommonModule],
  templateUrl: './contact.html',
  styleUrl: './contact.scss'
})
export class ContactComponent {
  contactInfo = {
    email: 'contact&#64;teamportfolio.dev',
    phone: '+216 XX XXX XXX',
    location: 'Tunisia'
  };

  socialLinks = [
    { name: 'LinkedIn', url: 'https://linkedin.com', icon: 'fab fa-linkedin' },
    { name: 'GitHub', url: 'https://github.com', icon: 'fab fa-github' },
    { name: 'Twitter', url: 'https://twitter.com', icon: 'fab fa-twitter' }
  ];
}
