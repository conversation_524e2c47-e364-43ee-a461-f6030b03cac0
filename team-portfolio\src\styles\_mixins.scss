// ===== PROFESSIONAL MIXINS & ANIMATIONS =====

// Import variables
@import 'variables';

// ===== ANIMATION KEYFRAMES =====

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba($secondary-light, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba($secondary-light, 0.6);
  }
}

// ===== UTILITY MIXINS =====

// Smooth transitions
@mixin transition($properties: all, $duration: $transition-normal, $timing: ease-in-out) {
  transition: $properties $duration $timing;
}

// Hover lift effect
@mixin hover-lift($distance: 5px, $shadow: $shadow-lg) {
  transition: all $transition-normal;
  
  &:hover {
    transform: translateY(-#{$distance});
    box-shadow: $shadow;
  }
}

// Glassmorphism effect
@mixin glassmorphism($opacity: 0.1, $blur: 10px, $border-opacity: 0.2) {
  background: rgba($neutral-white, $opacity);
  backdrop-filter: blur($blur);
  border: 1px solid rgba($neutral-white, $border-opacity);
}

// Text gradient
@mixin text-gradient($gradient) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// Button base styles
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
  padding: $spacing-md $spacing-xl;
  border: none;
  border-radius: $radius-full;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-normal;
  position: relative;
  overflow: hidden;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Primary button
@mixin button-primary {
  @include button-base;
  background: $gradient-secondary;
  color: $neutral-white;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: $shadow-glow;
  }
  
  &:active {
    transform: translateY(0);
  }
}

// Secondary button
@mixin button-secondary {
  @include button-base;
  background: transparent;
  color: $secondary-light;
  border: 2px solid $secondary-light;
  
  &:hover:not(:disabled) {
    background: $secondary-light;
    color: $neutral-white;
    transform: translateY(-2px);
  }
}

// Card styles
@mixin card-base {
  background: $gradient-card;
  backdrop-filter: blur(10px);
  border: 1px solid rgba($neutral-white, 0.1);
  border-radius: $radius-2xl;
  padding: $spacing-2xl;
  transition: all $transition-normal;
}

@mixin card-hover {
  &:hover {
    transform: translateY(-5px);
    box-shadow: $shadow-2xl;
    border-color: rgba($secondary-light, 0.3);
  }
}

// Responsive breakpoints
@mixin mobile {
  @media (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: $breakpoint-md) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// Animation utilities
@mixin animate-on-scroll($animation: fadeInUp, $duration: 0.8s, $delay: 0s) {
  opacity: 0;
  animation: $animation $duration ease-out $delay forwards;
}

// Skill tag styles
@mixin skill-tag {
  display: inline-block;
  padding: $spacing-xs $spacing-md;
  background: rgba($neutral-white, 0.15);
  border: 1px solid rgba($neutral-white, 0.2);
  border-radius: $radius-full;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all $transition-normal;
  
  &:hover {
    background: rgba($neutral-white, 0.25);
    transform: translateY(-1px);
  }
}

// Social link styles
@mixin social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: $radius-full;
  background: rgba($neutral-white, 0.1);
  color: $neutral-white;
  text-decoration: none;
  transition: all $transition-normal;
  border: 1px solid rgba($neutral-white, 0.2);
  
  &:hover {
    background: $secondary-light;
    transform: translateY(-3px);
    box-shadow: $shadow-glow;
  }
}
