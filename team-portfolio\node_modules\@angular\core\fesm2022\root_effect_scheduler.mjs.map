{"version": 3, "file": "root_effect_scheduler.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/error_details_base_url.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/global.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/ng_dev_mode.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/property.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/stringify.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/forward_ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/assert.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/interface/defs.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/injection_token.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/debug/injector_profiler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/interface/provider.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/fields.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/util/stringify_utils.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/errors_di.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/inject_switch.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/injector_compatibility.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/definition_factory.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/array_utils.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/empty.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/initializer_token.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/injector_token.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/internal_tokens.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/null_injector.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/def_getters.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/provider_collection.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/scope.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/r3_injector.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/contextual.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/interfaces/view.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/interfaces/container.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/interfaces/type_checks.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/assert.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/namespaces.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/util/view_utils.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/state.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/create_injector.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/di/injector.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/document.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/linker/destroy_ref.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/error_handler.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/api.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/signal.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/change_detection/scheduling/zoneless_scheduling.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/asserts.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/view_context.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/pending_tasks.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/util/noop.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/src/render3/reactivity/root_effect_scheduler.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Base URL for the error details page.\n *\n * Keep this constant in sync across:\n *  - packages/compiler-cli/src/ngtsc/diagnostics/src/error_details_base_url.ts\n *  - packages/core/src/error_details_base_url.ts\n */\nexport const ERROR_DETAILS_PAGE_BASE_URL = 'https://angular.dev/errors';\n\n/**\n * URL for the XSS security documentation.\n */\nexport const XSS_SECURITY_URL =\n  'https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ERROR_DETAILS_PAGE_BASE_URL} from './error_details_base_url';\n\n/**\n * The list of error codes used in runtime code of the `core` package.\n * Reserved error code range: 100-999.\n *\n * Note: the minus sign denotes the fact that a particular code has a detailed guide on\n * angular.io. This extra annotation is needed to avoid introducing a separate set to store\n * error codes which have guides, which might leak into runtime code.\n *\n * Full list of available error guides can be found at https://angular.dev/errors.\n *\n * Error code ranges per package:\n *  - core (this package): 100-999\n *  - forms: 1000-1999\n *  - common: 2000-2999\n *  - animations: 3000-3999\n *  - router: 4000-4999\n *  - platform-browser: 5000-5500\n */\nexport const enum RuntimeErrorCode {\n  // Change Detection Errors\n  EXPRESSION_CHANGED_AFTER_CHECKED = -100,\n  RECURSIVE_APPLICATION_REF_TICK = 101,\n  INFINITE_CHANGE_DETECTION = 103,\n\n  // Dependency Injection Errors\n  CYCLIC_DI_DEPENDENCY = -200,\n  PROVIDER_NOT_FOUND = -201,\n  INVALID_FACTORY_DEPENDENCY = 202,\n  MISSING_INJECTION_CONTEXT = -203,\n  INVALID_INJECTION_TOKEN = 204,\n  INJECTOR_ALREADY_DESTROYED = 205,\n  PROVIDER_IN_WRONG_CONTEXT = 207,\n  MISSING_INJECTION_TOKEN = 208,\n  INVALID_MULTI_PROVIDER = -209,\n  MISSING_DOCUMENT = 210,\n\n  // Template Errors\n  MULTIPLE_COMPONENTS_MATCH = -300,\n  EXPORT_NOT_FOUND = -301,\n  PIPE_NOT_FOUND = -302,\n  UNKNOWN_BINDING = 303,\n  UNKNOWN_ELEMENT = 304,\n  TEMPLATE_STRUCTURE_ERROR = 305,\n  INVALID_EVENT_BINDING = 306,\n  HOST_DIRECTIVE_UNRESOLVABLE = 307,\n  HOST_DIRECTIVE_NOT_STANDALONE = 308,\n  DUPLICATE_DIRECTIVE = 309,\n  HOST_DIRECTIVE_COMPONENT = 310,\n  HOST_DIRECTIVE_UNDEFINED_BINDING = 311,\n  HOST_DIRECTIVE_CONFLICTING_ALIAS = 312,\n  MULTIPLE_MATCHING_PIPES = 313,\n  UNINITIALIZED_LET_ACCESS = 314,\n  NO_BINDING_TARGET = 315,\n  INVALID_BINDING_TARGET = 316,\n  INVALID_SET_INPUT_CALL = 317,\n\n  // Bootstrap Errors\n  MULTIPLE_PLATFORMS = 400,\n  PLATFORM_NOT_FOUND = 401,\n  MISSING_REQUIRED_INJECTABLE_IN_BOOTSTRAP = 402,\n  BOOTSTRAP_COMPONENTS_NOT_FOUND = -403,\n  PLATFORM_ALREADY_DESTROYED = 404,\n  ASYNC_INITIALIZERS_STILL_RUNNING = 405,\n  APPLICATION_REF_ALREADY_DESTROYED = 406,\n  RENDERER_NOT_FOUND = 407,\n  PROVIDED_BOTH_ZONE_AND_ZONELESS = 408,\n\n  // Hydration Errors\n  HYDRATION_NODE_MISMATCH = -500,\n  HYDRATION_MISSING_SIBLINGS = -501,\n  HYDRATION_MISSING_NODE = -502,\n  UNSUPPORTED_PROJECTION_DOM_NODES = -503,\n  INVALID_SKIP_HYDRATION_HOST = -504,\n  MISSING_HYDRATION_ANNOTATIONS = -505,\n  HYDRATION_STABLE_TIMEDOUT = -506,\n  MISSING_SSR_CONTENT_INTEGRITY_MARKER = -507,\n  MISCONFIGURED_INCREMENTAL_HYDRATION = 508,\n\n  // Signal Errors\n  SIGNAL_WRITE_FROM_ILLEGAL_CONTEXT = 600,\n  REQUIRE_SYNC_WITHOUT_SYNC_EMIT = 601,\n  ASSERTION_NOT_INSIDE_REACTIVE_CONTEXT = -602,\n\n  // Styling Errors\n\n  // Declarations Errors\n\n  // i18n Errors\n  INVALID_I18N_STRUCTURE = 700,\n  MISSING_LOCALE_DATA = 701,\n\n  // Defer errors (750-799 range)\n  DEFER_LOADING_FAILED = -750,\n  DEFER_IN_HMR_MODE = -751,\n\n  // standalone errors\n  IMPORT_PROVIDERS_FROM_STANDALONE = 800,\n\n  // JIT Compilation Errors\n  // Other\n  INVALID_DIFFER_INPUT = 900,\n  NO_SUPPORTING_DIFFER_FACTORY = 901,\n  VIEW_ALREADY_ATTACHED = 902,\n  INVALID_INHERITANCE = 903,\n  UNSAFE_VALUE_IN_RESOURCE_URL = 904,\n  UNSAFE_VALUE_IN_SCRIPT = 905,\n  MISSING_GENERATED_DEF = 906,\n  TYPE_IS_NOT_STANDALONE = 907,\n  MISSING_ZONEJS = 908,\n  UNEXPECTED_ZONE_STATE = 909,\n  UNSAFE_IFRAME_ATTRS = -910,\n  VIEW_ALREADY_DESTROYED = 911,\n  COMPONENT_ID_COLLISION = -912,\n  IMAGE_PERFORMANCE_WARNING = -913,\n  UNEXPECTED_ZONEJS_PRESENT_IN_ZONELESS_MODE = 914,\n  MISSING_NG_MODULE_DEFINITION = 915,\n  MISSING_DIRECTIVE_DEFINITION = 916,\n  NO_COMPONENT_FACTORY_FOUND = 917,\n\n  // Signal integration errors\n  REQUIRED_INPUT_NO_VALUE = -950,\n  REQUIRED_QUERY_NO_VALUE = -951,\n  REQUIRED_MODEL_NO_VALUE = 952,\n\n  // Output()\n  OUTPUT_REF_DESTROYED = 953,\n\n  // Repeater errors\n  LOOP_TRACK_DUPLICATE_KEYS = -955,\n  LOOP_TRACK_RECREATE = -956,\n\n  // Runtime dependency tracker errors\n  RUNTIME_DEPS_INVALID_IMPORTED_TYPE = 980,\n  RUNTIME_DEPS_ORPHAN_COMPONENT = 981,\n\n  // Resource errors\n  MUST_PROVIDE_STREAM_OPTION = 990,\n  RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE = 991,\n\n  // Upper bounds for core runtime errors is 999\n}\n\n/**\n * Class that represents a runtime error.\n * Formats and outputs the error message in a consistent way.\n *\n * Example:\n * ```ts\n *  throw new RuntimeError(\n *    RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED,\n *    ngDevMode && 'Injector has already been destroyed.');\n * ```\n *\n * Note: the `message` argument contains a descriptive error message as a string in development\n * mode (when the `ngDevMode` is defined). In production mode (after tree-shaking pass), the\n * `message` argument becomes `false`, thus we account for it in the typings and the runtime\n * logic.\n */\nexport class RuntimeError<T extends number = RuntimeErrorCode> extends Error {\n  constructor(\n    public code: T,\n    message: null | false | string,\n  ) {\n    super(formatRuntimeError<T>(code, message));\n  }\n}\n\nexport function formatRuntimeErrorCode<T extends number = RuntimeErrorCode>(code: T): string {\n  // Error code might be a negative number, which is a special marker that instructs the logic to\n  // generate a link to the error details page on angular.io.\n  // We also prepend `0` to non-compile-time errors.\n  return `NG0${Math.abs(code)}`;\n}\n\n/**\n * Called to format a runtime error.\n * See additional info on the `message` argument type in the `RuntimeError` class description.\n */\nexport function formatRuntimeError<T extends number = RuntimeErrorCode>(\n  code: T,\n  message: null | false | string,\n): string {\n  const fullCode = formatRuntimeErrorCode(code);\n\n  let errorMessage = `${fullCode}${message ? ': ' + message : ''}`;\n\n  if (ngDevMode && code < 0) {\n    const addPeriodSeparator = !errorMessage.match(/[.,;!?\\n]$/);\n    const separator = addPeriodSeparator ? '.' : '';\n    errorMessage = `${errorMessage}${separator} Find more at ${ERROR_DETAILS_PAGE_BASE_URL}/${fullCode}`;\n  }\n  return errorMessage;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nconst _global: any = globalThis;\n\n/**\n * Attention: whenever providing a new value, be sure to add an\n * entry into the corresponding `....externs.js` file,\n * so that closure won't use that global for its purposes.\n */\nexport {_global as global};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {global} from './global';\n\ndeclare global {\n  /**\n   * Values of ngDevMode\n   * Depending on the current state of the application, ngDevMode may have one of several values.\n   *\n   * For convenience, the “truthy” value which enables dev mode is also an object which contains\n   * Angular’s performance counters. This is not necessary, but cuts down on boilerplate for the\n   * perf counters.\n   *\n   * ngDevMode may also be set to false. This can happen in one of a few ways:\n   * - The user explicitly sets `window.ngDevMode = false` somewhere in their app.\n   * - The user calls `enableProdMode()`.\n   * - The URL contains a `ngDevMode=false` text.\n   * Finally, ngDevMode may not have been defined at all.\n   */\n  const ngDevMode: null | NgDevModePerfCounters;\n\n  interface NgDevModePerfCounters {\n    hydratedNodes: number;\n    hydratedComponents: number;\n    dehydratedViewsRemoved: number;\n    dehydratedViewsCleanupRuns: number;\n    componentsSkippedHydration: number;\n    deferBlocksWithIncrementalHydration: number;\n  }\n}\n\nfunction ngDevModeResetPerfCounters(): NgDevModePerfCounters {\n  const locationString = typeof location !== 'undefined' ? location.toString() : '';\n  const newCounters: NgDevModePerfCounters = {\n    hydratedNodes: 0,\n    hydratedComponents: 0,\n    dehydratedViewsRemoved: 0,\n    dehydratedViewsCleanupRuns: 0,\n    componentsSkippedHydration: 0,\n    deferBlocksWithIncrementalHydration: 0,\n  };\n\n  // Make sure to refer to ngDevMode as ['ngDevMode'] for closure.\n  const allowNgDevModeTrue = locationString.indexOf('ngDevMode=false') === -1;\n  if (!allowNgDevModeTrue) {\n    global['ngDevMode'] = false;\n  } else {\n    if (typeof global['ngDevMode'] !== 'object') {\n      global['ngDevMode'] = {};\n    }\n    Object.assign(global['ngDevMode'], newCounters);\n  }\n  return newCounters;\n}\n\n/**\n * This function checks to see if the `ngDevMode` has been set. If yes,\n * then we honor it, otherwise we default to dev mode with additional checks.\n *\n * The idea is that unless we are doing production build where we explicitly\n * set `ngDevMode == false` we should be helping the developer by providing\n * as much early warning and errors as possible.\n *\n * `ɵɵdefineComponent` is guaranteed to have been called before any component template functions\n * (and thus Ivy instructions), so a single initialization there is sufficient to ensure ngDevMode\n * is defined for the entire instruction set.\n *\n * When checking `ngDevMode` on toplevel, always init it before referencing it\n * (e.g. `((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode())`), otherwise you can\n *  get a `ReferenceError` like in https://github.com/angular/angular/issues/31595.\n *\n * Details on possible values for `ngDevMode` can be found on its docstring.\n */\nexport function initNgDevMode(): boolean {\n  // The below checks are to ensure that calling `initNgDevMode` multiple times does not\n  // reset the counters.\n  // If the `ngDevMode` is not an object, then it means we have not created the perf counters\n  // yet.\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (typeof ngDevMode !== 'object' || Object.keys(ngDevMode).length === 0) {\n      ngDevModeResetPerfCounters();\n    }\n    return typeof ngDevMode !== 'undefined' && !!ngDevMode;\n  }\n  return false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport function getClosureSafeProperty<T>(objWithPropertyToExtract: T): string {\n  for (let key in objWithPropertyToExtract) {\n    if (objWithPropertyToExtract[key] === (getClosureSafeProperty as any)) {\n      return key;\n    }\n  }\n  // Cannot change it to `RuntimeError` because the `util` target cannot\n  // circularly depend on the `core` target.\n  throw Error(\n    typeof ngDevMode !== 'undefined' && ngDevMode\n      ? 'Could not find renamed property on target object.'\n      : '',\n  );\n}\n\n/**\n * Sets properties on a target object from a source object, but only if\n * the property doesn't already exist on the target object.\n * @param target The target to set properties on\n * @param source The source of the property keys and values to set\n */\nexport function fillProperties(target: Record<string, unknown>, source: Record<string, unknown>) {\n  for (const key in source) {\n    if (source.hasOwnProperty(key) && !target.hasOwnProperty(key)) {\n      target[key] = source[key];\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport function stringify(token: any): string {\n  if (typeof token === 'string') {\n    return token;\n  }\n\n  if (Array.isArray(token)) {\n    return `[${token.map(stringify).join(', ')}]`;\n  }\n\n  if (token == null) {\n    return '' + token;\n  }\n\n  const name = token.overriddenName || token.name;\n  if (name) {\n    return `${name}`;\n  }\n\n  const result = token.toString();\n\n  if (result == null) {\n    return '' + result;\n  }\n\n  const newLineIndex = result.indexOf('\\n');\n  return newLineIndex >= 0 ? result.slice(0, newLineIndex) : result;\n}\n\n/**\n * Concatenates two strings with separator, allocating new strings only when necessary.\n *\n * @param before before string.\n * @param separator separator string.\n * @param after after string.\n * @returns concatenated string.\n */\nexport function concatStringsWithSpace(before: string | null, after: string | null): string {\n  if (!before) return after || '';\n  if (!after) return before;\n  return `${before} ${after}`;\n}\n\n/**\n * Ellipses the string in the middle when longer than the max length\n *\n * @param string\n * @param maxLength of the output string\n * @returns ellipsed string with ... in the middle\n */\nexport function truncateMiddle(str: string, maxLength = 100): string {\n  if (!str || maxLength < 1 || str.length <= maxLength) return str;\n  if (maxLength == 1) return str.substring(0, 1) + '...';\n\n  const halfLimit = Math.round(maxLength / 2);\n  return str.substring(0, halfLimit) + '...' + str.substring(str.length - halfLimit);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../interface/type';\nimport {getClosureSafeProperty} from '../util/property';\nimport {stringify} from '../util/stringify';\n\n/**\n * An interface that a function passed into `forwardRef` has to implement.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='forward_ref_fn'}\n * @publicApi\n */\nexport interface ForwardRefFn {\n  (): any;\n}\n\nconst __forward_ref__ = getClosureSafeProperty({__forward_ref__: getClosureSafeProperty});\n\n/**\n * Allows to refer to references which are not yet defined.\n *\n * For instance, `forwardRef` is used when the `token` which we need to refer to for the purposes of\n * DI is declared, but not yet defined. It is also used when the `token` which we use when creating\n * a query is not yet defined.\n *\n * `forwardRef` is also used to break circularities in standalone components imports.\n *\n * @usageNotes\n * ### Circular dependency example\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='forward_ref'}\n *\n * ### Circular standalone reference import example\n * ```angular-ts\n * @Component({\n *   imports: [ChildComponent],\n *   selector: 'app-parent',\n *   template: `<app-child [hideParent]=\"hideParent()\"></app-child>`,\n * })\n * export class ParentComponent {\n *    hideParent = input.required<boolean>();\n * }\n *\n *\n * @Component({\n *   imports: [forwardRef(() => ParentComponent)],\n *   selector: 'app-child',\n *   template: `\n *    @if(!hideParent() {\n *       <app-parent/>\n *    }\n *  `,\n * })\n * export class ChildComponent {\n *    hideParent = input.required<boolean>();\n * }\n * ```\n *\n * @publicApi\n */\nexport function forwardRef(forwardRefFn: ForwardRefFn): Type<any> {\n  (<any>forwardRefFn).__forward_ref__ = forwardRef;\n  (<any>forwardRefFn).toString = function () {\n    return stringify(this());\n  };\n  return <Type<any>>(<any>forwardRefFn);\n}\n\n/**\n * Lazily retrieves the reference value from a forwardRef.\n *\n * Acts as the identity function when given a non-forward-ref value.\n *\n * @usageNotes\n * ### Example\n *\n * {@example core/di/ts/forward_ref/forward_ref_spec.ts region='resolve_forward_ref'}\n *\n * @see {@link forwardRef}\n * @publicApi\n */\nexport function resolveForwardRef<T>(type: T): T {\n  return isForwardRef(type) ? type() : type;\n}\n\n/** Checks whether a function is wrapped by a `forwardRef`. */\nexport function isForwardRef(fn: any): fn is () => any {\n  return (\n    typeof fn === 'function' &&\n    fn.hasOwnProperty(__forward_ref__) &&\n    fn.__forward_ref__ === forwardRef\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// The functions in this file verify that the assumptions we are making\n// about state in an instruction are correct before implementing any logic.\n// They are meant only to be called in dev mode as sanity checks.\n\nimport {getActiveConsumer} from '../../primitives/signals';\n\nimport {stringify} from './stringify';\n\nexport function assertNumber(actual: any, msg: string): asserts actual is number {\n  if (!(typeof actual === 'number')) {\n    throwError(msg, typeof actual, 'number', '===');\n  }\n}\n\nexport function assertNumberInRange(\n  actual: any,\n  minInclusive: number,\n  maxInclusive: number,\n): asserts actual is number {\n  assertNumber(actual, 'Expected a number');\n  assertLessThanOrEqual(actual, maxInclusive, 'Expected number to be less than or equal to');\n  assertGreaterThanOrEqual(actual, minInclusive, 'Expected number to be greater than or equal to');\n}\n\nexport function assertString(actual: any, msg: string): asserts actual is string {\n  if (!(typeof actual === 'string')) {\n    throwError(msg, actual === null ? 'null' : typeof actual, 'string', '===');\n  }\n}\n\nexport function assertFunction(actual: any, msg: string): asserts actual is Function {\n  if (!(typeof actual === 'function')) {\n    throwError(msg, actual === null ? 'null' : typeof actual, 'function', '===');\n  }\n}\n\nexport function assertEqual<T>(actual: T, expected: T, msg: string) {\n  if (!(actual == expected)) {\n    throwError(msg, actual, expected, '==');\n  }\n}\n\nexport function assertNotEqual<T>(actual: T, expected: T, msg: string): asserts actual is T {\n  if (!(actual != expected)) {\n    throwError(msg, actual, expected, '!=');\n  }\n}\n\nexport function assertSame<T>(actual: T, expected: T, msg: string): asserts actual is T {\n  if (!(actual === expected)) {\n    throwError(msg, actual, expected, '===');\n  }\n}\n\nexport function assertNotSame<T>(actual: T, expected: T, msg: string) {\n  if (!(actual !== expected)) {\n    throwError(msg, actual, expected, '!==');\n  }\n}\n\nexport function assertLessThan<T>(actual: T, expected: T, msg: string): asserts actual is T {\n  if (!(actual < expected)) {\n    throwError(msg, actual, expected, '<');\n  }\n}\n\nexport function assertLessThanOrEqual<T>(actual: T, expected: T, msg: string): asserts actual is T {\n  if (!(actual <= expected)) {\n    throwError(msg, actual, expected, '<=');\n  }\n}\n\nexport function assertGreaterThan<T>(actual: T, expected: T, msg: string): asserts actual is T {\n  if (!(actual > expected)) {\n    throwError(msg, actual, expected, '>');\n  }\n}\n\nexport function assertGreaterThanOrEqual<T>(\n  actual: T,\n  expected: T,\n  msg: string,\n): asserts actual is T {\n  if (!(actual >= expected)) {\n    throwError(msg, actual, expected, '>=');\n  }\n}\n\nexport function assertNotDefined<T>(actual: T, msg: string) {\n  if (actual != null) {\n    throwError(msg, actual, null, '==');\n  }\n}\n\nexport function assertDefined<T>(actual: T | null | undefined, msg: string): asserts actual is T {\n  if (actual == null) {\n    throwError(msg, actual, null, '!=');\n  }\n}\n\nexport function throwError(msg: string): never;\nexport function throwError(msg: string, actual: any, expected: any, comparison: string): never;\nexport function throwError(msg: string, actual?: any, expected?: any, comparison?: string): never {\n  throw new Error(\n    `ASSERTION ERROR: ${msg}` +\n      (comparison == null ? '' : ` [Expected=> ${expected} ${comparison} ${actual} <=Actual]`),\n  );\n}\n\nexport function assertDomNode(node: any): asserts node is Node {\n  if (!(node instanceof Node)) {\n    throwError(`The provided value must be an instance of a DOM Node but got ${stringify(node)}`);\n  }\n}\n\nexport function assertElement(node: any): asserts node is Element {\n  if (!(node instanceof Element)) {\n    throwError(`The provided value must be an element but got ${stringify(node)}`);\n  }\n}\n\nexport function assertIndexInRange(arr: any[], index: number) {\n  assertDefined(arr, 'Array must be defined.');\n  const maxLen = arr.length;\n  if (index < 0 || index >= maxLen) {\n    throwError(`Index expected to be less than ${maxLen} but got ${index}`);\n  }\n}\n\nexport function assertOneOf(value: any, ...validValues: any[]) {\n  if (validValues.indexOf(value) !== -1) return true;\n  throwError(\n    `Expected value to be one of ${JSON.stringify(validValues)} but was ${JSON.stringify(value)}.`,\n  );\n}\n\nexport function assertNotReactive(fn: string): void {\n  if (getActiveConsumer() !== null) {\n    throwError(`${fn}() should never be called in a reactive context.`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../../interface/type';\nimport {getClosureSafeProperty} from '../../util/property';\n\nimport {\n  ClassProvider,\n  ConstructorProvider,\n  EnvironmentProviders,\n  ExistingProvider,\n  FactoryProvider,\n  StaticClassProvider,\n  ValueProvider,\n} from './provider';\n\n/**\n * Information about how a type or `InjectionToken` interfaces with the DI system.\n *\n * At a minimum, this includes a `factory` which defines how to create the given type `T`, possibly\n * requesting injection of other types if necessary.\n *\n * Optionally, a `providedIn` parameter specifies that the given type belongs to a particular\n * `Injector`, `NgModule`, or a special scope (e.g. `'root'`). A value of `null` indicates\n * that the injectable does not belong to any scope.\n *\n * @codeGenApi\n * @publicApi The ViewEngine compiler emits code with this type for injectables. This code is\n *   deployed to npm, and should be treated as public api.\n\n */\nexport interface ɵɵInjectableDeclaration<T> {\n  /**\n   * Specifies that the given type belongs to a particular injector:\n   * - `InjectorType` such as `NgModule`,\n   * - `'root'` the root injector\n   * - `'any'` all injectors.\n   * - `null`, does not belong to any injector. Must be explicitly listed in the injector\n   *   `providers`.\n   */\n  providedIn: InjectorType<any> | 'root' | 'platform' | 'any' | 'environment' | null;\n\n  /**\n   * The token to which this definition belongs.\n   *\n   * Note that this may not be the same as the type that the `factory` will create.\n   */\n  token: unknown;\n\n  /**\n   * Factory method to execute to create an instance of the injectable.\n   */\n  factory: (t?: Type<any>) => T;\n\n  /**\n   * In a case of no explicit injector, a location where the instance of the injectable is stored.\n   */\n  value: T | undefined;\n}\n\n/**\n * Information about the providers to be included in an `Injector` as well as how the given type\n * which carries the information should be created by the DI system.\n *\n * An `InjectorDef` can import other types which have `InjectorDefs`, forming a deep nested\n * structure of providers with a defined priority (identically to how `NgModule`s also have\n * an import/dependency structure).\n *\n * NOTE: This is a private type and should not be exported\n *\n * @codeGenApi\n */\nexport interface ɵɵInjectorDef<T> {\n  // TODO(alxhub): Narrow down the type here once decorators properly change the return type of the\n  // class they are decorating (to add the ɵprov property for example).\n  providers: (\n    | Type<any>\n    | ValueProvider\n    | ExistingProvider\n    | FactoryProvider\n    | ConstructorProvider\n    | StaticClassProvider\n    | ClassProvider\n    | EnvironmentProviders\n    | any[]\n  )[];\n\n  imports: (InjectorType<any> | InjectorTypeWithProviders<any>)[];\n}\n\n/**\n * A `Type` which has a `ɵprov: ɵɵInjectableDeclaration` static field.\n *\n * `InjectableType`s contain their own Dependency Injection metadata and are usable in an\n * `InjectorDef`-based `StaticInjector`.\n *\n * @publicApi\n */\nexport interface InjectableType<T> extends Type<T> {\n  /**\n   * Opaque type whose structure is highly version dependent. Do not rely on any properties.\n   */\n  ɵprov: unknown;\n}\n\n/**\n * A type which has an `InjectorDef` static field.\n *\n * `InjectorTypes` can be used to configure a `StaticInjector`.\n *\n * This is an opaque type whose structure is highly version dependent. Do not rely on any\n * properties.\n *\n * @publicApi\n */\nexport interface InjectorType<T> extends Type<T> {\n  ɵfac?: unknown;\n  ɵinj: unknown;\n}\n\n/**\n * Describes the `InjectorDef` equivalent of a `ModuleWithProviders`, an `InjectorType` with an\n * associated array of providers.\n *\n * Objects of this type can be listed in the imports section of an `InjectorDef`.\n *\n * NOTE: This is a private type and should not be exported\n */\nexport interface InjectorTypeWithProviders<T> {\n  ngModule: InjectorType<T>;\n  providers?: (\n    | Type<any>\n    | ValueProvider\n    | ExistingProvider\n    | FactoryProvider\n    | ConstructorProvider\n    | StaticClassProvider\n    | ClassProvider\n    | EnvironmentProviders\n    | any[]\n  )[];\n}\n\n/**\n * Construct an injectable definition which defines how a token will be constructed by the DI\n * system, and in which injectors (if any) it will be available.\n *\n * This should be assigned to a static `ɵprov` field on a type, which will then be an\n * `InjectableType`.\n *\n * Options:\n * * `providedIn` determines which injectors will include the injectable, by either associating it\n *   with an `@NgModule` or other `InjectorType`, or by specifying that this injectable should be\n *   provided in the `'root'` injector, which will be the application-level injector in most apps.\n * * `factory` gives the zero argument function which will create an instance of the injectable.\n *   The factory can call [`inject`](api/core/inject) to access the `Injector` and request injection\n * of dependencies.\n *\n * @codeGenApi\n * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.\n */\nexport function ɵɵdefineInjectable<T>(opts: {\n  token: unknown;\n  providedIn?: Type<any> | 'root' | 'platform' | 'any' | 'environment' | null;\n  factory: () => T;\n}): unknown {\n  return {\n    token: opts.token,\n    providedIn: (opts.providedIn as any) || null,\n    factory: opts.factory,\n    value: undefined,\n  } as ɵɵInjectableDeclaration<T>;\n}\n\n/**\n * @deprecated in v8, delete after v10. This API should be used only by generated code, and that\n * code should now use ɵɵdefineInjectable instead.\n * @publicApi\n */\nexport const defineInjectable = ɵɵdefineInjectable;\n\n/**\n * Construct an `InjectorDef` which configures an injector.\n *\n * This should be assigned to a static injector def (`ɵinj`) field on a type, which will then be an\n * `InjectorType`.\n *\n * Options:\n *\n * * `providers`: an optional array of providers to add to the injector. Each provider must\n *   either have a factory or point to a type which has a `ɵprov` static property (the\n *   type must be an `InjectableType`).\n * * `imports`: an optional array of imports of other `InjectorType`s or `InjectorTypeWithModule`s\n *   whose providers will also be added to the injector. Locally provided types will override\n *   providers from imports.\n *\n * @codeGenApi\n */\nexport function ɵɵdefineInjector(options: {providers?: any[]; imports?: any[]}): unknown {\n  return {providers: options.providers || [], imports: options.imports || []};\n}\n\n/**\n * Read the injectable def (`ɵprov`) for `type` in a way which is immune to accidentally reading\n * inherited value.\n *\n * @param type A type which may have its own (non-inherited) `ɵprov`.\n */\nexport function getInjectableDef<T>(type: any): ɵɵInjectableDeclaration<T> | null {\n  return getOwnDefinition(type, NG_PROV_DEF);\n}\n\nexport function isInjectable(type: any): boolean {\n  return getInjectableDef(type) !== null;\n}\n\n/**\n * Return definition only if it is defined directly on `type` and is not inherited from a base\n * class of `type`.\n */\nfunction getOwnDefinition<T>(type: any, field: string): ɵɵInjectableDeclaration<T> | null {\n  // if the ɵprov prop exist but is undefined we still want to return null\n  return (type.hasOwnProperty(field) && type[field]) || null;\n}\n\n/**\n * Read the injectable def (`ɵprov`) for `type` or read the `ɵprov` from one of its ancestors.\n *\n * @param type A type which may have `ɵprov`, via inheritance.\n *\n * @deprecated Will be removed in a future version of Angular, where an error will occur in the\n *     scenario if we find the `ɵprov` on an ancestor only.\n */\nexport function getInheritedInjectableDef<T>(type: any): ɵɵInjectableDeclaration<T> | null {\n  // if the ɵprov prop exist but is undefined we still want to return null\n  const def = type?.[NG_PROV_DEF] ?? null;\n\n  if (def) {\n    ngDevMode &&\n      console.warn(\n        `DEPRECATED: DI is instantiating a token \"${type.name}\" that inherits its @Injectable decorator but does not provide one itself.\\n` +\n          `This will become an error in a future version of Angular. Please add @Injectable() to the \"${type.name}\" class.`,\n      );\n    return def;\n  } else {\n    return null;\n  }\n}\n\n/**\n * Read the injector def type in a way which is immune to accidentally reading inherited value.\n *\n * @param type type which may have an injector def (`ɵinj`)\n */\nexport function getInjectorDef<T>(type: any): ɵɵInjectorDef<T> | null {\n  return type && type.hasOwnProperty(NG_INJ_DEF) ? (type as any)[NG_INJ_DEF] : null;\n}\n\nexport const NG_PROV_DEF: string = getClosureSafeProperty({ɵprov: getClosureSafeProperty});\nexport const NG_INJ_DEF: string = getClosureSafeProperty({ɵinj: getClosureSafeProperty});\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../interface/type';\nimport {assertLessThan} from '../util/assert';\n\nimport {ɵɵdefineInjectable} from './interface/defs';\n\n/**\n * Creates a token that can be used in a DI Provider.\n *\n * Use an `InjectionToken` whenever the type you are injecting is not reified (does not have a\n * runtime representation) such as when injecting an interface, callable type, array or\n * parameterized type.\n *\n * `InjectionToken` is parameterized on `T` which is the type of object which will be returned by\n * the `Injector`. This provides an additional level of type safety.\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n * **Important Note**: Ensure that you use the same instance of the `InjectionToken` in both the\n * provider and the injection call. Creating a new instance of `InjectionToken` in different places,\n * even with the same description, will be treated as different tokens by Angular's DI system,\n * leading to a `NullInjectorError`.\n *\n * </div>\n *\n * {@example injection-token/src/main.ts region='InjectionToken'}\n *\n * When creating an `InjectionToken`, you can optionally specify a factory function which returns\n * (possibly by creating) a default value of the parameterized type `T`. This sets up the\n * `InjectionToken` using this factory as a provider as if it was defined explicitly in the\n * application's root injector. If the factory function, which takes zero arguments, needs to inject\n * dependencies, it can do so using the [`inject`](api/core/inject) function.\n * As you can see in the Tree-shakable InjectionToken example below.\n *\n * Additionally, if a `factory` is specified you can also specify the `providedIn` option, which\n * overrides the above behavior and marks the token as belonging to a particular `@NgModule` (note:\n * this option is now deprecated). As mentioned above, `'root'` is the default value for\n * `providedIn`.\n *\n * The `providedIn: NgModule` and `providedIn: 'any'` options are deprecated.\n *\n * @usageNotes\n * ### Basic Examples\n *\n * ### Plain InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='InjectionToken'}\n *\n * ### Tree-shakable InjectionToken\n *\n * {@example core/di/ts/injector_spec.ts region='ShakableInjectionToken'}\n *\n * @publicApi\n */\nexport class InjectionToken<T> {\n  /** @internal */\n  readonly ngMetadataName = 'InjectionToken';\n\n  readonly ɵprov: unknown;\n\n  /**\n   * @param _desc   Description for the token,\n   *                used only for debugging purposes,\n   *                it should but does not need to be unique\n   * @param options Options for the token's usage, as described above\n   */\n  constructor(\n    protected _desc: string,\n    options?: {\n      providedIn?: Type<any> | 'root' | 'platform' | 'any' | null;\n      factory: () => T;\n    },\n  ) {\n    this.ɵprov = undefined;\n    if (typeof options == 'number') {\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        assertLessThan(options, 0, 'Only negative numbers are supported here');\n      // This is a special hack to assign __NG_ELEMENT_ID__ to this instance.\n      // See `InjectorMarkers`\n      (this as any).__NG_ELEMENT_ID__ = options;\n    } else if (options !== undefined) {\n      this.ɵprov = ɵɵdefineInjectable({\n        token: this,\n        providedIn: options.providedIn || 'root',\n        factory: options.factory,\n      });\n    }\n  }\n\n  /**\n   * @internal\n   */\n  get multi(): InjectionToken<Array<T>> {\n    return this as InjectionToken<Array<T>>;\n  }\n\n  toString(): string {\n    return `InjectionToken ${this._desc}`;\n  }\n}\n\nexport interface InjectableDefToken<T> extends InjectionToken<T> {\n  ɵprov: unknown;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type {FactoryProvider, ProviderToken} from '../../di';\nimport {resolveForwardRef} from '../../di/forward_ref';\nimport {InjectionToken} from '../../di/injection_token';\nimport type {Injector} from '../../di/injector';\nimport {InjectOptions, InternalInjectFlags} from '../../di/interface/injector';\nimport type {SingleProvider} from '../../di/provider_collection';\nimport {Type} from '../../interface/type';\nimport {throwError} from '../../util/assert';\nimport type {TNode} from '../interfaces/node';\nimport type {LView} from '../interfaces/view';\nimport type {EffectRef} from '../reactivity/effect';\n\n/**\n * An enum describing the types of events that can be emitted from the injector profiler\n */\nexport const enum InjectorProfilerEventType {\n  /**\n   * Emits when a service is injected.\n   */\n  Inject,\n\n  /**\n   * Emits when an Angular class instance is created by an injector.\n   */\n  InstanceCreatedByInjector,\n\n  /**\n   * Emits when an injector configures a provider.\n   */\n  ProviderConfigured,\n\n  /**\n   * Emits when an effect is created.\n   */\n  EffectCreated,\n\n  /**\n   * Emits when an Angular DI system is about to create an instance corresponding to a given token.\n   */\n  InjectorToCreateInstanceEvent,\n}\n\n/**\n * An object that defines an injection context for the injector profiler.\n */\nexport interface InjectorProfilerContext {\n  /**\n   *  The Injector that service is being injected into.\n   *      - Example: if ModuleA --provides--> ServiceA --injects--> ServiceB\n   *                 then inject(ServiceB) in ServiceA has ModuleA as an injector context\n   */\n  injector: Injector;\n\n  /**\n   *  The class where the constructor that is calling `inject` is located\n   *      - Example: if ModuleA --provides--> ServiceA --injects--> ServiceB\n   *                 then inject(ServiceB) in ServiceA has ServiceA as a construction context\n   */\n  token: Type<unknown> | null;\n}\n\nexport interface InjectedServiceEvent {\n  type: InjectorProfilerEventType.Inject;\n  context: InjectorProfilerContext;\n  service: InjectedService;\n}\n\nexport interface InjectorToCreateInstanceEvent {\n  type: InjectorProfilerEventType.InjectorToCreateInstanceEvent;\n  context: InjectorProfilerContext;\n  token: ProviderToken<unknown>;\n}\n\nexport interface InjectorCreatedInstanceEvent {\n  type: InjectorProfilerEventType.InstanceCreatedByInjector;\n  context: InjectorProfilerContext;\n  instance: InjectorCreatedInstance;\n}\n\nexport interface ProviderConfiguredEvent {\n  type: InjectorProfilerEventType.ProviderConfigured;\n  context: InjectorProfilerContext;\n  providerRecord: ProviderRecord;\n}\n\nexport interface EffectCreatedEvent {\n  type: InjectorProfilerEventType.EffectCreated;\n  context: InjectorProfilerContext;\n  effect: EffectRef;\n}\n\n/**\n * An object representing an event that is emitted through the injector profiler\n */\n\nexport type InjectorProfilerEvent =\n  | InjectedServiceEvent\n  | InjectorToCreateInstanceEvent\n  | InjectorCreatedInstanceEvent\n  | ProviderConfiguredEvent\n  | EffectCreatedEvent;\n\n/**\n * An object that contains information about a provider that has been configured\n *\n * TODO: rename to indicate that it is a debug structure eg. ProviderDebugInfo.\n */\nexport interface ProviderRecord {\n  /**\n   * DI token that this provider is configuring\n   */\n  token: Type<unknown> | InjectionToken<unknown>;\n\n  /**\n   * Determines if provider is configured as view provider.\n   */\n  isViewProvider: boolean;\n\n  /**\n   * The raw provider associated with this ProviderRecord.\n   */\n  provider: SingleProvider;\n\n  /**\n   * The path of DI containers that were followed to import this provider\n   */\n  importPath?: Type<unknown>[];\n}\n\n/**\n * An object that contains information about a value that has been constructed within an injector\n */\nexport interface InjectorCreatedInstance {\n  /**\n   * Value of the created instance\n   */\n  value: unknown;\n}\n\n/**\n * An object that contains information a service that has been injected within an\n * InjectorProfilerContext\n */\nexport interface InjectedService {\n  /**\n   * DI token of the Service that is injected\n   */\n  token?: Type<unknown> | InjectionToken<unknown>;\n\n  /**\n   * Value of the injected service\n   */\n  value: unknown;\n\n  /**\n   * Flags that this service was injected with\n   */\n  flags?: InternalInjectFlags | InjectOptions;\n\n  /**\n   * Injector that this service was provided in.\n   */\n  providedIn?: Injector;\n\n  /**\n   * In NodeInjectors, the LView and TNode that serviced this injection.\n   */\n  injectedIn?: {lView: LView; tNode: TNode};\n}\n\nexport interface InjectorProfiler {\n  (event: InjectorProfilerEvent): void;\n}\n\nlet _injectorProfilerContext: InjectorProfilerContext;\nexport function getInjectorProfilerContext() {\n  !ngDevMode && throwError('getInjectorProfilerContext should never be called in production mode');\n  return _injectorProfilerContext;\n}\n\nexport function setInjectorProfilerContext(context: InjectorProfilerContext) {\n  !ngDevMode && throwError('setInjectorProfilerContext should never be called in production mode');\n\n  const previous = _injectorProfilerContext;\n  _injectorProfilerContext = context;\n  return previous;\n}\n\nconst injectorProfilerCallbacks: InjectorProfiler[] = [];\n\nconst NOOP_PROFILER_REMOVAL = () => {};\n\nfunction removeProfiler(profiler: InjectorProfiler) {\n  const profilerIdx = injectorProfilerCallbacks.indexOf(profiler);\n  if (profilerIdx !== -1) {\n    injectorProfilerCallbacks.splice(profilerIdx, 1);\n  }\n}\n\n/**\n * Adds a callback function which will be invoked during certain DI events within the\n * runtime (for example: injecting services, creating injectable instances, configuring providers).\n * Multiple profiler callbacks can be set: in this case profiling events are\n * reported to every registered callback.\n *\n * Warning: this function is *INTERNAL* and should not be relied upon in application's code.\n * The contract of the function might be changed in any release and/or the function can be removed\n * completely.\n *\n * @param profiler function provided by the caller or null value to disable profiling.\n * @returns a cleanup function that, when invoked, removes a given profiler callback.\n */\nexport function setInjectorProfiler(injectorProfiler: InjectorProfiler | null): () => void {\n  !ngDevMode && throwError('setInjectorProfiler should never be called in production mode');\n\n  if (injectorProfiler !== null) {\n    if (!injectorProfilerCallbacks.includes(injectorProfiler)) {\n      injectorProfilerCallbacks.push(injectorProfiler);\n    }\n    return () => removeProfiler(injectorProfiler);\n  } else {\n    injectorProfilerCallbacks.length = 0;\n    return NOOP_PROFILER_REMOVAL;\n  }\n}\n\n/**\n * Injector profiler function which emits on DI events executed by the runtime.\n *\n * @param event InjectorProfilerEvent corresponding to the DI event being emitted\n */\nexport function injectorProfiler(event: InjectorProfilerEvent): void {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n\n  for (let i = 0; i < injectorProfilerCallbacks.length; i++) {\n    const injectorProfilerCallback = injectorProfilerCallbacks[i];\n    injectorProfilerCallback(event);\n  }\n}\n\n/**\n * Emits an InjectorProfilerEventType.ProviderConfigured to the injector profiler. The data in the\n * emitted event includes the raw provider, as well as the token that provider is providing.\n *\n * @param eventProvider A provider object\n */\nexport function emitProviderConfiguredEvent(\n  eventProvider: SingleProvider,\n  isViewProvider: boolean = false,\n): void {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n\n  let token;\n  // if the provider is a TypeProvider (typeof provider is function) then the token is the\n  // provider itself\n  if (typeof eventProvider === 'function') {\n    token = eventProvider;\n  }\n  // if the provider is an injection token, then the token is the injection token.\n  else if (eventProvider instanceof InjectionToken) {\n    token = eventProvider;\n  }\n  // in all other cases we can access the token via the `provide` property of the provider\n  else {\n    token = resolveForwardRef(eventProvider.provide);\n  }\n\n  let provider = eventProvider;\n  // Injection tokens may define their own default provider which gets attached to the token itself\n  // as `ɵprov`. In this case, we want to emit the provider that is attached to the token, not the\n  // token itself.\n  if (eventProvider instanceof InjectionToken) {\n    provider = (eventProvider.ɵprov as FactoryProvider) || eventProvider;\n  }\n\n  injectorProfiler({\n    type: InjectorProfilerEventType.ProviderConfigured,\n    context: getInjectorProfilerContext(),\n    providerRecord: {token, provider, isViewProvider},\n  });\n}\n\n/**\n * Emits an event to the injector profiler when an instance corresponding to a given token is about to be created be an injector. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nexport function emitInjectorToCreateInstanceEvent(token: ProviderToken<unknown>): void {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n\n  injectorProfiler({\n    type: InjectorProfilerEventType.InjectorToCreateInstanceEvent,\n    context: getInjectorProfilerContext(),\n    token: token,\n  });\n}\n\n/**\n * Emits an event to the injector profiler with the instance that was created. Note that\n * the injector associated with this emission can be accessed by using getDebugInjectContext()\n *\n * @param instance an object created by an injector\n */\nexport function emitInstanceCreatedByInjectorEvent(instance: unknown): void {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n\n  injectorProfiler({\n    type: InjectorProfilerEventType.InstanceCreatedByInjector,\n    context: getInjectorProfilerContext(),\n    instance: {value: instance},\n  });\n}\n\n/**\n * @param token DI token associated with injected service\n * @param value the instance of the injected service (i.e the result of `inject(token)`)\n * @param flags the flags that the token was injected with\n */\nexport function emitInjectEvent(\n  token: Type<unknown>,\n  value: unknown,\n  flags: InternalInjectFlags,\n): void {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n\n  injectorProfiler({\n    type: InjectorProfilerEventType.Inject,\n    context: getInjectorProfilerContext(),\n    service: {token, value, flags},\n  });\n}\n\nexport function emitEffectCreatedEvent(effect: EffectRef): void {\n  !ngDevMode && throwError('Injector profiler should never be called in production mode');\n\n  injectorProfiler({\n    type: InjectorProfilerEventType.EffectCreated,\n    context: getInjectorProfilerContext(),\n    effect,\n  });\n}\n\nexport function runInInjectorProfilerContext(\n  injector: Injector,\n  token: Type<unknown>,\n  callback: () => void,\n): void {\n  !ngDevMode &&\n    throwError('runInInjectorProfilerContext should never be called in production mode');\n\n  const prevInjectContext = setInjectorProfilerContext({injector, token});\n  try {\n    callback();\n  } finally {\n    setInjectorProfilerContext(prevInjectContext);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../../interface/type';\n\n/**\n * Configures the `Injector` to return a value for a token.\n * Base for `ValueProvider` decorator.\n *\n * @publicApi\n */\nexport interface ValueSansProvider {\n  /**\n   * The value to inject.\n   */\n  useValue: any;\n}\n\n/**\n * Configures the `Injector` to return a value for a token.\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example core/di/ts/provider_spec.ts region='ValueProvider'}\n *\n * ### Multi-value example\n *\n * {@example core/di/ts/provider_spec.ts region='MultiProviderAspect'}\n *\n * @publicApi\n */\nexport interface ValueProvider extends ValueSansProvider {\n  /**\n   * An injection token. Typically an instance of `Type` or `InjectionToken`, but can be `any`.\n   */\n  provide: any;\n\n  /**\n   * When true, injector returns an array of instances. This is useful to allow multiple\n   * providers spread across many files to provide configuration information to a common token.\n   */\n  multi?: boolean;\n}\n\n/**\n * Configures the `Injector` to return an instance of `useClass` for a token.\n * Base for `StaticClassProvider` decorator.\n *\n * @publicApi\n */\nexport interface StaticClassSansProvider {\n  /**\n   * An optional class to instantiate for the `token`. By default, the `provide`\n   * class is instantiated.\n   */\n  useClass: Type<any>;\n\n  /**\n   * A list of `token`s to be resolved by the injector. The list of values is then\n   * used as arguments to the `useClass` constructor.\n   */\n  deps: any[];\n}\n\n/**\n * Configures the `Injector` to return an instance of `useClass` for a token.\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * {@example core/di/ts/provider_spec.ts region='StaticClassProvider'}\n *\n * Note that following two providers are not equal:\n *\n * {@example core/di/ts/provider_spec.ts region='StaticClassProviderDifference'}\n *\n * ### Multi-value example\n *\n * {@example core/di/ts/provider_spec.ts region='MultiProviderAspect'}\n *\n * @publicApi\n */\nexport interface StaticClassProvider extends StaticClassSansProvider {\n  /**\n   * An injection token. Typically an instance of `Type` or `InjectionToken`, but can be `any`.\n   */\n  provide: any;\n\n  /**\n   * When true, injector returns an array of instances. This is useful to allow multiple\n   * providers spread across many files to provide configuration information to a common token.\n   */\n  multi?: boolean;\n}\n\n/**\n * Configures the `Injector` to return an instance of a token.\n *\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * ```ts\n * @Injectable(SomeModule, {deps: []})\n * class MyService {}\n * ```\n *\n * @publicApi\n */\nexport interface ConstructorSansProvider {\n  /**\n   * A list of `token`s to be resolved by the injector.\n   */\n  deps?: any[];\n}\n\n/**\n * Configures the `Injector` to return an instance of a token.\n *\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * {@example core/di/ts/provider_spec.ts region='ConstructorProvider'}\n *\n * ### Multi-value example\n *\n * {@example core/di/ts/provider_spec.ts region='MultiProviderAspect'}\n *\n * @publicApi\n */\nexport interface ConstructorProvider extends ConstructorSansProvider {\n  /**\n   * An injection token. Typically an instance of `Type` or `InjectionToken`, but can be `any`.\n   */\n  provide: Type<any>;\n\n  /**\n   * When true, injector returns an array of instances. This is useful to allow multiple\n   * providers spread across many files to provide configuration information to a common token.\n   */\n  multi?: boolean;\n}\n\n/**\n * Configures the `Injector` to return a value of another `useExisting` token.\n *\n * @see {@link ExistingProvider}\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @publicApi\n */\nexport interface ExistingSansProvider {\n  /**\n   * Existing `token` to return. (Equivalent to `injector.get(useExisting)`)\n   */\n  useExisting: any;\n}\n\n/**\n * Configures the `Injector` to return a value of another `useExisting` token.\n *\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * {@example core/di/ts/provider_spec.ts region='ExistingProvider'}\n *\n * ### Multi-value example\n *\n * {@example core/di/ts/provider_spec.ts region='MultiProviderAspect'}\n *\n * @publicApi\n */\nexport interface ExistingProvider extends ExistingSansProvider {\n  /**\n   * An injection token. Typically an instance of `Type` or `InjectionToken`, but can be `any`.\n   */\n  provide: any;\n\n  /**\n   * When true, injector returns an array of instances. This is useful to allow multiple\n   * providers spread across many files to provide configuration information to a common token.\n   */\n  multi?: boolean;\n}\n\n/**\n * Configures the `Injector` to return a value by invoking a `useFactory` function.\n *\n * @see {@link FactoryProvider}\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @publicApi\n */\nexport interface FactorySansProvider {\n  /**\n   * A function to invoke to create a value for this `token`. The function is invoked with\n   * resolved values of `token`s in the `deps` field.\n   */\n  useFactory: Function;\n\n  /**\n   * A list of `token`s to be resolved by the injector. The list of values is then\n   * used as arguments to the `useFactory` function.\n   */\n  deps?: any[];\n}\n\n/**\n * Configures the `Injector` to return a value by invoking a `useFactory` function.\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * {@example core/di/ts/provider_spec.ts region='FactoryProvider'}\n *\n * Dependencies can also be marked as optional:\n *\n * {@example core/di/ts/provider_spec.ts region='FactoryProviderOptionalDeps'}\n *\n * ### Multi-value example\n *\n * {@example core/di/ts/provider_spec.ts region='MultiProviderAspect'}\n *\n * @publicApi\n */\nexport interface FactoryProvider extends FactorySansProvider {\n  /**\n   * An injection token. (Typically an instance of `Type` or `InjectionToken`, but can be `any`).\n   */\n  provide: any;\n\n  /**\n   * When true, injector returns an array of instances. This is useful to allow multiple\n   * providers spread across many files to provide configuration information to a common token.\n   */\n  multi?: boolean;\n}\n\n/**\n * Describes how an `Injector` should be configured as static (that is, without reflection).\n * A static provider provides tokens to an injector for various types of dependencies.\n *\n * @see {@link Injector.create()}\n * @see [Dependency Injection Guide](guide/di/dependency-injection-providers).\n *\n * @publicApi\n */\nexport type StaticProvider =\n  | ValueProvider\n  | ExistingProvider\n  | StaticClassProvider\n  | ConstructorProvider\n  | FactoryProvider\n  | any[];\n\n/**\n * Configures the `Injector` to return an instance of `Type` when `Type' is used as the token.\n *\n * Create an instance by invoking the `new` operator and supplying additional arguments.\n * This form is a short form of `TypeProvider`;\n *\n * For more details, see the [\"Dependency Injection Guide\"](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * {@example core/di/ts/provider_spec.ts region='TypeProvider'}\n *\n * @publicApi\n */\nexport interface TypeProvider extends Type<any> {}\n\n/**\n * Configures the `Injector` to return a value by invoking a `useClass` function.\n * Base for `ClassProvider` decorator.\n *\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @publicApi\n */\nexport interface ClassSansProvider {\n  /**\n   * Class to instantiate for the `token`.\n   */\n  useClass: Type<any>;\n}\n\n/**\n * Configures the `Injector` to return an instance of `useClass` for a token.\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @usageNotes\n *\n * {@example core/di/ts/provider_spec.ts region='ClassProvider'}\n *\n * Note that following two providers are not equal:\n *\n * {@example core/di/ts/provider_spec.ts region='ClassProviderDifference'}\n *\n * ### Multi-value example\n *\n * {@example core/di/ts/provider_spec.ts region='MultiProviderAspect'}\n *\n * @publicApi\n */\nexport interface ClassProvider extends ClassSansProvider {\n  /**\n   * An injection token. (Typically an instance of `Type` or `InjectionToken`, but can be `any`).\n   */\n  provide: any;\n\n  /**\n   * When true, injector returns an array of instances. This is useful to allow multiple\n   * providers spread across many files to provide configuration information to a common token.\n   */\n  multi?: boolean;\n}\n\n/**\n * Describes how the `Injector` should be configured.\n * @see [Dependency Injection Guide](guide/di/dependency-injection.\n *\n * @see {@link StaticProvider}\n *\n * @publicApi\n */\nexport type Provider =\n  | TypeProvider\n  | ValueProvider\n  | ClassProvider\n  | ConstructorProvider\n  | ExistingProvider\n  | FactoryProvider\n  | any[];\n\n/**\n * Encapsulated `Provider`s that are only accepted during creation of an `EnvironmentInjector` (e.g.\n * in an `NgModule`).\n *\n * Using this wrapper type prevents providers which are only designed to work in\n * application/environment injectors from being accidentally included in\n * `@Component.providers` and ending up in a component injector.\n *\n * This wrapper type prevents access to the `Provider`s inside.\n *\n * @see {@link makeEnvironmentProviders}\n * @see {@link importProvidersFrom}\n *\n * @publicApi\n */\nexport type EnvironmentProviders = {\n  ɵbrand: 'EnvironmentProviders';\n};\n\nexport interface InternalEnvironmentProviders extends EnvironmentProviders {\n  ɵproviders: (Provider | EnvironmentProviders)[];\n\n  /**\n   * If present, indicates that the `EnvironmentProviders` were derived from NgModule providers.\n   *\n   * This is used to produce clearer error messages.\n   */\n  ɵfromNgModule?: true;\n}\n\nexport function isEnvironmentProviders(\n  value: Provider | EnvironmentProviders | InternalEnvironmentProviders,\n): value is InternalEnvironmentProviders {\n  return value && !!(value as InternalEnvironmentProviders).ɵproviders;\n}\n\n/**\n * Describes a function that is used to process provider lists (such as provider\n * overrides).\n */\nexport type ProcessProvidersFunction = (providers: Provider[]) => Provider[];\n\n/**\n * A wrapper around an NgModule that associates it with providers\n * Usage without a generic type is deprecated.\n *\n * @publicApi\n */\nexport interface ModuleWithProviders<T> {\n  ngModule: Type<T>;\n  providers?: Array<Provider | EnvironmentProviders>;\n}\n\n/**\n * Providers that were imported from NgModules via the `importProvidersFrom` function.\n *\n * These providers are meant for use in an application injector (or other environment injectors) and\n * should not be used in component injectors.\n *\n * This type cannot be directly implemented. It's returned from the `importProvidersFrom` function\n * and serves to prevent the extracted NgModule providers from being used in the wrong contexts.\n *\n * @see {@link importProvidersFrom}\n *\n * @publicApi\n * @deprecated replaced by `EnvironmentProviders`\n */\nexport type ImportedNgModuleProviders = EnvironmentProviders;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {getClosureSafeProperty} from '../util/property';\n\nexport const NG_COMP_DEF: string = getClosureSafeProperty({ɵcmp: getClosureSafeProperty});\nexport const NG_DIR_DEF: string = getClosureSafeProperty({ɵdir: getClosureSafeProperty});\nexport const NG_PIPE_DEF: string = getClosureSafeProperty({ɵpipe: getClosureSafeProperty});\nexport const NG_MOD_DEF: string = getClosureSafeProperty({ɵmod: getClosureSafeProperty});\nexport const NG_FACTORY_DEF: string = getClosureSafeProperty({ɵfac: getClosureSafeProperty});\n\n/**\n * If a directive is diPublic, bloom<PERSON><PERSON> sets a property on the type with this constant as\n * the key and the directive's unique ID as the value. This allows us to map directives to their\n * bloom filter bit for DI.\n */\n// TODO(misko): This is wrong. The NG_ELEMENT_ID should never be minified.\nexport const NG_ELEMENT_ID: string = getClosureSafeProperty({\n  __NG_ELEMENT_ID__: getClosureSafeProperty,\n});\n\n/**\n * The `NG_ENV_ID` field on a DI token indicates special processing in the `EnvironmentInjector`:\n * getting such tokens from the `EnvironmentInjector` will bypass the standard DI resolution\n * strategy and instead will return implementation produced by the `NG_ENV_ID` factory function.\n *\n * This particular retrieval of DI tokens is mostly done to eliminate circular dependencies and\n * improve tree-shaking.\n */\nexport const NG_ENV_ID: string = getClosureSafeProperty({__NG_ENV_ID__: getClosureSafeProperty});\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../../interface/type';\nimport {NG_COMP_DEF} from '../fields';\n\n/**\n * Used for stringify render output in Ivy.\n * Important! This function is very performance-sensitive and we should\n * be extra careful not to introduce megamorphic reads in it.\n * Check `core/test/render3/perf/render_stringify` for benchmarks and alternate implementations.\n */\nexport function renderStringify(value: any): string {\n  if (typeof value === 'string') return value;\n  if (value == null) return '';\n  // Use `String` so that it invokes the `toString` method of the value. Note that this\n  // appears to be faster than calling `value.toString` (see `render_stringify` benchmark).\n  return String(value);\n}\n\n/**\n * Used to stringify a value so that it can be displayed in an error message.\n *\n * Important! This function contains a megamorphic read and should only be\n * used for error messages.\n */\nexport function stringifyForError(value: any): string {\n  if (typeof value === 'function') return value.name || value.toString();\n  if (typeof value === 'object' && value != null && typeof value.type === 'function') {\n    return value.type.name || value.type.toString();\n  }\n\n  return renderStringify(value);\n}\n\n/**\n * Used to stringify a `Type` and including the file path and line number in which it is defined, if\n * possible, for better debugging experience.\n *\n * Important! This function contains a megamorphic read and should only be used for error messages.\n */\nexport function debugStringifyTypeForError(type: Type<any>): string {\n  // TODO(pmvald): Do some refactoring so that we can use getComponentDef here without creating\n  // circular deps.\n  let componentDef = (type as any)[NG_COMP_DEF] || null;\n  if (componentDef !== null && componentDef.debugInfo) {\n    return stringifyTypeFromDebugInfo(componentDef.debugInfo);\n  }\n\n  return stringifyForError(type);\n}\n\n// TODO(pmvald): Do some refactoring so that we can use the type ClassDebugInfo for the param\n// debugInfo here without creating circular deps.\nfunction stringifyTypeFromDebugInfo(debugInfo: any): string {\n  if (!debugInfo.filePath || !debugInfo.lineNumber) {\n    return debugInfo.className;\n  } else {\n    return `${debugInfo.className} (at ${debugInfo.filePath}:${debugInfo.lineNumber})`;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type {ProviderToken} from '../di';\nimport {isEnvironmentProviders} from '../di/interface/provider';\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {Type} from '../interface/type';\nimport {stringify} from '../util/stringify';\n\nimport {stringifyForError} from './util/stringify_utils';\n\n/** Called when directives inject each other (creating a circular dependency) */\nexport function throwCyclicDependencyError(token: string, path?: string[]): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.CYCLIC_DI_DEPENDENCY,\n    ngDevMode\n      ? `Circular dependency in DI detected for ${token}${path ? `. Dependency path: ${path.join(' > ')} > ${token}` : ''}`\n      : token,\n  );\n}\n\nexport function throwMixedMultiProviderError() {\n  throw new Error(`Cannot mix multi providers and regular providers`);\n}\n\nexport function throwInvalidProviderError(\n  ngModuleType?: Type<unknown>,\n  providers?: any[],\n  provider?: any,\n): never {\n  if (ngModuleType && providers) {\n    const providerDetail = providers.map((v) => (v == provider ? '?' + provider + '?' : '...'));\n    throw new Error(\n      `Invalid provider for the NgModule '${stringify(\n        ngModuleType,\n      )}' - only instances of Provider and Type are allowed, got: [${providerDetail.join(', ')}]`,\n    );\n  } else if (isEnvironmentProviders(provider)) {\n    if (provider.ɵfromNgModule) {\n      throw new RuntimeError(\n        RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT,\n        `Invalid providers from 'importProvidersFrom' present in a non-environment injector. 'importProvidersFrom' can't be used for component providers.`,\n      );\n    } else {\n      throw new RuntimeError(\n        RuntimeErrorCode.PROVIDER_IN_WRONG_CONTEXT,\n        `Invalid providers present in a non-environment injector. 'EnvironmentProviders' can't be used for component providers.`,\n      );\n    }\n  } else {\n    throw new Error('Invalid provider');\n  }\n}\n\n/** Throws an error when a token is not found in DI. */\nexport function throwProviderNotFoundError(\n  token: ProviderToken<unknown>,\n  injectorName?: string,\n): never {\n  const errorMessage =\n    ngDevMode &&\n    `No provider for ${stringifyForError(token)} found${injectorName ? ` in ${injectorName}` : ''}`;\n  throw new RuntimeError(RuntimeErrorCode.PROVIDER_NOT_FOUND, errorMessage);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {throwProviderNotFoundError} from '../render3/errors_di';\nimport {assertNotEqual} from '../util/assert';\n\nimport {getInjectableDef, ɵɵInjectableDeclaration} from './interface/defs';\nimport {InternalInjectFlags} from './interface/injector';\nimport {ProviderToken} from './provider_token';\n\n/**\n * Current implementation of inject.\n *\n * By default, it is `injectInjectorOnly`, which makes it `Injector`-only aware. It can be changed\n * to `directiveInject`, which brings in the `NodeInjector` system of ivy. It is designed this\n * way for two reasons:\n *  1. `Injector` should not depend on ivy logic.\n *  2. To maintain tree shake-ability we don't want to bring in unnecessary code.\n */\nlet _injectImplementation:\n  | (<T>(token: ProviderToken<T>, flags?: InternalInjectFlags) => T | null)\n  | undefined;\nexport function getInjectImplementation() {\n  return _injectImplementation;\n}\n\n/**\n * Sets the current inject implementation.\n */\nexport function setInjectImplementation(\n  impl: (<T>(token: ProviderToken<T>, flags?: InternalInjectFlags) => T | null) | undefined,\n): (<T>(token: ProviderToken<T>, flags?: InternalInjectFlags) => T | null) | undefined {\n  const previous = _injectImplementation;\n  _injectImplementation = impl;\n  return previous;\n}\n\n/**\n * Injects `root` tokens in limp mode.\n *\n * If no injector exists, we can still inject tree-shakable providers which have `providedIn` set to\n * `\"root\"`. This is known as the limp mode injection. In such case the value is stored in the\n * injectable definition.\n */\nexport function injectRootLimpMode<T>(\n  token: ProviderToken<T>,\n  notFoundValue: T | undefined,\n  flags: InternalInjectFlags,\n): T | null {\n  const injectableDef: ɵɵInjectableDeclaration<T> | null = getInjectableDef(token);\n  if (injectableDef && injectableDef.providedIn == 'root') {\n    return injectableDef.value === undefined\n      ? (injectableDef.value = injectableDef.factory())\n      : injectableDef.value;\n  }\n  if (flags & InternalInjectFlags.Optional) return null;\n  if (notFoundValue !== undefined) return notFoundValue;\n  throwProviderNotFoundError(token, 'Injector');\n}\n\n/**\n * Assert that `_injectImplementation` is not `fn`.\n *\n * This is useful, to prevent infinite recursion.\n *\n * @param fn Function which it should not equal to\n */\nexport function assertInjectImplementationNotEqual(\n  fn: <T>(token: ProviderToken<T>, flags?: InternalInjectFlags) => T | null,\n) {\n  ngDevMode &&\n    assertNotEqual(_injectImplementation, fn, 'Calling ɵɵinject would cause infinite recursion');\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport '../util/ng_dev_mode';\n\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {Type} from '../interface/type';\nimport {emitInjectEvent} from '../render3/debug/injector_profiler';\nimport {stringify} from '../util/stringify';\n\nimport {resolveForwardRef} from './forward_ref';\nimport {getInjectImplementation, injectRootLimpMode} from './inject_switch';\nimport type {Injector} from './injector';\nimport {DecoratorFlags, InternalInjectFlags, InjectOptions} from './interface/injector';\nimport {ProviderToken} from './provider_token';\nimport type {HostAttributeToken} from './host_attribute_token';\nimport {\n  Injector as PrimitivesInjector,\n  isNotFound,\n  NotFound,\n  InjectionToken as PrimitivesInjectionToken,\n  getCurrentInjector,\n} from '../../primitives/di';\n\nimport {InjectionToken} from './injection_token';\n\nconst _THROW_IF_NOT_FOUND = {};\nexport const THROW_IF_NOT_FOUND = _THROW_IF_NOT_FOUND;\n\nexport {getCurrentInjector, setCurrentInjector} from '../../primitives/di';\n\n/*\n * Name of a property (that we patch onto DI decorator), which is used as an annotation of which\n * InjectFlag this decorator represents. This allows to avoid direct references to the DI decorators\n * in the code, thus making them tree-shakable.\n */\nconst DI_DECORATOR_FLAG = '__NG_DI_FLAG__';\n\n/**\n * A wrapper around an `Injector` that implements the `PrimitivesInjector` interface.\n *\n * This is used to allow the `inject` function to be used with the new primitives-based DI system.\n */\nexport class RetrievingInjector implements PrimitivesInjector {\n  constructor(readonly injector: Injector) {}\n  retrieve<T>(token: PrimitivesInjectionToken<T>, options: unknown): T | NotFound {\n    const flags: InternalInjectFlags =\n      convertToBitFlags(options as InjectOptions | undefined) || InternalInjectFlags.Default;\n    try {\n      return (this.injector as BackwardsCompatibleInjector).get(\n        token as unknown as InjectionToken<T>,\n        // When a dependency is requested with an optional flag, DI returns null as the default value.\n        (flags & InternalInjectFlags.Optional ? null : THROW_IF_NOT_FOUND) as T,\n        flags,\n      ) as T;\n    } catch (e: any) {\n      if (isNotFound(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n}\n\nexport const NG_TEMP_TOKEN_PATH = 'ngTempTokenPath';\nconst NG_TOKEN_PATH = 'ngTokenPath';\nconst NEW_LINE = /\\n/gm;\nconst NO_NEW_LINE = 'ɵ';\nexport const SOURCE = '__source';\n\n/**\n * Temporary type to allow internal symbols to use inject flags. This should be\n * removed once we consolidate the flags and the object literal approach.\n */\nexport type BackwardsCompatibleInjector = Injector & {\n  get<T>(\n    token: ProviderToken<T>,\n    notFoundValue?: T,\n    options?: InternalInjectFlags | InjectOptions,\n  ): T;\n};\n\nexport function injectInjectorOnly<T>(token: ProviderToken<T>): T;\nexport function injectInjectorOnly<T>(\n  token: ProviderToken<T>,\n  flags?: InternalInjectFlags,\n): T | null;\nexport function injectInjectorOnly<T>(\n  token: ProviderToken<T>,\n  flags = InternalInjectFlags.Default,\n): T | null {\n  const currentInjector = getCurrentInjector();\n  if (currentInjector === undefined) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MISSING_INJECTION_CONTEXT,\n      ngDevMode &&\n        `The \\`${stringify(token)}\\` token injection failed. \\`inject()\\` function must be called from an injection context such as a constructor, a factory function, a field initializer, or a function used with \\`runInInjectionContext\\`.`,\n    );\n  } else if (currentInjector === null) {\n    return injectRootLimpMode(token, undefined, flags);\n  } else {\n    const options = convertToInjectOptions(flags);\n    const value = currentInjector.retrieve(token as PrimitivesInjectionToken<T>, options) as T;\n    ngDevMode && emitInjectEvent(token as Type<unknown>, value, flags);\n    if (isNotFound(value)) {\n      if (options.optional) {\n        return null;\n      }\n      throw value;\n    }\n    return value;\n  }\n}\n\n/**\n * Generated instruction: injects a token from the currently active injector.\n *\n * (Additional documentation moved to `inject`, as it is the public API, and an alias for this\n * instruction)\n *\n * @see inject\n * @codeGenApi\n * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.\n */\nexport function ɵɵinject<T>(token: ProviderToken<T>): T;\nexport function ɵɵinject<T>(token: ProviderToken<T>, flags?: InternalInjectFlags): T | null;\nexport function ɵɵinject(token: HostAttributeToken): string;\nexport function ɵɵinject(token: HostAttributeToken, flags?: InternalInjectFlags): string | null;\nexport function ɵɵinject<T>(\n  token: ProviderToken<T> | HostAttributeToken,\n  flags?: InternalInjectFlags,\n): string | null;\nexport function ɵɵinject<T>(\n  token: ProviderToken<T> | HostAttributeToken,\n  flags = InternalInjectFlags.Default,\n): T | null {\n  return (getInjectImplementation() || injectInjectorOnly)(\n    resolveForwardRef(token as Type<T>),\n    flags,\n  );\n}\n\n/**\n * Throws an error indicating that a factory function could not be generated by the compiler for a\n * particular class.\n *\n * The name of the class is not mentioned here, but will be in the generated factory function name\n * and thus in the stack trace.\n *\n * @codeGenApi\n */\nexport function ɵɵinvalidFactoryDep(index: number): never {\n  throw new RuntimeError(\n    RuntimeErrorCode.INVALID_FACTORY_DEPENDENCY,\n    ngDevMode &&\n      `This constructor is not compatible with Angular Dependency Injection because its dependency at index ${index} of the parameter list is invalid.\nThis can happen if the dependency type is a primitive like a string or if an ancestor of this class is missing an Angular decorator.\n\nPlease check that 1) the type for the parameter at index ${index} is correct and 2) the correct Angular decorators are defined for this class and its ancestors.`,\n  );\n}\n\n/**\n * @param token A token that represents a dependency that should be injected.\n * @returns the injected value if operation is successful, `null` otherwise.\n * @throws if called outside of a supported context.\n *\n * @publicApi\n */\nexport function inject<T>(token: ProviderToken<T>): T;\n/**\n * @param token A token that represents a dependency that should be injected.\n * @param options Control how injection is executed. Options correspond to injection strategies\n *     that can be specified with parameter decorators `@Host`, `@Self`, `@SkipSelf`, and\n *     `@Optional`.\n * @returns the injected value if operation is successful.\n * @throws if called outside of a supported context, or if the token is not found.\n *\n * @publicApi\n */\nexport function inject<T>(token: ProviderToken<T>, options: InjectOptions & {optional?: false}): T;\n/**\n * @param token A token that represents a dependency that should be injected.\n * @param options Control how injection is executed. Options correspond to injection strategies\n *     that can be specified with parameter decorators `@Host`, `@Self`, `@SkipSelf`, and\n *     `@Optional`.\n * @returns the injected value if operation is successful,  `null` if the token is not\n *     found and optional injection has been requested.\n * @throws if called outside of a supported context, or if the token is not found and optional\n *     injection was not requested.\n *\n * @publicApi\n */\nexport function inject<T>(token: ProviderToken<T>, options: InjectOptions): T | null;\n/**\n * @param token A token that represents a static attribute on the host node that should be injected.\n * @returns Value of the attribute if it exists.\n * @throws If called outside of a supported context or the attribute does not exist.\n *\n * @publicApi\n */\nexport function inject(token: HostAttributeToken): string;\n/**\n * @param token A token that represents a static attribute on the host node that should be injected.\n * @returns Value of the attribute if it exists, otherwise `null`.\n * @throws If called outside of a supported context.\n *\n * @publicApi\n */\nexport function inject(token: HostAttributeToken, options: {optional: true}): string | null;\n/**\n * @param token A token that represents a static attribute on the host node that should be injected.\n * @returns Value of the attribute if it exists.\n * @throws If called outside of a supported context or the attribute does not exist.\n *\n * @publicApi\n */\nexport function inject(token: HostAttributeToken, options: {optional: false}): string;\n/**\n * Injects a token from the currently active injector.\n * `inject` is only supported in an [injection context](guide/di/dependency-injection-context). It\n * can be used during:\n * - Construction (via the `constructor`) of a class being instantiated by the DI system, such\n * as an `@Injectable` or `@Component`.\n * - In the initializer for fields of such classes.\n * - In the factory function specified for `useFactory` of a `Provider` or an `@Injectable`.\n * - In the `factory` function specified for an `InjectionToken`.\n * - In a stackframe of a function call in a DI context\n *\n * @param token A token that represents a dependency that should be injected.\n * @param flags Optional flags that control how injection is executed.\n * The flags correspond to injection strategies that can be specified with\n * parameter decorators `@Host`, `@Self`, `@SkipSelf`, and `@Optional`.\n * @returns the injected value if operation is successful, `null` otherwise.\n * @throws if called outside of a supported context.\n *\n * @usageNotes\n * In practice the `inject()` calls are allowed in a constructor, a constructor parameter and a\n * field initializer:\n *\n * ```ts\n * @Injectable({providedIn: 'root'})\n * export class Car {\n *   radio: Radio|undefined;\n *   // OK: field initializer\n *   spareTyre = inject(Tyre);\n *\n *   constructor() {\n *     // OK: constructor body\n *     this.radio = inject(Radio);\n *   }\n * }\n * ```\n *\n * It is also legal to call `inject` from a provider's factory:\n *\n * ```ts\n * providers: [\n *   {provide: Car, useFactory: () => {\n *     // OK: a class factory\n *     const engine = inject(Engine);\n *     return new Car(engine);\n *   }}\n * ]\n * ```\n *\n * Calls to the `inject()` function outside of the class creation context will result in error. Most\n * notably, calls to `inject()` are disallowed after a class instance was created, in methods\n * (including lifecycle hooks):\n *\n * ```ts\n * @Component({ ... })\n * export class CarComponent {\n *   ngOnInit() {\n *     // ERROR: too late, the component instance was already created\n *     const engine = inject(Engine);\n *     engine.start();\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nexport function inject<T>(token: ProviderToken<T> | HostAttributeToken, options?: InjectOptions) {\n  // The `as any` here _shouldn't_ be necessary, but without it JSCompiler\n  // throws a disambiguation  error due to the multiple signatures.\n  return ɵɵinject(token as any, convertToBitFlags(options));\n}\n\n// Converts object-based DI flags (`InjectOptions`) to bit flags (`InjectFlags`).\nexport function convertToBitFlags(\n  flags: InjectOptions | InternalInjectFlags | undefined,\n): InternalInjectFlags | undefined {\n  if (typeof flags === 'undefined' || typeof flags === 'number') {\n    return flags;\n  }\n\n  // While TypeScript doesn't accept it without a cast, bitwise OR with false-y values in\n  // JavaScript is a no-op. We can use that for a very codesize-efficient conversion from\n  // `InjectOptions` to `InjectFlags`.\n  return (InternalInjectFlags.Default | // comment to force a line break in the formatter\n    ((flags.optional && InternalInjectFlags.Optional) as number) |\n    ((flags.host && InternalInjectFlags.Host) as number) |\n    ((flags.self && InternalInjectFlags.Self) as number) |\n    ((flags.skipSelf && InternalInjectFlags.SkipSelf) as number)) as InternalInjectFlags;\n}\n\n// Converts bitflags to inject options\nfunction convertToInjectOptions(flags: InternalInjectFlags): InjectOptions {\n  return {\n    optional: !!(flags & InternalInjectFlags.Optional),\n    host: !!(flags & InternalInjectFlags.Host),\n    self: !!(flags & InternalInjectFlags.Self),\n    skipSelf: !!(flags & InternalInjectFlags.SkipSelf),\n  };\n}\n\nexport function injectArgs(types: (ProviderToken<any> | any[])[]): any[] {\n  const args: any[] = [];\n  for (let i = 0; i < types.length; i++) {\n    const arg = resolveForwardRef(types[i]);\n    if (Array.isArray(arg)) {\n      if (arg.length === 0) {\n        throw new RuntimeError(\n          RuntimeErrorCode.INVALID_DIFFER_INPUT,\n          ngDevMode && 'Arguments array must have arguments.',\n        );\n      }\n      let type: Type<any> | undefined = undefined;\n      let flags: InternalInjectFlags = InternalInjectFlags.Default;\n\n      for (let j = 0; j < arg.length; j++) {\n        const meta = arg[j];\n        const flag = getInjectFlag(meta);\n        if (typeof flag === 'number') {\n          // Special case when we handle @Inject decorator.\n          if (flag === DecoratorFlags.Inject) {\n            type = meta.token;\n          } else {\n            flags |= flag;\n          }\n        } else {\n          type = meta;\n        }\n      }\n\n      args.push(ɵɵinject(type!, flags));\n    } else {\n      args.push(ɵɵinject(arg));\n    }\n  }\n  return args;\n}\n\n/**\n * Attaches a given InjectFlag to a given decorator using monkey-patching.\n * Since DI decorators can be used in providers `deps` array (when provider is configured using\n * `useFactory`) without initialization (e.g. `Host`) and as an instance (e.g. `new Host()`), we\n * attach the flag to make it available both as a static property and as a field on decorator\n * instance.\n *\n * @param decorator Provided DI decorator.\n * @param flag InjectFlag that should be applied.\n */\nexport function attachInjectFlag(decorator: any, flag: InternalInjectFlags | DecoratorFlags): any {\n  decorator[DI_DECORATOR_FLAG] = flag;\n  decorator.prototype[DI_DECORATOR_FLAG] = flag;\n  return decorator;\n}\n\n/**\n * Reads monkey-patched property that contains InjectFlag attached to a decorator.\n *\n * @param token Token that may contain monkey-patched DI flags property.\n */\nexport function getInjectFlag(token: any): number | undefined {\n  return token[DI_DECORATOR_FLAG];\n}\n\nexport function catchInjectorError(\n  e: any,\n  token: any,\n  injectorErrorName: string,\n  source: string | null,\n): never {\n  const tokenPath: any[] = e[NG_TEMP_TOKEN_PATH];\n  if (token[SOURCE]) {\n    tokenPath.unshift(token[SOURCE]);\n  }\n  e.message = formatError('\\n' + e.message, tokenPath, injectorErrorName, source);\n  e[NG_TOKEN_PATH] = tokenPath;\n  e[NG_TEMP_TOKEN_PATH] = null;\n  throw e;\n}\n\nexport function formatError(\n  text: string,\n  obj: any,\n  injectorErrorName: string,\n  source: string | null = null,\n): string {\n  text = text && text.charAt(0) === '\\n' && text.charAt(1) == NO_NEW_LINE ? text.slice(2) : text;\n  let context = stringify(obj);\n  if (Array.isArray(obj)) {\n    context = obj.map(stringify).join(' -> ');\n  } else if (typeof obj === 'object') {\n    let parts = <string[]>[];\n    for (let key in obj) {\n      if (obj.hasOwnProperty(key)) {\n        let value = obj[key];\n        parts.push(\n          key + ':' + (typeof value === 'string' ? JSON.stringify(value) : stringify(value)),\n        );\n      }\n    }\n    context = `{${parts.join(', ')}}`;\n  }\n  return `${injectorErrorName}${source ? '(' + source + ')' : ''}[${context}]: ${text.replace(\n    NEW_LINE,\n    '\\n  ',\n  )}`;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../interface/type';\nimport {stringify} from '../util/stringify';\nimport {NG_FACTORY_DEF} from './fields';\n\n/**\n * Definition of what a factory function should look like.\n */\nexport type FactoryFn<T> = {\n  /**\n   * Subclasses without an explicit constructor call through to the factory of their base\n   * definition, providing it with their own constructor to instantiate.\n   */\n  <U extends T>(t?: Type<U>): U;\n\n  /**\n   * If no constructor to instantiate is provided, an instance of type T itself is created.\n   */\n  (t?: undefined): T;\n};\n\nexport function getFactoryDef<T>(type: any, throwNotFound: true): FactoryFn<T>;\nexport function getFactoryDef<T>(type: any): FactoryFn<T> | null;\nexport function getFactoryDef<T>(type: any, throwNotFound?: boolean): FactoryFn<T> | null {\n  const hasFactoryDef = type.hasOwnProperty(NG_FACTORY_DEF);\n  if (!hasFactoryDef && throwNotFound === true && ngDevMode) {\n    throw new Error(`Type ${stringify(type)} does not have 'ɵfac' property.`);\n  }\n  return hasFactoryDef ? type[NG_FACTORY_DEF] : null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {assertEqual, assertLessThanOrEqual} from './assert';\n\n/**\n * Determines if the contents of two arrays is identical\n *\n * @param a first array\n * @param b second array\n * @param identityAccessor Optional function for extracting stable object identity from a value in\n *     the array.\n */\nexport function arrayEquals<T>(a: T[], b: T[], identityAccessor?: (value: T) => unknown): boolean {\n  if (a.length !== b.length) return false;\n  for (let i = 0; i < a.length; i++) {\n    let valueA = a[i];\n    let valueB = b[i];\n    if (identityAccessor) {\n      valueA = identityAccessor(valueA) as any;\n      valueB = identityAccessor(valueB) as any;\n    }\n    if (valueB !== valueA) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Flattens an array.\n */\nexport function flatten(list: any[]): any[] {\n  return list.flat(Number.POSITIVE_INFINITY);\n}\n\nexport function deepForEach<T>(input: (T | any[])[], fn: (value: T) => void): void {\n  input.forEach((value) => (Array.isArray(value) ? deepForEach(value, fn) : fn(value)));\n}\n\nexport function addToArray(arr: any[], index: number, value: any): void {\n  // perf: array.push is faster than array.splice!\n  if (index >= arr.length) {\n    arr.push(value);\n  } else {\n    arr.splice(index, 0, value);\n  }\n}\n\nexport function removeFromArray(arr: any[], index: number): any {\n  // perf: array.pop is faster than array.splice!\n  if (index >= arr.length - 1) {\n    return arr.pop();\n  } else {\n    return arr.splice(index, 1)[0];\n  }\n}\n\nexport function newArray<T = any>(size: number): T[];\nexport function newArray<T>(size: number, value: T): T[];\nexport function newArray<T>(size: number, value?: T): T[] {\n  const list: T[] = [];\n  for (let i = 0; i < size; i++) {\n    list.push(value!);\n  }\n  return list;\n}\n\n/**\n * Remove item from array (Same as `Array.splice()` but faster.)\n *\n * `Array.splice()` is not as fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * https://jsperf.com/fast-array-splice (About 20x faster)\n *\n * @param array Array to splice\n * @param index Index of element in array to remove.\n * @param count Number of items to remove.\n */\nexport function arraySplice(array: any[], index: number, count: number): void {\n  const length = array.length - count;\n  while (index < length) {\n    array[index] = array[index + count];\n    index++;\n  }\n  while (count--) {\n    array.pop(); // shrink the array\n  }\n}\n\n/**\n * Same as `Array.splice(index, 0, value)` but faster.\n *\n * `Array.splice()` is not fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * @param array Array to splice.\n * @param index Index in array where the `value` should be added.\n * @param value Value to add to array.\n */\nexport function arrayInsert(array: any[], index: number, value: any): void {\n  ngDevMode && assertLessThanOrEqual(index, array.length, \"Can't insert past array end.\");\n  let end = array.length;\n  while (end > index) {\n    const previousEnd = end - 1;\n    array[end] = array[previousEnd];\n    end = previousEnd;\n  }\n  array[index] = value;\n}\n\n/**\n * Same as `Array.splice2(index, 0, value1, value2)` but faster.\n *\n * `Array.splice()` is not fast because it has to allocate an array for the elements which were\n * removed. This causes memory pressure and slows down code when most of the time we don't\n * care about the deleted items array.\n *\n * @param array Array to splice.\n * @param index Index in array where the `value` should be added.\n * @param value1 Value to add to array.\n * @param value2 Value to add to array.\n */\nexport function arrayInsert2(array: any[], index: number, value1: any, value2: any): void {\n  ngDevMode && assertLessThanOrEqual(index, array.length, \"Can't insert past array end.\");\n  let end = array.length;\n  if (end == index) {\n    // inserting at the end.\n    array.push(value1, value2);\n  } else if (end === 1) {\n    // corner case when we have less items in array than we have items to insert.\n    array.push(value2, array[0]);\n    array[0] = value1;\n  } else {\n    end--;\n    array.push(array[end - 1], array[end]);\n    while (end > index) {\n      const previousEnd = end - 2;\n      array[end] = array[previousEnd];\n      end--;\n    }\n    array[index] = value1;\n    array[index + 1] = value2;\n  }\n}\n\n/**\n * Get an index of an `value` in a sorted `array`.\n *\n * NOTE:\n * - This uses binary search algorithm for fast removals.\n *\n * @param array A sorted array to binary search.\n * @param value The value to look for.\n * @returns index of the value.\n *   - positive index if value found.\n *   - negative index if value not found. (`~index` to get the value where it should have been\n *     located)\n */\nexport function arrayIndexOfSorted(array: string[], value: string): number {\n  return _arrayIndexOfSorted(array, value, 0);\n}\n\n/**\n * `KeyValueArray` is an array where even positions contain keys and odd positions contain values.\n *\n * `KeyValueArray` provides a very efficient way of iterating over its contents. For small\n * sets (~10) the cost of binary searching an `KeyValueArray` has about the same performance\n * characteristics that of a `Map` with significantly better memory footprint.\n *\n * If used as a `Map` the keys are stored in alphabetical order so that they can be binary searched\n * for retrieval.\n *\n * See: `keyValueArraySet`, `keyValueArrayGet`, `keyValueArrayIndexOf`, `keyValueArrayDelete`.\n */\nexport interface KeyValueArray<VALUE> extends Array<VALUE | string> {\n  __brand__: 'array-map';\n}\n\n/**\n * Set a `value` for a `key`.\n *\n * @param keyValueArray to modify.\n * @param key The key to locate or create.\n * @param value The value to set for a `key`.\n * @returns index (always even) of where the value vas set.\n */\nexport function keyValueArraySet<V>(\n  keyValueArray: KeyValueArray<V>,\n  key: string,\n  value: V,\n): number {\n  let index = keyValueArrayIndexOf(keyValueArray, key);\n  if (index >= 0) {\n    // if we found it set it.\n    keyValueArray[index | 1] = value;\n  } else {\n    index = ~index;\n    arrayInsert2(keyValueArray, index, key, value);\n  }\n  return index;\n}\n\n/**\n * Retrieve a `value` for a `key` (on `undefined` if not found.)\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @return The `value` stored at the `key` location or `undefined if not found.\n */\nexport function keyValueArrayGet<V>(keyValueArray: KeyValueArray<V>, key: string): V | undefined {\n  const index = keyValueArrayIndexOf(keyValueArray, key);\n  if (index >= 0) {\n    // if we found it retrieve it.\n    return keyValueArray[index | 1] as V;\n  }\n  return undefined;\n}\n\n/**\n * Retrieve a `key` index value in the array or `-1` if not found.\n *\n * @param keyValueArray to search.\n * @param key The key to locate.\n * @returns index of where the key is (or should have been.)\n *   - positive (even) index if key found.\n *   - negative index if key not found. (`~index` (even) to get the index where it should have\n *     been inserted.)\n */\nexport function keyValueArrayIndexOf<V>(keyValueArray: KeyValueArray<V>, key: string): number {\n  return _arrayIndexOfSorted(keyValueArray as string[], key, 1);\n}\n\n/**\n * Delete a `key` (and `value`) from the `KeyValueArray`.\n *\n * @param keyValueArray to modify.\n * @param key The key to locate or delete (if exist).\n * @returns index of where the key was (or should have been.)\n *   - positive (even) index if key found and deleted.\n *   - negative index if key not found. (`~index` (even) to get the index where it should have\n *     been.)\n */\nexport function keyValueArrayDelete<V>(keyValueArray: KeyValueArray<V>, key: string): number {\n  const index = keyValueArrayIndexOf(keyValueArray, key);\n  if (index >= 0) {\n    // if we found it remove it.\n    arraySplice(keyValueArray, index, 2);\n  }\n  return index;\n}\n\n/**\n * INTERNAL: Get an index of an `value` in a sorted `array` by grouping search by `shift`.\n *\n * NOTE:\n * - This uses binary search algorithm for fast removals.\n *\n * @param array A sorted array to binary search.\n * @param value The value to look for.\n * @param shift grouping shift.\n *   - `0` means look at every location\n *   - `1` means only look at every other (even) location (the odd locations are to be ignored as\n *         they are values.)\n * @returns index of the value.\n *   - positive index if value found.\n *   - negative index if value not found. (`~index` to get the value where it should have been\n * inserted)\n */\nfunction _arrayIndexOfSorted(array: string[], value: string, shift: number): number {\n  ngDevMode && assertEqual(Array.isArray(array), true, 'Expecting an array');\n  let start = 0;\n  let end = array.length >> shift;\n  while (end !== start) {\n    const middle = start + ((end - start) >> 1); // find the middle.\n    const current = array[middle << shift];\n    if (value === current) {\n      return middle << shift;\n    } else if (current > value) {\n      end = middle;\n    } else {\n      start = middle + 1; // We already searched middle so make it non-inclusive by adding 1\n    }\n  }\n  return ~(end << shift);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {initNgDevMode} from './ng_dev_mode';\n\n/**\n * This file contains reuseable \"empty\" symbols that can be used as default return values\n * in different parts of the rendering code. Because the same symbols are returned, this\n * allows for identity checks against these values to be consistently used by the framework\n * code.\n */\n\nexport const EMPTY_OBJ: never = {} as never;\nexport const EMPTY_ARRAY: any[] = [];\n\n// freezing the values prevents any code from accidentally inserting new values in\nif ((typeof ngDevMode === 'undefined' || ngDevMode) && initNgDevMode()) {\n  // These property accesses can be ignored because ngDevMode will be set to false\n  // when optimizing code and the whole if statement will be dropped.\n  // tslint:disable-next-line:no-toplevel-property-access\n  Object.freeze(EMPTY_OBJ);\n  // tslint:disable-next-line:no-toplevel-property-access\n  Object.freeze(EMPTY_ARRAY);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from './injection_token';\n\n/**\n * A multi-provider token for initialization functions that will run upon construction of an\n * environment injector.\n *\n * @deprecated from v19.0.0, use provideEnvironmentInitializer instead\n *\n * @see {@link provideEnvironmentInitializer}\n *\n * Note: As opposed to the `APP_INITIALIZER` token, the `ENVIRONMENT_INITIALIZER` functions are not awaited,\n * hence they should not be `async`.\n *\n * @publicApi\n */\nexport const ENVIRONMENT_INITIALIZER = new InjectionToken<ReadonlyArray<() => void>>(\n  ngDevMode ? 'ENVIRONMENT_INITIALIZER' : '',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from './injection_token';\nimport type {Injector} from './injector';\nimport {InjectorMarkers} from './injector_marker';\n\n/**\n * An InjectionToken that gets the current `Injector` for `createInjector()`-style injectors.\n *\n * Requesting this token instead of `Injector` allows `StaticInjector` to be tree-shaken from a\n * project.\n *\n * @publicApi\n */\nexport const INJECTOR = new InjectionToken<Injector>(\n  ngDevMode ? 'INJECTOR' : '',\n  // Disable tslint because this is const enum which gets inlined not top level prop access.\n  // tslint:disable-next-line: no-toplevel-property-access\n  InjectorMarkers.Injector as any, // Special value used by <PERSON> to identify `Injector`.\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Type} from '../interface/type';\n\nimport {InjectionToken} from './injection_token';\n\nexport const INJECTOR_DEF_TYPES = new InjectionToken<ReadonlyArray<Type<unknown>>>(\n  ngDevMode ? 'INJECTOR_DEF_TYPES' : '',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NotFoundError} from '@angular/core/primitives/di';\nimport {stringify} from '../util/stringify';\nimport type {Injector} from './injector';\nimport {THROW_IF_NOT_FOUND} from './injector_compatibility';\n\nexport class NullInjector implements Injector {\n  get(token: any, notFoundValue: any = THROW_IF_NOT_FOUND): any {\n    if (notFoundValue === THROW_IF_NOT_FOUND) {\n      const error = new NotFoundError(`NullInjectorError: No provider for ${stringify(token)}!`);\n      throw error;\n    }\n    return notFoundValue;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {Type} from '../interface/type';\nimport type {NgModuleDef} from '../r3_symbols';\nimport {stringify} from '../util/stringify';\nimport {NG_COMP_DEF, NG_DIR_DEF, NG_MOD_DEF, NG_PIPE_DEF} from './fields';\nimport type {ComponentDef, DirectiveDef, PipeDef} from './interfaces/definition';\n\nexport function getNgModuleDef<T>(type: any): NgModuleDef<T> | null {\n  return type[NG_MOD_DEF] || null;\n}\n\nexport function getNgModuleDefOrThrow<T>(type: any): NgModuleDef<T> | never {\n  const ngModuleDef = getNgModuleDef<T>(type);\n  if (!ngModuleDef) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MISSING_NG_MODULE_DEFINITION,\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        `Type ${stringify(type)} does not have 'ɵmod' property.`,\n    );\n  }\n  return ngModuleDef;\n}\n\n/**\n * The following getter methods retrieve the definition from the type. Currently the retrieval\n * honors inheritance, but in the future we may change the rule to require that definitions are\n * explicit. This would require some sort of migration strategy.\n */\n\nexport function getComponentDef<T>(type: any): ComponentDef<T> | null {\n  return type[NG_COMP_DEF] || null;\n}\n\nexport function getDirectiveDefOrThrow<T>(type: any): DirectiveDef<T> | never {\n  const def = getDirectiveDef<T>(type);\n  if (!def) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MISSING_DIRECTIVE_DEFINITION,\n      (typeof ngDevMode === 'undefined' || ngDevMode) &&\n        `Type ${stringify(type)} does not have 'ɵdir' property.`,\n    );\n  }\n  return def;\n}\n\nexport function getDirectiveDef<T>(type: any): DirectiveDef<T> | null {\n  return type[NG_DIR_DEF] || null;\n}\n\nexport function getPipeDef<T>(type: any): PipeDef<T> | null {\n  return type[NG_PIPE_DEF] || null;\n}\n\n/**\n * Checks whether a given Component, Directive or Pipe is marked as standalone.\n * This will return false if passed anything other than a Component, Directive, or Pipe class\n * See [this guide](guide/components/importing) for additional information:\n *\n * @param type A reference to a Component, Directive or Pipe.\n * @publicApi\n */\nexport function isStandalone(type: Type<unknown>): boolean {\n  const def = getComponentDef(type) || getDirectiveDef(type) || getPipeDef(type);\n  return def !== null && def.standalone;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {Type} from '../interface/type';\nimport {getComponentDef} from '../render3/def_getters';\nimport {getFactoryDef} from '../render3/definition_factory';\nimport {throwCyclicDependencyError, throwInvalidProviderError} from '../render3/errors_di';\nimport {stringifyForError} from '../render3/util/stringify_utils';\nimport {deepForEach} from '../util/array_utils';\nimport {EMPTY_ARRAY} from '../util/empty';\nimport {getClosureSafeProperty} from '../util/property';\nimport {stringify} from '../util/stringify';\n\nimport {resolveForwardRef} from './forward_ref';\nimport {ENVIRONMENT_INITIALIZER} from './initializer_token';\nimport {ɵɵinject as inject} from './injector_compatibility';\nimport {getInjectorDef, InjectorType, InjectorTypeWithProviders} from './interface/defs';\nimport {\n  ClassProvider,\n  ConstructorProvider,\n  EnvironmentProviders,\n  ExistingProvider,\n  FactoryProvider,\n  InternalEnvironmentProviders,\n  isEnvironmentProviders,\n  ModuleWithProviders,\n  Provider,\n  StaticClassProvider,\n  TypeProvider,\n  ValueProvider,\n} from './interface/provider';\nimport {INJECTOR_DEF_TYPES} from './internal_tokens';\n\n/**\n * Wrap an array of `Provider`s into `EnvironmentProviders`, preventing them from being accidentally\n * referenced in `@Component` in a component injector.\n *\n * @publicApi\n */\nexport function makeEnvironmentProviders(\n  providers: (Provider | EnvironmentProviders)[],\n): EnvironmentProviders {\n  return {\n    ɵproviders: providers,\n  } as unknown as EnvironmentProviders;\n}\n\n/**\n * @description\n * This function is used to provide initialization functions that will be executed upon construction\n * of an environment injector.\n *\n * Note that the provided initializer is run in the injection context.\n *\n * Previously, this was achieved using the `ENVIRONMENT_INITIALIZER` token which is now deprecated.\n *\n * @see {@link ENVIRONMENT_INITIALIZER}\n *\n * @usageNotes\n * The following example illustrates how to configure an initialization function using\n * `provideEnvironmentInitializer()`\n * ```ts\n * createEnvironmentInjector(\n *   [\n *     provideEnvironmentInitializer(() => {\n *       console.log('environment initialized');\n *     }),\n *   ],\n *   parentInjector\n * );\n * ```\n *\n * @publicApi\n */\nexport function provideEnvironmentInitializer(initializerFn: () => void): EnvironmentProviders {\n  return makeEnvironmentProviders([\n    {\n      provide: ENVIRONMENT_INITIALIZER,\n      multi: true,\n      useValue: initializerFn,\n    },\n  ]);\n}\n\n/**\n * A source of providers for the `importProvidersFrom` function.\n *\n * @publicApi\n */\nexport type ImportProvidersSource =\n  | Type<unknown>\n  | ModuleWithProviders<unknown>\n  | Array<ImportProvidersSource>;\n\ntype WalkProviderTreeVisitor = (\n  provider: SingleProvider,\n  container: Type<unknown> | InjectorType<unknown>,\n) => void;\n\n/**\n * Collects providers from all NgModules and standalone components, including transitively imported\n * ones.\n *\n * Providers extracted via `importProvidersFrom` are only usable in an application injector or\n * another environment injector (such as a route injector). They should not be used in component\n * providers.\n *\n * More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The results of the `importProvidersFrom` call can be used in the `bootstrapApplication` call:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(NgModuleOne, NgModuleTwo)\n *   ]\n * });\n * ```\n *\n * You can also use the `importProvidersFrom` results in the `providers` field of a route, when a\n * standalone component is used:\n *\n * ```ts\n * export const ROUTES: Route[] = [\n *   {\n *     path: 'foo',\n *     providers: [\n *       importProvidersFrom(NgModuleOne, NgModuleTwo)\n *     ],\n *     component: YourStandaloneComponent\n *   }\n * ];\n * ```\n *\n * @returns Collected providers from the specified list of types.\n * @publicApi\n */\nexport function importProvidersFrom(...sources: ImportProvidersSource[]): EnvironmentProviders {\n  return {\n    ɵproviders: internalImportProvidersFrom(true, sources),\n    ɵfromNgModule: true,\n  } as InternalEnvironmentProviders;\n}\n\nexport function internalImportProvidersFrom(\n  checkForStandaloneCmp: boolean,\n  ...sources: ImportProvidersSource[]\n): Provider[] {\n  const providersOut: SingleProvider[] = [];\n  const dedup = new Set<Type<unknown>>(); // already seen types\n  let injectorTypesWithProviders: InjectorTypeWithProviders<unknown>[] | undefined;\n\n  const collectProviders: WalkProviderTreeVisitor = (provider) => {\n    providersOut.push(provider);\n  };\n\n  deepForEach(sources, (source) => {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && checkForStandaloneCmp) {\n      const cmpDef = getComponentDef(source);\n      if (cmpDef?.standalone) {\n        throw new RuntimeError(\n          RuntimeErrorCode.IMPORT_PROVIDERS_FROM_STANDALONE,\n          `Importing providers supports NgModule or ModuleWithProviders but got a standalone component \"${stringifyForError(\n            source,\n          )}\"`,\n        );\n      }\n    }\n\n    // Narrow `source` to access the internal type analogue for `ModuleWithProviders`.\n    const internalSource = source as Type<unknown> | InjectorTypeWithProviders<unknown>;\n    if (walkProviderTree(internalSource, collectProviders, [], dedup)) {\n      injectorTypesWithProviders ||= [];\n      injectorTypesWithProviders.push(internalSource);\n    }\n  });\n  // Collect all providers from `ModuleWithProviders` types.\n  if (injectorTypesWithProviders !== undefined) {\n    processInjectorTypesWithProviders(injectorTypesWithProviders, collectProviders);\n  }\n\n  return providersOut;\n}\n\n/**\n * Collects all providers from the list of `ModuleWithProviders` and appends them to the provided\n * array.\n */\nfunction processInjectorTypesWithProviders(\n  typesWithProviders: InjectorTypeWithProviders<unknown>[],\n  visitor: WalkProviderTreeVisitor,\n): void {\n  for (let i = 0; i < typesWithProviders.length; i++) {\n    const {ngModule, providers} = typesWithProviders[i];\n    deepForEachProvider(\n      providers! as Array<Provider | InternalEnvironmentProviders>,\n      (provider) => {\n        ngDevMode && validateProvider(provider, providers || EMPTY_ARRAY, ngModule);\n        visitor(provider, ngModule);\n      },\n    );\n  }\n}\n\n/**\n * Internal type for a single provider in a deep provider array.\n */\nexport type SingleProvider =\n  | TypeProvider\n  | ValueProvider\n  | ClassProvider\n  | ConstructorProvider\n  | ExistingProvider\n  | FactoryProvider\n  | StaticClassProvider;\n\n/**\n * The logic visits an `InjectorType`, an `InjectorTypeWithProviders`, or a standalone\n * `ComponentType`, and all of its transitive providers and collects providers.\n *\n * If an `InjectorTypeWithProviders` that declares providers besides the type is specified,\n * the function will return \"true\" to indicate that the providers of the type definition need\n * to be processed. This allows us to process providers of injector types after all imports of\n * an injector definition are processed. (following View Engine semantics: see FW-1349)\n */\nexport function walkProviderTree(\n  container: Type<unknown> | InjectorTypeWithProviders<unknown>,\n  visitor: WalkProviderTreeVisitor,\n  parents: Type<unknown>[],\n  dedup: Set<Type<unknown>>,\n): container is InjectorTypeWithProviders<unknown> {\n  container = resolveForwardRef(container);\n  if (!container) return false;\n\n  // The actual type which had the definition. Usually `container`, but may be an unwrapped type\n  // from `InjectorTypeWithProviders`.\n  let defType: Type<unknown> | null = null;\n\n  let injDef = getInjectorDef(container);\n  const cmpDef = !injDef && getComponentDef(container);\n  if (!injDef && !cmpDef) {\n    // `container` is not an injector type or a component type. It might be:\n    //  * An `InjectorTypeWithProviders` that wraps an injector type.\n    //  * A standalone directive or pipe that got pulled in from a standalone component's\n    //    dependencies.\n    // Try to unwrap it as an `InjectorTypeWithProviders` first.\n    const ngModule: Type<unknown> | undefined = (container as InjectorTypeWithProviders<any>)\n      .ngModule as Type<unknown> | undefined;\n    injDef = getInjectorDef(ngModule);\n    if (injDef) {\n      defType = ngModule!;\n    } else {\n      // Not a component or injector type, so ignore it.\n      return false;\n    }\n  } else if (cmpDef && !cmpDef.standalone) {\n    return false;\n  } else {\n    defType = container as Type<unknown>;\n  }\n\n  // Check for circular dependencies.\n  if (ngDevMode && parents.indexOf(defType) !== -1) {\n    const defName = stringify(defType);\n    const path = parents.map(stringify);\n    throwCyclicDependencyError(defName, path);\n  }\n\n  // Check for multiple imports of the same module\n  const isDuplicate = dedup.has(defType);\n\n  if (cmpDef) {\n    if (isDuplicate) {\n      // This component definition has already been processed.\n      return false;\n    }\n    dedup.add(defType);\n\n    if (cmpDef.dependencies) {\n      const deps =\n        typeof cmpDef.dependencies === 'function' ? cmpDef.dependencies() : cmpDef.dependencies;\n      for (const dep of deps) {\n        walkProviderTree(dep, visitor, parents, dedup);\n      }\n    }\n  } else if (injDef) {\n    // First, include providers from any imports.\n    if (injDef.imports != null && !isDuplicate) {\n      // Before processing defType's imports, add it to the set of parents. This way, if it ends\n      // up deeply importing itself, this can be detected.\n      ngDevMode && parents.push(defType);\n      // Add it to the set of dedups. This way we can detect multiple imports of the same module\n      dedup.add(defType);\n\n      let importTypesWithProviders: InjectorTypeWithProviders<any>[] | undefined;\n      try {\n        deepForEach(injDef.imports, (imported) => {\n          if (walkProviderTree(imported, visitor, parents, dedup)) {\n            importTypesWithProviders ||= [];\n            // If the processed import is an injector type with providers, we store it in the\n            // list of import types with providers, so that we can process those afterwards.\n            importTypesWithProviders.push(imported);\n          }\n        });\n      } finally {\n        // Remove it from the parents set when finished.\n        ngDevMode && parents.pop();\n      }\n\n      // Imports which are declared with providers (TypeWithProviders) need to be processed\n      // after all imported modules are processed. This is similar to how View Engine\n      // processes/merges module imports in the metadata resolver. See: FW-1349.\n      if (importTypesWithProviders !== undefined) {\n        processInjectorTypesWithProviders(importTypesWithProviders, visitor);\n      }\n    }\n\n    if (!isDuplicate) {\n      // Track the InjectorType and add a provider for it.\n      // It's important that this is done after the def's imports.\n      const factory = getFactoryDef(defType) || (() => new defType!());\n\n      // Append extra providers to make more info available for consumers (to retrieve an injector\n      // type), as well as internally (to calculate an injection scope correctly and eagerly\n      // instantiate a `defType` when an injector is created).\n\n      // Provider to create `defType` using its factory.\n      visitor({provide: defType, useFactory: factory, deps: EMPTY_ARRAY}, defType);\n\n      // Make this `defType` available to an internal logic that calculates injector scope.\n      visitor({provide: INJECTOR_DEF_TYPES, useValue: defType, multi: true}, defType);\n\n      // Provider to eagerly instantiate `defType` via `INJECTOR_INITIALIZER`.\n      visitor(\n        {provide: ENVIRONMENT_INITIALIZER, useValue: () => inject(defType!), multi: true},\n        defType,\n      );\n    }\n\n    // Next, include providers listed on the definition itself.\n    const defProviders = injDef.providers as Array<SingleProvider | InternalEnvironmentProviders>;\n    if (defProviders != null && !isDuplicate) {\n      const injectorType = container as InjectorType<any>;\n      deepForEachProvider(defProviders, (provider) => {\n        ngDevMode && validateProvider(provider as SingleProvider, defProviders, injectorType);\n        visitor(provider, injectorType);\n      });\n    }\n  } else {\n    // Should not happen, but just in case.\n    return false;\n  }\n\n  return (\n    defType !== container && (container as InjectorTypeWithProviders<any>).providers !== undefined\n  );\n}\n\nfunction validateProvider(\n  provider: SingleProvider,\n  providers: Array<SingleProvider | InternalEnvironmentProviders>,\n  containerType: Type<unknown>,\n): void {\n  if (\n    isTypeProvider(provider) ||\n    isValueProvider(provider) ||\n    isFactoryProvider(provider) ||\n    isExistingProvider(provider)\n  ) {\n    return;\n  }\n\n  // Here we expect the provider to be a `useClass` provider (by elimination).\n  const classRef = resolveForwardRef(\n    provider && ((provider as StaticClassProvider | ClassProvider).useClass || provider.provide),\n  );\n  if (!classRef) {\n    throwInvalidProviderError(containerType, providers, provider);\n  }\n}\n\nfunction deepForEachProvider(\n  providers: Array<Provider | InternalEnvironmentProviders>,\n  fn: (provider: SingleProvider) => void,\n): void {\n  for (let provider of providers) {\n    if (isEnvironmentProviders(provider)) {\n      provider = provider.ɵproviders;\n    }\n    if (Array.isArray(provider)) {\n      deepForEachProvider(provider, fn);\n    } else {\n      fn(provider);\n    }\n  }\n}\n\nexport const USE_VALUE: string = getClosureSafeProperty<ValueProvider>({\n  provide: String,\n  useValue: getClosureSafeProperty,\n});\n\nexport function isValueProvider(value: SingleProvider): value is ValueProvider {\n  return value !== null && typeof value == 'object' && USE_VALUE in value;\n}\n\nexport function isExistingProvider(value: SingleProvider): value is ExistingProvider {\n  return !!(value && (value as ExistingProvider).useExisting);\n}\n\nexport function isFactoryProvider(value: SingleProvider): value is FactoryProvider {\n  return !!(value && (value as FactoryProvider).useFactory);\n}\n\nexport function isTypeProvider(value: SingleProvider): value is TypeProvider {\n  return typeof value === 'function';\n}\n\nexport function isClassProvider(value: SingleProvider): value is ClassProvider {\n  return !!(value as StaticClassProvider | ClassProvider).useClass;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from './injection_token';\n\nexport type InjectorScope = 'root' | 'platform' | 'environment';\n\n/**\n * An internal token whose presence in an injector indicates that the injector should treat itself\n * as a root scoped injector when processing requests for unknown tokens which may indicate\n * they are provided in the root scope.\n */\nexport const INJECTOR_SCOPE = new InjectionToken<InjectorScope | null>(\n  ngDevMode ? 'Set Injector scope.' : '',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport '../util/ng_dev_mode';\n\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {<PERSON><PERSON><PERSON><PERSON>} from '../interface/lifecycle_hooks';\nimport {Type} from '../interface/type';\nimport {\n  emitInjectorToCreateInstanceEvent,\n  emitInstanceCreatedByInjectorEvent,\n  emitProviderConfiguredEvent,\n  InjectorProfilerContext,\n  runInInjectorProfilerContext,\n  setInjectorProfilerContext,\n} from '../render3/debug/injector_profiler';\nimport {FactoryFn, getFactoryDef} from '../render3/definition_factory';\nimport {\n  throwCyclicDependencyError,\n  throwInvalidProviderError,\n  throwMixedMultiProviderError,\n} from '../render3/errors_di';\nimport {NG_ENV_ID} from '../render3/fields';\nimport {newArray} from '../util/array_utils';\nimport {EMPTY_ARRAY} from '../util/empty';\nimport {stringify} from '../util/stringify';\n\nimport {resolveForwardRef} from './forward_ref';\nimport {ENVIRONMENT_INITIALIZER} from './initializer_token';\nimport {setInjectImplementation} from './inject_switch';\nimport {InjectionToken} from './injection_token';\nimport type {Injector} from './injector';\nimport {\n  BackwardsCompatibleInjector,\n  catchInjectorError,\n  convertToBitFlags,\n  injectArgs,\n  NG_TEMP_TOKEN_PATH,\n  setCurrentInjector,\n  THROW_IF_NOT_FOUND,\n  ɵɵinject,\n} from './injector_compatibility';\nimport {INJECTOR} from './injector_token';\nimport {\n  getInheritedInjectableDef,\n  getInjectableDef,\n  InjectorType,\n  ɵɵInjectableDeclaration,\n} from './interface/defs';\nimport {InternalInjectFlags, InjectOptions} from './interface/injector';\nimport {\n  ClassProvider,\n  ConstructorProvider,\n  EnvironmentProviders,\n  InternalEnvironmentProviders,\n  isEnvironmentProviders,\n  Provider,\n  StaticClassProvider,\n  TypeProvider,\n} from './interface/provider';\nimport {INJECTOR_DEF_TYPES} from './internal_tokens';\nimport {NullInjector} from './null_injector';\nimport {\n  isExistingProvider,\n  isFactoryProvider,\n  isTypeProvider,\n  isValueProvider,\n  SingleProvider,\n} from './provider_collection';\nimport {ProviderToken} from './provider_token';\nimport {INJECTOR_SCOPE, InjectorScope} from './scope';\nimport {setActiveConsumer} from '@angular/core/primitives/signals';\nimport {\n  Injector as PrimitivesInjector,\n  InjectionToken as PrimitivesInjectionToken,\n  NOT_FOUND,\n  NotFound,\n  isNotFound,\n} from '@angular/core/primitives/di';\n\n/**\n * Marker which indicates that a value has not yet been created from the factory function.\n */\nconst NOT_YET = {};\n\n/**\n * Marker which indicates that the factory function for a token is in the process of being called.\n *\n * If the injector is asked to inject a token with its value set to CIRCULAR, that indicates\n * injection of a dependency has recursively attempted to inject the original token, and there is\n * a circular dependency among the providers.\n */\nconst CIRCULAR = {};\n\n/**\n * A lazily initialized NullInjector.\n */\nlet NULL_INJECTOR: Injector | undefined = undefined;\n\nexport function getNullInjector(): Injector {\n  if (NULL_INJECTOR === undefined) {\n    NULL_INJECTOR = new NullInjector();\n  }\n  return NULL_INJECTOR;\n}\n\n/**\n * An entry in the injector which tracks information about the given token, including a possible\n * current value.\n */\ninterface Record<T> {\n  factory: (() => T) | undefined;\n  value: T | {};\n  multi: any[] | undefined;\n}\n\n/**\n * An `Injector` that's part of the environment injector hierarchy, which exists outside of the\n * component tree.\n *\n * @publicApi\n */\nexport abstract class EnvironmentInjector implements Injector {\n  /**\n   * Retrieves an instance from the injector based on the provided token.\n   * @returns The instance from the injector if defined, otherwise the `notFoundValue`.\n   * @throws When the `notFoundValue` is `undefined` or `Injector.THROW_IF_NOT_FOUND`.\n   */\n  abstract get<T>(\n    token: ProviderToken<T>,\n    notFoundValue: undefined,\n    options: InjectOptions & {\n      optional?: false;\n    },\n  ): T;\n  /**\n   * Retrieves an instance from the injector based on the provided token.\n   * @returns The instance from the injector if defined, otherwise the `notFoundValue`.\n   * @throws When the `notFoundValue` is `undefined` or `Injector.THROW_IF_NOT_FOUND`.\n   */\n  abstract get<T>(\n    token: ProviderToken<T>,\n    notFoundValue: null | undefined,\n    options: InjectOptions,\n  ): T | null;\n  /**\n   * Retrieves an instance from the injector based on the provided token.\n   * @returns The instance from the injector if defined, otherwise the `notFoundValue`.\n   * @throws When the `notFoundValue` is `undefined` or `Injector.THROW_IF_NOT_FOUND`.\n   */\n  abstract get<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n  /**\n   * @deprecated from v4.0.0 use ProviderToken<T>\n   * @suppress {duplicate}\n   */\n  abstract get<T>(token: string | ProviderToken<T>, notFoundValue?: any): any;\n\n  /**\n   * Runs the given function in the context of this `EnvironmentInjector`.\n   *\n   * Within the function's stack frame, [`inject`](api/core/inject) can be used to inject\n   * dependencies from this injector. Note that `inject` is only usable synchronously, and cannot be\n   * used in any asynchronous callbacks or after any `await` points.\n   *\n   * @param fn the closure to be run in the context of this injector\n   * @returns the return value of the function, if any\n   * @deprecated use the standalone function `runInInjectionContext` instead\n   */\n  abstract runInContext<ReturnT>(fn: () => ReturnT): ReturnT;\n\n  abstract destroy(): void;\n\n  /** @internal */\n  abstract get destroyed(): boolean;\n\n  /**\n   * @internal\n   */\n  abstract onDestroy(callback: () => void): () => void;\n}\n\nexport class R3Injector extends EnvironmentInjector implements PrimitivesInjector {\n  /**\n   * Map of tokens to records which contain the instances of those tokens.\n   * - `null` value implies that we don't have the record. Used by tree-shakable injectors\n   * to prevent further searches.\n   */\n  private records = new Map<ProviderToken<any>, Record<any> | null>();\n\n  /**\n   * Set of values instantiated by this injector which contain `ngOnDestroy` lifecycle hooks.\n   */\n  private _ngOnDestroyHooks = new Set<OnDestroy>();\n\n  private _onDestroyHooks: Array<() => void> = [];\n\n  /**\n   * Flag indicating that this injector was previously destroyed.\n   */\n  override get destroyed(): boolean {\n    return this._destroyed;\n  }\n  private _destroyed = false;\n\n  private injectorDefTypes: Set<Type<unknown>>;\n\n  constructor(\n    providers: Array<Provider | EnvironmentProviders>,\n    readonly parent: Injector,\n    readonly source: string | null,\n    readonly scopes: Set<InjectorScope>,\n  ) {\n    super();\n    // Start off by creating Records for every provider.\n    forEachSingleProvider(providers as Array<Provider | InternalEnvironmentProviders>, (provider) =>\n      this.processProvider(provider),\n    );\n\n    // Make sure the INJECTOR token provides this injector.\n    this.records.set(INJECTOR, makeRecord(undefined, this));\n\n    // And `EnvironmentInjector` if the current injector is supposed to be env-scoped.\n    if (scopes.has('environment')) {\n      this.records.set(EnvironmentInjector, makeRecord(undefined, this));\n    }\n\n    // Detect whether this injector has the APP_ROOT_SCOPE token and thus should provide\n    // any injectable scoped to APP_ROOT_SCOPE.\n    const record = this.records.get(INJECTOR_SCOPE) as Record<InjectorScope | null>;\n    if (record != null && typeof record.value === 'string') {\n      this.scopes.add(record.value as InjectorScope);\n    }\n\n    this.injectorDefTypes = new Set(this.get(INJECTOR_DEF_TYPES, EMPTY_ARRAY, {self: true}));\n  }\n\n  retrieve<T>(token: PrimitivesInjectionToken<T>, options?: unknown): T | NotFound {\n    const flags: InternalInjectFlags =\n      convertToBitFlags(options as InjectOptions | undefined) || InternalInjectFlags.Default;\n    try {\n      return (this as BackwardsCompatibleInjector).get(\n        token as unknown as InjectionToken<T>,\n        // When a dependency is requested with an optional flag, DI returns null as the default value.\n        THROW_IF_NOT_FOUND as T,\n        flags,\n      );\n    } catch (e: any) {\n      if (isNotFound(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  /**\n   * Destroy the injector and release references to every instance or provider associated with it.\n   *\n   * Also calls the `OnDestroy` lifecycle hooks of every instance that was created for which a\n   * hook was found.\n   */\n  override destroy(): void {\n    assertNotDestroyed(this);\n\n    // Set destroyed = true first, in case lifecycle hooks re-enter destroy().\n    this._destroyed = true;\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      // Call all the lifecycle hooks.\n      for (const service of this._ngOnDestroyHooks) {\n        service.ngOnDestroy();\n      }\n      const onDestroyHooks = this._onDestroyHooks;\n      // Reset the _onDestroyHooks array before iterating over it to prevent hooks that unregister\n      // themselves from mutating the array during iteration.\n      this._onDestroyHooks = [];\n      for (const hook of onDestroyHooks) {\n        hook();\n      }\n    } finally {\n      // Release all references.\n      this.records.clear();\n      this._ngOnDestroyHooks.clear();\n      this.injectorDefTypes.clear();\n      setActiveConsumer(prevConsumer);\n    }\n  }\n\n  override onDestroy(callback: () => void): () => void {\n    assertNotDestroyed(this);\n    this._onDestroyHooks.push(callback);\n    return () => this.removeOnDestroy(callback);\n  }\n\n  override runInContext<ReturnT>(fn: () => ReturnT): ReturnT {\n    assertNotDestroyed(this);\n\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n\n    let prevInjectContext: InjectorProfilerContext | undefined;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({injector: this, token: null});\n    }\n\n    try {\n      return fn();\n    } finally {\n      setCurrentInjector(previousInjector);\n      setInjectImplementation(previousInjectImplementation);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext!);\n    }\n  }\n\n  override get<T>(\n    token: ProviderToken<T>,\n    notFoundValue: any = THROW_IF_NOT_FOUND,\n    options?: InjectOptions,\n  ): T {\n    assertNotDestroyed(this);\n\n    if (token.hasOwnProperty(NG_ENV_ID)) {\n      return (token as any)[NG_ENV_ID](this);\n    }\n\n    const flags = convertToBitFlags(options) as InternalInjectFlags;\n\n    // Set the injection context.\n    let prevInjectContext: InjectorProfilerContext;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({injector: this, token: token as Type<T>});\n    }\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    try {\n      // Check for the SkipSelf flag.\n      if (!(flags & InternalInjectFlags.SkipSelf)) {\n        // SkipSelf isn't set, check if the record belongs to this injector.\n        let record: Record<T> | undefined | null = this.records.get(token);\n        if (record === undefined) {\n          // No record, but maybe the token is scoped to this injector. Look for an injectable\n          // def with a scope matching this injector.\n          const def = couldBeInjectableType(token) && getInjectableDef(token);\n          if (def && this.injectableDefInScope(def)) {\n            // Found an injectable def and it's scoped to this injector. Pretend as if it was here\n            // all along.\n\n            if (ngDevMode) {\n              runInInjectorProfilerContext(this, token as Type<T>, () => {\n                emitProviderConfiguredEvent(token as TypeProvider);\n              });\n            }\n\n            record = makeRecord(injectableDefOrInjectorDefFactory(token), NOT_YET);\n          } else {\n            record = null;\n          }\n          this.records.set(token, record);\n        }\n        // If a record was found, get the instance for it and return it.\n        if (record != null /* NOT null || undefined */) {\n          return this.hydrate(token, record);\n        }\n      }\n\n      // Select the next injector based on the Self flag - if self is set, the next injector is\n      // the NullInjector, otherwise it's the parent.\n      const nextInjector = !(flags & InternalInjectFlags.Self) ? this.parent : getNullInjector();\n      // Set the notFoundValue based on the Optional flag - if optional is set and notFoundValue\n      // is undefined, the value is null, otherwise it's the notFoundValue.\n      notFoundValue =\n        flags & InternalInjectFlags.Optional && notFoundValue === THROW_IF_NOT_FOUND\n          ? null\n          : notFoundValue;\n      return nextInjector.get(token, notFoundValue);\n    } catch (e: any) {\n      if (isNotFound(e)) {\n        // @ts-ignore\n        const path: any[] = (e[NG_TEMP_TOKEN_PATH] = e[NG_TEMP_TOKEN_PATH] || []);\n        path.unshift(stringify(token));\n        if (previousInjector) {\n          // We still have a parent injector, keep throwing\n          throw e;\n        } else {\n          // Format & throw the final error message when we don't have any previous injector\n          return catchInjectorError(e, token, 'R3InjectorError', this.source);\n        }\n      } else {\n        throw e;\n      }\n    } finally {\n      // Lastly, restore the previous injection context.\n      setInjectImplementation(previousInjectImplementation);\n      setCurrentInjector(previousInjector);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext!);\n    }\n  }\n\n  /** @internal */\n  resolveInjectorInitializers() {\n    const prevConsumer = setActiveConsumer(null);\n    const previousInjector = setCurrentInjector(this);\n    const previousInjectImplementation = setInjectImplementation(undefined);\n    let prevInjectContext: InjectorProfilerContext | undefined;\n    if (ngDevMode) {\n      prevInjectContext = setInjectorProfilerContext({injector: this, token: null});\n    }\n\n    try {\n      const initializers = this.get(ENVIRONMENT_INITIALIZER, EMPTY_ARRAY, {self: true});\n      if (ngDevMode && !Array.isArray(initializers)) {\n        throw new RuntimeError(\n          RuntimeErrorCode.INVALID_MULTI_PROVIDER,\n          'Unexpected type of the `ENVIRONMENT_INITIALIZER` token value ' +\n            `(expected an array, but got ${typeof initializers}). ` +\n            'Please check that the `ENVIRONMENT_INITIALIZER` token is configured as a ' +\n            '`multi: true` provider.',\n        );\n      }\n      for (const initializer of initializers) {\n        initializer();\n      }\n    } finally {\n      setCurrentInjector(previousInjector);\n      setInjectImplementation(previousInjectImplementation);\n      ngDevMode && setInjectorProfilerContext(prevInjectContext!);\n      setActiveConsumer(prevConsumer);\n    }\n  }\n\n  override toString() {\n    const tokens: string[] = [];\n    const records = this.records;\n    for (const token of records.keys()) {\n      tokens.push(stringify(token));\n    }\n    return `R3Injector[${tokens.join(', ')}]`;\n  }\n\n  /**\n   * Process a `SingleProvider` and add it.\n   */\n  private processProvider(provider: SingleProvider): void {\n    // Determine the token from the provider. Either it's its own token, or has a {provide: ...}\n    // property.\n    provider = resolveForwardRef(provider);\n    let token: any = isTypeProvider(provider)\n      ? provider\n      : resolveForwardRef(provider && provider.provide);\n\n    // Construct a `Record` for the provider.\n    const record = providerToRecord(provider);\n    if (ngDevMode) {\n      runInInjectorProfilerContext(this, token, () => {\n        // Emit InjectorProfilerEventType.Create if provider is a value provider because\n        // these are the only providers that do not go through the value hydration logic\n        // where this event would normally be emitted from.\n        if (isValueProvider(provider)) {\n          emitInjectorToCreateInstanceEvent(token);\n          emitInstanceCreatedByInjectorEvent(provider.useValue);\n        }\n\n        emitProviderConfiguredEvent(provider);\n      });\n    }\n\n    if (!isTypeProvider(provider) && provider.multi === true) {\n      // If the provider indicates that it's a multi-provider, process it specially.\n      // First check whether it's been defined already.\n      let multiRecord = this.records.get(token);\n      if (multiRecord) {\n        // It has. Throw a nice error if\n        if (ngDevMode && multiRecord.multi === undefined) {\n          throwMixedMultiProviderError();\n        }\n      } else {\n        multiRecord = makeRecord(undefined, NOT_YET, true);\n        multiRecord.factory = () => injectArgs(multiRecord!.multi!);\n        this.records.set(token, multiRecord);\n      }\n      token = provider;\n      multiRecord.multi!.push(provider);\n    } else {\n      if (ngDevMode) {\n        const existing = this.records.get(token);\n        if (existing && existing.multi !== undefined) {\n          throwMixedMultiProviderError();\n        }\n      }\n    }\n    this.records.set(token, record);\n  }\n\n  private hydrate<T>(token: ProviderToken<T>, record: Record<T>): T {\n    const prevConsumer = setActiveConsumer(null);\n    try {\n      if (record.value === CIRCULAR) {\n        throwCyclicDependencyError(stringify(token));\n      } else if (record.value === NOT_YET) {\n        record.value = CIRCULAR;\n\n        if (ngDevMode) {\n          runInInjectorProfilerContext(this, token as Type<T>, () => {\n            emitInjectorToCreateInstanceEvent(token);\n            record.value = record.factory!();\n            emitInstanceCreatedByInjectorEvent(record.value);\n          });\n        } else {\n          record.value = record.factory!();\n        }\n      }\n      if (typeof record.value === 'object' && record.value && hasOnDestroy(record.value)) {\n        this._ngOnDestroyHooks.add(record.value);\n      }\n      return record.value as T;\n    } finally {\n      setActiveConsumer(prevConsumer);\n    }\n  }\n\n  private injectableDefInScope(def: ɵɵInjectableDeclaration<any>): boolean {\n    if (!def.providedIn) {\n      return false;\n    }\n    const providedIn = resolveForwardRef(def.providedIn);\n    if (typeof providedIn === 'string') {\n      return providedIn === 'any' || this.scopes.has(providedIn);\n    } else {\n      return this.injectorDefTypes.has(providedIn);\n    }\n  }\n\n  private removeOnDestroy(callback: () => void): void {\n    const destroyCBIdx = this._onDestroyHooks.indexOf(callback);\n    if (destroyCBIdx !== -1) {\n      this._onDestroyHooks.splice(destroyCBIdx, 1);\n    }\n  }\n}\n\nfunction injectableDefOrInjectorDefFactory(token: ProviderToken<any>): FactoryFn<any> {\n  // Most tokens will have an injectable def directly on them, which specifies a factory directly.\n  const injectableDef = getInjectableDef(token);\n  const factory = injectableDef !== null ? injectableDef.factory : getFactoryDef(token);\n\n  if (factory !== null) {\n    return factory;\n  }\n\n  // InjectionTokens should have an injectable def (ɵprov) and thus should be handled above.\n  // If it's missing that, it's an error.\n  if (token instanceof InjectionToken) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INJECTION_TOKEN,\n      ngDevMode && `Token ${stringify(token)} is missing a ɵprov definition.`,\n    );\n  }\n\n  // Undecorated types can sometimes be created if they have no constructor arguments.\n  if (token instanceof Function) {\n    return getUndecoratedInjectableFactory(token);\n  }\n\n  // There was no way to resolve a factory for this token.\n  throw new RuntimeError(RuntimeErrorCode.INVALID_INJECTION_TOKEN, ngDevMode && 'unreachable');\n}\n\nfunction getUndecoratedInjectableFactory(token: Function) {\n  // If the token has parameters then it has dependencies that we cannot resolve implicitly.\n  const paramLength = token.length;\n  if (paramLength > 0) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INVALID_INJECTION_TOKEN,\n      ngDevMode &&\n        `Can't resolve all parameters for ${stringify(token)}: (${newArray(paramLength, '?').join(\n          ', ',\n        )}).`,\n    );\n  }\n\n  // The constructor function appears to have no parameters.\n  // This might be because it inherits from a super-class. In which case, use an injectable\n  // def from an ancestor if there is one.\n  // Otherwise this really is a simple class with no dependencies, so return a factory that\n  // just instantiates the zero-arg constructor.\n  const inheritedInjectableDef = getInheritedInjectableDef(token);\n  if (inheritedInjectableDef !== null) {\n    return () => inheritedInjectableDef.factory(token as Type<any>);\n  } else {\n    return () => new (token as Type<any>)();\n  }\n}\n\nfunction providerToRecord(provider: SingleProvider): Record<any> {\n  if (isValueProvider(provider)) {\n    return makeRecord(undefined, provider.useValue);\n  } else {\n    const factory: (() => any) | undefined = providerToFactory(provider);\n    return makeRecord(factory, NOT_YET);\n  }\n}\n\n/**\n * Converts a `SingleProvider` into a factory function.\n *\n * @param provider provider to convert to factory\n */\nexport function providerToFactory(\n  provider: SingleProvider,\n  ngModuleType?: InjectorType<any>,\n  providers?: any[],\n): () => any {\n  let factory: (() => any) | undefined = undefined;\n  if (ngDevMode && isEnvironmentProviders(provider)) {\n    throwInvalidProviderError(undefined, providers, provider);\n  }\n\n  if (isTypeProvider(provider)) {\n    const unwrappedProvider = resolveForwardRef(provider);\n    return getFactoryDef(unwrappedProvider) || injectableDefOrInjectorDefFactory(unwrappedProvider);\n  } else {\n    if (isValueProvider(provider)) {\n      factory = () => resolveForwardRef(provider.useValue);\n    } else if (isFactoryProvider(provider)) {\n      factory = () => provider.useFactory(...injectArgs(provider.deps || []));\n    } else if (isExistingProvider(provider)) {\n      factory = () => ɵɵinject(resolveForwardRef(provider.useExisting));\n    } else {\n      const classRef = resolveForwardRef(\n        provider &&\n          ((provider as StaticClassProvider | ClassProvider).useClass || provider.provide),\n      );\n      if (ngDevMode && !classRef) {\n        throwInvalidProviderError(ngModuleType, providers, provider);\n      }\n      if (hasDeps(provider)) {\n        factory = () => new classRef(...injectArgs(provider.deps));\n      } else {\n        return getFactoryDef(classRef) || injectableDefOrInjectorDefFactory(classRef);\n      }\n    }\n  }\n  return factory;\n}\n\nexport function assertNotDestroyed(injector: R3Injector): void {\n  if (injector.destroyed) {\n    throw new RuntimeError(\n      RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED,\n      ngDevMode && 'Injector has already been destroyed.',\n    );\n  }\n}\n\nfunction makeRecord<T>(\n  factory: (() => T) | undefined,\n  value: T | {},\n  multi: boolean = false,\n): Record<T> {\n  return {\n    factory: factory,\n    value: value,\n    multi: multi ? [] : undefined,\n  };\n}\n\nfunction hasDeps(\n  value: ClassProvider | ConstructorProvider | StaticClassProvider,\n): value is ClassProvider & {deps: any[]} {\n  return !!(value as any).deps;\n}\n\nfunction hasOnDestroy(value: any): value is OnDestroy {\n  return (\n    value !== null &&\n    typeof value === 'object' &&\n    typeof (value as OnDestroy).ngOnDestroy === 'function'\n  );\n}\n\nfunction couldBeInjectableType(value: any): value is ProviderToken<any> {\n  return (\n    typeof value === 'function' ||\n    (typeof value === 'object' && value.ngMetadataName === 'InjectionToken')\n  );\n}\n\nfunction forEachSingleProvider(\n  providers: Array<Provider | EnvironmentProviders>,\n  fn: (provider: SingleProvider) => void,\n): void {\n  for (const provider of providers) {\n    if (Array.isArray(provider)) {\n      forEachSingleProvider(provider, fn);\n    } else if (provider && isEnvironmentProviders(provider)) {\n      forEachSingleProvider(provider.ɵproviders, fn);\n    } else {\n      fn(provider as SingleProvider);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {\n  InjectorProfilerContext,\n  setInjectorProfilerContext,\n} from '../render3/debug/injector_profiler';\n\nimport {getInjectImplementation, setInjectImplementation} from './inject_switch';\nimport type {Injector} from './injector';\nimport {getCurrentInjector, setCurrentInjector, RetrievingInjector} from './injector_compatibility';\nimport {assertNotDestroyed, R3Injector} from './r3_injector';\nimport {Injector as PrimitivesInjector} from '@angular/core/primitives/di';\n\n/**\n * Runs the given function in the [context](guide/di/dependency-injection-context) of the given\n * `Injector`.\n *\n * Within the function's stack frame, [`inject`](api/core/inject) can be used to inject dependencies\n * from the given `Injector`. Note that `inject` is only usable synchronously, and cannot be used in\n * any asynchronous callbacks or after any `await` points.\n *\n * @param injector the injector which will satisfy calls to [`inject`](api/core/inject) while `fn`\n *     is executing\n * @param fn the closure to be run in the context of `injector`\n * @returns the return value of the function, if any\n * @publicApi\n */\nexport function runInInjectionContext<ReturnT>(injector: Injector, fn: () => ReturnT): ReturnT {\n  let internalInjector: PrimitivesInjector;\n  if (injector instanceof R3Injector) {\n    assertNotDestroyed(injector);\n    internalInjector = injector;\n  } else {\n    internalInjector = new RetrievingInjector(injector);\n  }\n\n  let prevInjectorProfilerContext: InjectorProfilerContext;\n  if (ngDevMode) {\n    prevInjectorProfilerContext = setInjectorProfilerContext({injector, token: null});\n  }\n  const prevInjector = setCurrentInjector(internalInjector);\n  const previousInjectImplementation = setInjectImplementation(undefined);\n  try {\n    return fn();\n  } finally {\n    setCurrentInjector(prevInjector);\n    ngDevMode && setInjectorProfilerContext(prevInjectorProfilerContext!);\n    setInjectImplementation(previousInjectImplementation);\n  }\n}\n\n/**\n * Whether the current stack frame is inside an injection context.\n */\nexport function isInInjectionContext(): boolean {\n  return getInjectImplementation() !== undefined || getCurrentInjector() != null;\n}\n/**\n * Asserts that the current stack frame is within an [injection\n * context](guide/di/dependency-injection-context) and has access to `inject`.\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nexport function assertInInjectionContext(debugFn: Function): void {\n  // Taking a `Function` instead of a string name here prevents the unminified name of the function\n  // from being retained in the bundle regardless of minification.\n  if (!isInInjectionContext()) {\n    throw new RuntimeError(\n      RuntimeErrorCode.MISSING_INJECTION_CONTEXT,\n      ngDevMode &&\n        debugFn.name +\n          '() can only be used within an injection context such as a constructor, a factory function, a field initializer, or a function used with `runInInjectionContext`',\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type {ChangeDetectionScheduler} from '../../change_detection/scheduling/zoneless_scheduling';\nimport {TDeferBlockDetails} from '../../defer/interfaces';\nimport type {Injector} from '../../di/injector';\nimport {ProviderToken} from '../../di/provider_token';\nimport {DehydratedView} from '../../hydration/interfaces';\nimport {SchemaMetadata} from '../../metadata/schema';\nimport {Sanitizer} from '../../sanitization/sanitizer';\nimport type {AfterRenderSequence} from '../after_render/manager';\nimport type {ReactiveLViewConsumer} from '../reactive_lview_consumer';\nimport type {ViewEffectNode} from '../reactivity/effect';\n\nimport type {LContainer} from './container';\nimport {\n  ComponentDef,\n  ComponentTemplate,\n  DirectiveDef,\n  DirectiveDefList,\n  HostBindingsFunction,\n  PipeDef,\n  PipeDefList,\n  ViewQueriesFunction,\n} from './definition';\nimport {I18nUpdateOpCodes, TI18n, TIcu} from './i18n';\nimport {TConstants, TNode} from './node';\nimport type {LQueries, TQueries} from './query';\nimport {Renderer, RendererFactory} from './renderer';\nimport {RElement} from './renderer_dom';\nimport {TStylingKey, TStylingRange} from './styling';\n\n// Below are constants for LView indices to help us look up LView members\n// without having to remember the specific indices.\n// Uglify will inline these when minifying so there shouldn't be a cost.\nexport const HOST = 0;\nexport const TVIEW = 1;\n\n// Shared with LContainer\nexport const FLAGS = 2;\nexport const PARENT = 3;\nexport const NEXT = 4;\nexport const T_HOST = 5;\n// End shared with LContainer\n\nexport const HYDRATION = 6;\nexport const CLEANUP = 7;\nexport const CONTEXT = 8;\nexport const INJECTOR = 9;\nexport const ENVIRONMENT = 10;\nexport const RENDERER = 11;\nexport const CHILD_HEAD = 12;\nexport const CHILD_TAIL = 13;\n// FIXME(misko): Investigate if the three declarations aren't all same thing.\nexport const DECLARATION_VIEW = 14;\nexport const DECLARATION_COMPONENT_VIEW = 15;\nexport const DECLARATION_LCONTAINER = 16;\nexport const PREORDER_HOOK_FLAGS = 17;\nexport const QUERIES = 18;\nexport const ID = 19;\nexport const EMBEDDED_VIEW_INJECTOR = 20;\nexport const ON_DESTROY_HOOKS = 21;\nexport const EFFECTS_TO_SCHEDULE = 22;\nexport const EFFECTS = 23;\nexport const REACTIVE_TEMPLATE_CONSUMER = 24;\nexport const AFTER_RENDER_SEQUENCES_TO_ADD = 25;\n\n/**\n * Size of LView's header. Necessary to adjust for it when setting slots.\n *\n * IMPORTANT: `HEADER_OFFSET` should only be referred to the in the `ɵɵ*` instructions to translate\n * instruction index into `LView` index. All other indexes should be in the `LView` index space and\n * there should be no need to refer to `HEADER_OFFSET` anywhere else.\n */\nexport const HEADER_OFFSET = 26;\n\n// This interface replaces the real LView interface if it is an arg or a\n// return value of a public instruction. This ensures we don't need to expose\n// the actual interface, which should be kept private.\nexport interface OpaqueViewState {\n  '__brand__': 'Brand for OpaqueViewState that nothing will match';\n}\n\n/**\n * `LView` stores all of the information needed to process the instructions as\n * they are invoked from the template. Each embedded view and component view has its\n * own `LView`. When processing a particular view, we set the `viewData` to that\n * `LView`. When that view is done processing, the `viewData` is set back to\n * whatever the original `viewData` was before (the parent `LView`).\n *\n * Keeping separate state for each view facilities view insertion / deletion, so we\n * don't have to edit the data array based on which views are present.\n */\nexport interface LView<T = unknown> extends Array<any> {\n  /**\n   * The node into which this `LView` is inserted.\n   */\n  [HOST]: RElement | null;\n\n  /**\n   * The static data for this view. We need a reference to this so we can easily walk up the\n   * node tree in DI and get the TView.data array associated with a node (where the\n   * directive defs are stored).\n   */\n  readonly [TVIEW]: TView;\n\n  /** Flags for this view. See LViewFlags for more info. */\n  [FLAGS]: LViewFlags;\n\n  /**\n   * This may store an {@link LView} or {@link LContainer}.\n   *\n   * `LView` - The parent view. This is needed when we exit the view and must restore the previous\n   * LView. Without this, the render method would have to keep a stack of\n   * views as it is recursively rendering templates.\n   *\n   * `LContainer` - The current view is part of a container, and is an embedded view.\n   */\n  [PARENT]: LView | LContainer | null;\n\n  /**\n   *\n   * The next sibling LView or LContainer.\n   *\n   * Allows us to propagate between sibling view states that aren't in the same\n   * container. Embedded views already have a node.next, but it is only set for\n   * views in the same container. We need a way to link component views and views\n   * across containers as well.\n   */\n  [NEXT]: LView | LContainer | null;\n\n  /** Queries active for this view - nodes from a view are reported to those queries. */\n  [QUERIES]: LQueries | null;\n\n  /**\n   * Store the `TNode` of the location where the current `LView` is inserted into.\n   *\n   * Given:\n   * ```html\n   * <div>\n   *   <ng-template><span></span></ng-template>\n   * </div>\n   * ```\n   *\n   * We end up with two `TView`s.\n   * - `parent` `TView` which contains `<div><!-- anchor --></div>`\n   * - `child` `TView` which contains `<span></span>`\n   *\n   * Typically the `child` is inserted into the declaration location of the `parent`, but it can be\n   * inserted anywhere. Because it can be inserted anywhere it is not possible to store the\n   * insertion information in the `TView` and instead we must store it in the `LView[T_HOST]`.\n   *\n   * So to determine where is our insertion parent we would execute:\n   * ```ts\n   * const parentLView = lView[PARENT];\n   * const parentTNode = lView[T_HOST];\n   * const insertionParent = parentLView[parentTNode.index];\n   * ```\n   *\n   *\n   * If `null`, this is the root view of an application (root component is in this view) and it has\n   * no parents.\n   */\n  [T_HOST]: TNode | null;\n\n  /**\n   * When a view is destroyed, listeners need to be released and outputs need to be\n   * unsubscribed. This context array stores both listener functions wrapped with\n   * their context and output subscription instances for a particular view.\n   *\n   * These change per LView instance, so they cannot be stored on TView. Instead,\n   * TView.cleanup saves an index to the necessary context in this array.\n   *\n   * After `LView` is created it is possible to attach additional instance specific functions at the\n   * end of the `lView[CLEANUP]` because we know that no more `T` level cleanup functions will be\n   * added here.\n   */\n  [CLEANUP]: any[] | null;\n\n  /**\n   * - For dynamic views, this is the context with which to render the template (e.g.\n   *   `NgForContext`), or `{}` if not defined explicitly.\n   * - For root view of the root component it's a reference to the component instance itself.\n   * - For components, the context is a reference to the component instance itself.\n   * - For inline views, the context is null.\n   */\n  [CONTEXT]: T;\n\n  /** A Module Injector to be used as fall back after Element Injectors are consulted. */\n  readonly [INJECTOR]: Injector;\n\n  /**\n   * Contextual data that is shared across multiple instances of `LView` in the same application.\n   */\n  [ENVIRONMENT]: LViewEnvironment;\n\n  /** Renderer to be used for this view. */\n  [RENDERER]: Renderer;\n\n  /**\n   * Reference to the first LView or LContainer beneath this LView in\n   * the hierarchy.\n   *\n   * Necessary to store this so views can traverse through their nested views\n   * to remove listeners and call onDestroy callbacks.\n   */\n  [CHILD_HEAD]: LView | LContainer | null;\n\n  /**\n   * The last LView or LContainer beneath this LView in the hierarchy.\n   *\n   * The tail allows us to quickly add a new state to the end of the view list\n   * without having to propagate starting from the first child.\n   */\n  [CHILD_TAIL]: LView | LContainer | null;\n\n  /**\n   * View where this view's template was declared.\n   *\n   * The template for a dynamically created view may be declared in a different view than\n   * it is inserted. We already track the \"insertion view\" (view where the template was\n   * inserted) in LView[PARENT], but we also need access to the \"declaration view\"\n   * (view where the template was declared). Otherwise, we wouldn't be able to call the\n   * view's template function with the proper contexts. Context should be inherited from\n   * the declaration view tree, not the insertion view tree.\n   *\n   * Example (AppComponent template):\n   *\n   * <ng-template #foo></ng-template>       <-- declared here -->\n   * <some-comp [tpl]=\"foo\"></some-comp>    <-- inserted inside this component -->\n   *\n   * The <ng-template> above is declared in the AppComponent template, but it will be passed into\n   * SomeComp and inserted there. In this case, the declaration view would be the AppComponent,\n   * but the insertion view would be SomeComp. When we are removing views, we would want to\n   * traverse through the insertion view to clean up listeners. When we are calling the\n   * template function during change detection, we need the declaration view to get inherited\n   * context.\n   */\n  [DECLARATION_VIEW]: LView | null;\n\n  /**\n   * Points to the declaration component view, used to track transplanted `LView`s.\n   *\n   * See: `DECLARATION_VIEW` which points to the actual `LView` where it was declared, whereas\n   * `DECLARATION_COMPONENT_VIEW` points to the component which may not be same as\n   * `DECLARATION_VIEW`.\n   *\n   * Example:\n   * ```html\n   * <#VIEW #myComp>\n   *  <div *ngIf=\"true\">\n   *   <ng-template #myTmpl>...</ng-template>\n   *  </div>\n   * </#VIEW>\n   * ```\n   * In the above case `DECLARATION_VIEW` for `myTmpl` points to the `LView` of `ngIf` whereas\n   * `DECLARATION_COMPONENT_VIEW` points to `LView` of the `myComp` which owns the template.\n   *\n   * The reason for this is that all embedded views are always check-always whereas the component\n   * view can be check-always or on-push. When we have a transplanted view it is important to\n   * determine if we have transplanted a view from check-always declaration to on-push insertion\n   * point. In such a case the transplanted view needs to be added to the `LContainer` in the\n   * declared `LView` and CD during the declared view CD (in addition to the CD at the insertion\n   * point.) (Any transplanted views which are intra Component are of no interest because the CD\n   * strategy of declaration and insertion will always be the same, because it is the same\n   * component.)\n   *\n   * Queries already track moved views in `LView[DECLARATION_LCONTAINER]` and\n   * `LContainer[MOVED_VIEWS]`. However the queries also track `LView`s which moved within the same\n   * component `LView`. Transplanted views are a subset of moved views, and we use\n   * `DECLARATION_COMPONENT_VIEW` to differentiate them. As in this example.\n   *\n   * Example showing intra component `LView` movement.\n   * ```html\n   * <#VIEW #myComp>\n   *   <div *ngIf=\"condition; then thenBlock else elseBlock\"></div>\n   *   <ng-template #thenBlock>Content to render when condition is true.</ng-template>\n   *   <ng-template #elseBlock>Content to render when condition is false.</ng-template>\n   * </#VIEW>\n   * ```\n   * The `thenBlock` and `elseBlock` is moved but not transplanted.\n   *\n   * Example showing inter component `LView` movement (transplanted view).\n   * ```html\n   * <#VIEW #myComp>\n   *   <ng-template #myTmpl>...</ng-template>\n   *   <insertion-component [template]=\"myTmpl\"></insertion-component>\n   * </#VIEW>\n   * ```\n   * In the above example `myTmpl` is passed into a different component. If `insertion-component`\n   * instantiates `myTmpl` and `insertion-component` is on-push then the `LContainer` needs to be\n   * marked as containing transplanted views and those views need to be CD as part of the\n   * declaration CD.\n   *\n   *\n   * When change detection runs, it iterates over `[MOVED_VIEWS]` and CDs any child `LView`s where\n   * the `DECLARATION_COMPONENT_VIEW` of the current component and the child `LView` does not match\n   * (it has been transplanted across components.)\n   *\n   * Note: `[DECLARATION_COMPONENT_VIEW]` points to itself if the LView is a component view (the\n   *       simplest / most common case).\n   *\n   * see also:\n   *   - https://hackmd.io/@mhevery/rJUJsvv9H write up of the problem\n   *   - `LContainer[HAS_TRANSPLANTED_VIEWS]` which marks which `LContainer` has transplanted views.\n   *   - `LContainer[TRANSPLANT_HEAD]` and `LContainer[TRANSPLANT_TAIL]` storage for transplanted\n   *   - `LView[DECLARATION_LCONTAINER]` similar problem for queries\n   *   - `LContainer[MOVED_VIEWS]` similar problem for queries\n   */\n  [DECLARATION_COMPONENT_VIEW]: LView;\n\n  /**\n   * A declaration point of embedded views (ones instantiated based on the content of a\n   * <ng-template>), null for other types of views.\n   *\n   * We need to track all embedded views created from a given declaration point so we can prepare\n   * query matches in a proper order (query matches are ordered based on their declaration point and\n   * _not_ the insertion point).\n   */\n  [DECLARATION_LCONTAINER]: LContainer | null;\n\n  /**\n   * More flags for this view. See PreOrderHookFlags for more info.\n   */\n  [PREORDER_HOOK_FLAGS]: PreOrderHookFlags;\n\n  /** Unique ID of the view. Used for `__ngContext__` lookups in the `LView` registry. */\n  [ID]: number;\n\n  /**\n   * A container related to hydration annotation information that's associated with this LView.\n   */\n  [HYDRATION]: DehydratedView | null;\n\n  /**\n   * Optional injector assigned to embedded views that takes\n   * precedence over the element and module injectors.\n   */\n  readonly [EMBEDDED_VIEW_INJECTOR]: Injector | null;\n\n  /**\n   * Effect scheduling operations that need to run during this views's update pass.\n   */\n  [EFFECTS_TO_SCHEDULE]: Array<() => void> | null;\n\n  [EFFECTS]: Set<ViewEffectNode> | null;\n\n  /**\n   * A collection of callbacks functions that are executed when a given LView is destroyed. Those\n   * are user defined, LView-specific destroy callbacks that don't have any corresponding TView\n   * entries.\n   */\n  [ON_DESTROY_HOOKS]: Array<() => void> | null;\n\n  /**\n   * The `Consumer` for this `LView`'s template so that signal reads can be tracked.\n   *\n   * This is initially `null` and gets assigned a consumer after template execution\n   * if any signals were read.\n   */\n  [REACTIVE_TEMPLATE_CONSUMER]: ReactiveLViewConsumer | null;\n\n  // AfterRenderSequences that need to be scheduled\n  [AFTER_RENDER_SEQUENCES_TO_ADD]: AfterRenderSequence[] | null;\n}\n\n/**\n * Contextual data that is shared across multiple instances of `LView` in the same application.\n */\nexport interface LViewEnvironment {\n  /** Factory to be used for creating Renderer. */\n  rendererFactory: RendererFactory;\n\n  /** An optional custom sanitizer. */\n  sanitizer: Sanitizer | null;\n\n  /** Scheduler for change detection to notify when application state changes. */\n  changeDetectionScheduler: ChangeDetectionScheduler | null;\n\n  /**\n   * Whether `ng-reflect-*` attributes should be produced in dev mode\n   * (always disabled in prod mode).\n   */\n  ngReflect: boolean;\n}\n\n/** Flags associated with an LView (saved in LView[FLAGS]) */\nexport const enum LViewFlags {\n  /** The state of the init phase on the first 2 bits */\n  InitPhaseStateIncrementer = 0b00000000001,\n  InitPhaseStateMask = 0b00000000011,\n\n  /**\n   * Whether or not the view is in creationMode.\n   *\n   * This must be stored in the view rather than using `data` as a marker so that\n   * we can properly support embedded views. Otherwise, when exiting a child view\n   * back into the parent view, `data` will be defined and `creationMode` will be\n   * improperly reported as false.\n   */\n  CreationMode = 1 << 2,\n\n  /**\n   * Whether or not this LView instance is on its first processing pass.\n   *\n   * An LView instance is considered to be on its \"first pass\" until it\n   * has completed one creation mode run and one update mode run. At this\n   * time, the flag is turned off.\n   */\n  FirstLViewPass = 1 << 3,\n\n  /** Whether this view has default change detection strategy (checks always) or onPush */\n  CheckAlways = 1 << 4,\n\n  /** Whether there are any i18n blocks inside this LView. */\n  HasI18n = 1 << 5,\n\n  /** Whether or not this view is currently dirty (needing check) */\n  Dirty = 1 << 6,\n\n  /** Whether or not this view is currently attached to change detection tree. */\n  Attached = 1 << 7,\n\n  /** Whether or not this view is destroyed. */\n  Destroyed = 1 << 8,\n\n  /** Whether or not this view is the root view */\n  IsRoot = 1 << 9,\n\n  /**\n   * Whether this moved LView needs to be refreshed. Similar to the Dirty flag, but used for\n   * transplanted and signal views where the parent/ancestor views are not marked dirty as well.\n   * i.e. \"Refresh just this view\". Used in conjunction with the HAS_CHILD_VIEWS_TO_REFRESH\n   * flag.\n   */\n  RefreshView = 1 << 10,\n\n  /** Indicates that the view **or any of its ancestors** have an embedded view injector. */\n  HasEmbeddedViewInjector = 1 << 11,\n\n  /** Indicates that the view was created with `signals: true`. */\n  SignalView = 1 << 12,\n\n  /**\n   * Indicates that this LView has a view underneath it that needs to be refreshed during change\n   * detection. This flag indicates that even if this view is not dirty itself, we still need to\n   * traverse its children during change detection.\n   */\n  HasChildViewsToRefresh = 1 << 13,\n\n  /**\n   * This is the count of the bits the 1 was shifted above (base 10)\n   */\n  IndexWithinInitPhaseShift = 14,\n\n  /**\n   * Index of the current init phase on last 21 bits\n   */\n  IndexWithinInitPhaseIncrementer = 1 << IndexWithinInitPhaseShift,\n\n  // Subtracting 1 gives all 1s to the right of the initial shift\n  // So `(1 << 3) - 1` would give 3 1s: 1 << 3 = 0b01000, subtract 1 = 0b00111\n  IndexWithinInitPhaseReset = (1 << IndexWithinInitPhaseShift) - 1,\n}\n\n/**\n * Possible states of the init phase:\n * - 00: OnInit hooks to be run.\n * - 01: AfterContentInit hooks to be run\n * - 10: AfterViewInit hooks to be run\n * - 11: All init hooks have been run\n */\nexport const enum InitPhaseState {\n  OnInitHooksToBeRun = 0b00,\n  AfterContentInitHooksToBeRun = 0b01,\n  AfterViewInitHooksToBeRun = 0b10,\n  InitPhaseCompleted = 0b11,\n}\n\n/** More flags associated with an LView (saved in LView[PREORDER_HOOK_FLAGS]) */\nexport const enum PreOrderHookFlags {\n  /**\n     The index of the next pre-order hook to be called in the hooks array, on the first 16\n     bits\n   */\n  IndexOfTheNextPreOrderHookMaskMask = 0b01111111111111111,\n\n  /**\n   * The number of init hooks that have already been called, on the last 16 bits\n   */\n  NumberOfInitHooksCalledIncrementer = 0b010000000000000000,\n  NumberOfInitHooksCalledShift = 16,\n  NumberOfInitHooksCalledMask = 0b11111111111111110000000000000000,\n}\n\n/**\n * Stores a set of OpCodes to process `HostBindingsFunction` associated with a current view.\n *\n * In order to invoke `HostBindingsFunction` we need:\n * 1. 'elementIdx`: Index to the element associated with the `HostBindingsFunction`.\n * 2. 'directiveIdx`: Index to the directive associated with the `HostBindingsFunction`. (This will\n *    become the context for the `HostBindingsFunction` invocation.)\n * 3. `bindingRootIdx`: Location where the bindings for the `HostBindingsFunction` start. Internally\n *    `HostBindingsFunction` binding indexes start from `0` so we need to add `bindingRootIdx` to\n *    it.\n * 4. `HostBindingsFunction`: A host binding function to execute.\n *\n * The above information needs to be encoded into the `HostBindingOpCodes` in an efficient manner.\n *\n * 1. `elementIdx` is encoded into the `HostBindingOpCodes` as `~elementIdx` (so a negative number);\n * 2. `directiveIdx`\n * 3. `bindingRootIdx`\n * 4. `HostBindingsFunction` is passed in as is.\n *\n * The `HostBindingOpCodes` array contains:\n * - negative number to select the element index.\n * - followed by 1 or more of:\n *    - a number to select the directive index\n *    - a number to select the bindingRoot index\n *    - and a function to invoke.\n *\n * ## Example\n *\n * ```ts\n * const hostBindingOpCodes = [\n *   ~30,                               // Select element 30\n *   40, 45, MyDir.ɵdir.hostBindings    // Invoke host bindings on MyDir on element 30;\n *                                      // directiveIdx = 40; bindingRootIdx = 45;\n *   50, 55, OtherDir.ɵdir.hostBindings // Invoke host bindings on OtherDire on element 30\n *                                      // directiveIdx = 50; bindingRootIdx = 55;\n * ]\n * ```\n *\n * ## Pseudocode\n * ```ts\n * const hostBindingOpCodes = tView.hostBindingOpCodes;\n * if (hostBindingOpCodes === null) return;\n * for (let i = 0; i < hostBindingOpCodes.length; i++) {\n *   const opCode = hostBindingOpCodes[i] as number;\n *   if (opCode < 0) {\n *     // Negative numbers are element indexes.\n *     setSelectedIndex(~opCode);\n *   } else {\n *     // Positive numbers are NumberTuple which store bindingRootIndex and directiveIndex.\n *     const directiveIdx = opCode;\n *     const bindingRootIndx = hostBindingOpCodes[++i] as number;\n *     const hostBindingFn = hostBindingOpCodes[++i] as HostBindingsFunction<any>;\n *     setBindingRootForHostBindings(bindingRootIndx, directiveIdx);\n *     const context = lView[directiveIdx];\n *     hostBindingFn(RenderFlags.Update, context);\n *   }\n * }\n * ```\n *\n */\nexport interface HostBindingOpCodes extends Array<number | HostBindingsFunction<any>> {\n  __brand__: 'HostBindingOpCodes';\n  debug?: string[];\n}\n\n/**\n * Explicitly marks `TView` as a specific type in `ngDevMode`\n *\n * It is useful to know conceptually what time of `TView` we are dealing with when\n * debugging an application (even if the runtime does not need it.) For this reason\n * we store this information in the `ngDevMode` `TView` and than use it for\n * better debugging experience.\n */\nexport const enum TViewType {\n  /**\n   * Root `TView` is the used to bootstrap components into. It is used in conjunction with\n   * `LView` which takes an existing DOM node not owned by Angular and wraps it in `TView`/`LView`\n   * so that other components can be loaded into it.\n   */\n  Root = 0,\n\n  /**\n   * `TView` associated with a Component. This would be the `TView` directly associated with the\n   * component view (as opposed an `Embedded` `TView` which would be a child of `Component` `TView`)\n   */\n  Component = 1,\n\n  /**\n   * `TView` associated with a template. Such as `*ngIf`, `<ng-template>` etc... A `Component`\n   * can have zero or more `Embedded` `TView`s.\n   */\n  Embedded = 2,\n}\n\n/**\n * The static data for an LView (shared between all templates of a\n * given type).\n *\n * Stored on the `ComponentDef.tView`.\n */\nexport interface TView {\n  /**\n   * Type of `TView` (`Root`|`Component`|`Embedded`).\n   */\n  type: TViewType;\n\n  /**\n   * This is a blueprint used to generate LView instances for this TView. Copying this\n   * blueprint is faster than creating a new LView from scratch.\n   */\n  blueprint: LView;\n\n  /**\n   * The template function used to refresh the view of dynamically created views\n   * and components. Will be null for inline views.\n   */\n  template: ComponentTemplate<{}> | null;\n\n  /**\n   * A function containing query-related instructions.\n   */\n  viewQuery: ViewQueriesFunction<{}> | null;\n\n  /**\n   * A `TNode` representing the declaration location of this `TView` (not part of this TView).\n   */\n  declTNode: TNode | null;\n\n  // FIXME(misko): Why does `TView` not have `declarationTView` property?\n\n  /** Whether or not this template has been processed in creation mode. */\n  firstCreatePass: boolean;\n\n  /**\n   *  Whether or not this template has been processed in update mode (e.g. change detected)\n   *\n   * `firstUpdatePass` is used by styling to set up `TData` to contain metadata about the styling\n   * instructions. (Mainly to build up a linked list of styling priority order.)\n   *\n   * Typically this function gets cleared after first execution. If exception is thrown then this\n   * flag can remain turned un until there is first successful (no exception) pass. This means that\n   * individual styling instructions keep track of if they have already been added to the linked\n   * list to prevent double adding.\n   */\n  firstUpdatePass: boolean;\n\n  /** Static data equivalent of LView.data[]. Contains TNodes, PipeDefInternal or TI18n. */\n  data: TData;\n\n  /**\n   * The binding start index is the index at which the data array\n   * starts to store bindings only. Saving this value ensures that we\n   * will begin reading bindings at the correct point in the array when\n   * we are in update mode.\n   *\n   * -1 means that it has not been initialized.\n   */\n  bindingStartIndex: number;\n\n  /**\n   * The index where the \"expando\" section of `LView` begins. The expando\n   * section contains injectors, directive instances, and host binding values.\n   * Unlike the \"decls\" and \"vars\" sections of `LView`, the length of this\n   * section cannot be calculated at compile-time because directives are matched\n   * at runtime to preserve locality.\n   *\n   * We store this start index so we know where to start checking host bindings\n   * in `setHostBindings`.\n   */\n  expandoStartIndex: number;\n\n  /**\n   * Whether or not there are any static view queries tracked on this view.\n   *\n   * We store this so we know whether or not we should do a view query\n   * refresh after creation mode to collect static query results.\n   */\n  staticViewQueries: boolean;\n\n  /**\n   * Whether or not there are any static content queries tracked on this view.\n   *\n   * We store this so we know whether or not we should do a content query\n   * refresh after creation mode to collect static query results.\n   */\n  staticContentQueries: boolean;\n\n  /**\n   * A reference to the first child node located in the view.\n   */\n  firstChild: TNode | null;\n\n  /**\n   * Stores the OpCodes to be replayed during change-detection to process the `HostBindings`\n   *\n   * See `HostBindingOpCodes` for encoding details.\n   */\n  hostBindingOpCodes: HostBindingOpCodes | null;\n\n  /**\n   * Full registry of directives and components that may be found in this view.\n   *\n   * It's necessary to keep a copy of the full def list on the TView so it's possible\n   * to render template functions without a host component.\n   */\n  directiveRegistry: DirectiveDefList | null;\n\n  /**\n   * Full registry of pipes that may be found in this view.\n   *\n   * The property is either an array of `PipeDefs`s or a function which returns the array of\n   * `PipeDefs`s. The function is necessary to be able to support forward declarations.\n   *\n   * It's necessary to keep a copy of the full def list on the TView so it's possible\n   * to render template functions without a host component.\n   */\n  pipeRegistry: PipeDefList | null;\n\n  /**\n   * Array of ngOnInit, ngOnChanges and ngDoCheck hooks that should be executed for this view in\n   * creation mode.\n   *\n   * This array has a flat structure and contains TNode indices, directive indices (where an\n   * instance can be found in `LView`) and hook functions. TNode index is followed by the directive\n   * index and a hook function. If there are multiple hooks for a given TNode, the TNode index is\n   * not repeated and the next lifecycle hook information is stored right after the previous hook\n   * function. This is done so that at runtime the system can efficiently iterate over all of the\n   * functions to invoke without having to make any decisions/lookups.\n   */\n  preOrderHooks: HookData | null;\n\n  /**\n   * Array of ngOnChanges and ngDoCheck hooks that should be executed for this view in update mode.\n   *\n   * This array has the same structure as the `preOrderHooks` one.\n   */\n  preOrderCheckHooks: HookData | null;\n\n  /**\n   * Array of ngAfterContentInit and ngAfterContentChecked hooks that should be executed\n   * for this view in creation mode.\n   *\n   * Even indices: Directive index\n   * Odd indices: Hook function\n   */\n  contentHooks: HookData | null;\n\n  /**\n   * Array of ngAfterContentChecked hooks that should be executed for this view in update\n   * mode.\n   *\n   * Even indices: Directive index\n   * Odd indices: Hook function\n   */\n  contentCheckHooks: HookData | null;\n\n  /**\n   * Array of ngAfterViewInit and ngAfterViewChecked hooks that should be executed for\n   * this view in creation mode.\n   *\n   * Even indices: Directive index\n   * Odd indices: Hook function\n   */\n  viewHooks: HookData | null;\n\n  /**\n   * Array of ngAfterViewChecked hooks that should be executed for this view in\n   * update mode.\n   *\n   * Even indices: Directive index\n   * Odd indices: Hook function\n   */\n  viewCheckHooks: HookData | null;\n\n  /**\n   * Array of ngOnDestroy hooks that should be executed when this view is destroyed.\n   *\n   * Even indices: Directive index\n   * Odd indices: Hook function\n   */\n  destroyHooks: DestroyHookData | null;\n\n  /**\n   * When a view is destroyed, listeners need to be released and outputs need to be\n   * unsubscribed. This cleanup array stores both listener data (in chunks of 4)\n   * and output data (in chunks of 2) for a particular view. Combining the arrays\n   * saves on memory (70 bytes per array) and on a few bytes of code size (for two\n   * separate for loops).\n   *\n   * If it's a native DOM listener or output subscription being stored:\n   * 1st index is: event name  `name = tView.cleanup[i+0]`\n   * 2nd index is: index of native element or a function that retrieves global target (window,\n   *               document or body) reference based on the native element:\n   *    `typeof idxOrTargetGetter === 'function'`: global target getter function\n   *    `typeof idxOrTargetGetter === 'number'`: index of native element\n   *\n   * 3rd index is: index of listener function `listener = lView[CLEANUP][tView.cleanup[i+2]]`\n   * 4th index is: `useCaptureOrIndx = tView.cleanup[i+3]`\n   *    `typeof useCaptureOrIndx == 'boolean' : useCapture boolean\n   *    `typeof useCaptureOrIndx == 'number':\n   *         `useCaptureOrIndx >= 0` `removeListener = LView[CLEANUP][useCaptureOrIndx]`\n   *         `useCaptureOrIndx <  0` `subscription = LView[CLEANUP][-useCaptureOrIndx]`\n   *\n   * If it's an output subscription or query list destroy hook:\n   * 1st index is: output unsubscribe function / query list destroy function\n   * 2nd index is: index of function context in LView.cleanupInstances[]\n   *               `tView.cleanup[i+0].call(lView[CLEANUP][tView.cleanup[i+1]])`\n   */\n  cleanup: any[] | null;\n\n  /**\n   * A list of element indices for child components that will need to be\n   * refreshed when the current view has finished its check. These indices have\n   * already been adjusted for the HEADER_OFFSET.\n   *\n   */\n  components: number[] | null;\n\n  /**\n   * A collection of queries tracked in a given view.\n   */\n  queries: TQueries | null;\n\n  /**\n   * An array of indices pointing to directives with content queries alongside with the\n   * corresponding query index. Each entry in this array is a tuple of:\n   * - index of the first content query index declared by a given directive;\n   * - index of a directive.\n   *\n   * We are storing those indexes so we can refresh content queries as part of a view refresh\n   * process.\n   */\n  contentQueries: number[] | null;\n\n  /**\n   * Set of schemas that declare elements to be allowed inside the view.\n   */\n  schemas: SchemaMetadata[] | null;\n\n  /**\n   * Array of constants for the view. Includes attribute arrays, local definition arrays etc.\n   * Used for directive matching, attribute bindings, local definitions and more.\n   */\n  consts: TConstants | null;\n\n  /**\n   * Indicates that there was an error before we managed to complete the first create pass of the\n   * view. This means that the view is likely corrupted and we should try to recover it.\n   */\n  incompleteFirstPass: boolean;\n\n  /**\n   * Unique id of this TView for hydration purposes:\n   * - TViewType.Embedded: a unique id generated during serialization on the server\n   * - TViewType.Component: an id generated based on component properties\n   *                        (see `getComponentId` function for details)\n   */\n  ssrId: string | null;\n}\n\n/** Single hook callback function. */\nexport type HookFn = () => void;\n\n/**\n * Information necessary to call a hook. E.g. the callback that\n * needs to invoked and the index at which to find its context.\n */\nexport type HookEntry = number | HookFn;\n\n/**\n * Array of hooks that should be executed for a view and their directive indices.\n *\n * For each node of the view, the following data is stored:\n * 1) Node index (optional)\n * 2) A series of number/function pairs where:\n *  - even indices are directive indices\n *  - odd indices are hook functions\n *\n * Special cases:\n *  - a negative directive index flags an init hook (ngOnInit, ngAfterContentInit, ngAfterViewInit)\n */\nexport type HookData = HookEntry[];\n\n/**\n * Array of destroy hooks that should be executed for a view and their directive indices.\n *\n * The array is set up as a series of number/function or number/(number|function)[]:\n * - Even indices represent the context with which hooks should be called.\n * - Odd indices are the hook functions themselves. If a value at an odd index is an array,\n *   it represents the destroy hooks of a `multi` provider where:\n *     - Even indices represent the index of the provider for which we've registered a destroy hook,\n *       inside of the `multi` provider array.\n *     - Odd indices are the destroy hook functions.\n * For example:\n * LView: `[0, 1, 2, AService, 4, [BService, CService, DService]]`\n * destroyHooks: `[3, AService.ngOnDestroy, 5, [0, BService.ngOnDestroy, 2, DService.ngOnDestroy]]`\n *\n * In the example above `AService` is a type provider with an `ngOnDestroy`, whereas `BService`,\n * `CService` and `DService` are part of a `multi` provider where only `BService` and `DService`\n * have an `ngOnDestroy` hook.\n */\nexport type DestroyHookData = (HookEntry | HookData)[];\n\n/**\n * Static data that corresponds to the instance-specific data array on an LView.\n *\n * Each node's static data is stored in tData at the same index that it's stored\n * in the data array.  Any nodes that do not have static data store a null value in\n * tData to avoid a sparse array.\n *\n * Each pipe's definition is stored here at the same index as its pipe instance in\n * the data array.\n *\n * Each host property's name is stored here at the same index as its value in the\n * data array.\n *\n * Each property binding name is stored here at the same index as its value in\n * the data array. If the binding is an interpolation, the static string values\n * are stored parallel to the dynamic values. Example:\n *\n * id=\"prefix {{ v0 }} a {{ v1 }} b {{ v2 }} suffix\"\n *\n * LView       |   TView.data\n *------------------------\n *  v0 value   |   'a'\n *  v1 value   |   'b'\n *  v2 value   |   id � prefix � suffix\n *\n * Injector bloom filters are also stored here.\n */\nexport type TData = (\n  | TNode\n  | PipeDef<any>\n  | DirectiveDef<any>\n  | ComponentDef<any>\n  | number\n  | TStylingRange\n  | TStylingKey\n  | ProviderToken<any>\n  | TI18n\n  | I18nUpdateOpCodes\n  | TIcu\n  | null\n  | string\n  | TDeferBlockDetails\n)[];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DehydratedContainerView} from '../../hydration/interfaces';\n\nimport {TNode} from './node';\nimport {RComment, RElement} from './renderer_dom';\nimport {FLAGS, HOST, LView, NEXT, PARENT, T_HOST} from './view';\n\n/**\n * Special location which allows easy identification of type. If we have an array which was\n * retrieved from the `LView` and that array has `true` at `TYPE` location, we know it is\n * `LContainer`.\n */\nexport const TYPE = 1;\n\n/**\n * Below are constants for LContainer indices to help us look up LContainer members\n * without having to remember the specific indices.\n * Uglify will inline these when minifying so there shouldn't be a cost.\n */\n\n// FLAGS, PARENT, NEXT, and T_HOST are indices 2, 3, 4, and 5\n// As we already have these constants in LView, we don't need to re-create them.\n\nexport const DEHYDRATED_VIEWS = 6;\nexport const NATIVE = 7;\nexport const VIEW_REFS = 8;\nexport const MOVED_VIEWS = 9;\n\n/**\n * Size of LContainer's header. Represents the index after which all views in the\n * container will be inserted. We need to keep a record of current views so we know\n * which views are already in the DOM (and don't need to be re-added) and so we can\n * remove views from the DOM when they are no longer required.\n */\nexport const CONTAINER_HEADER_OFFSET = 10;\n\n/**\n * The state associated with a container.\n *\n * This is an array so that its structure is closer to LView. This helps\n * when traversing the view tree (which is a mix of containers and component\n * views), so we can jump to viewOrContainer[NEXT] in the same way regardless\n * of type.\n */\nexport interface LContainer extends Array<any> {\n  /**\n   * The host element of this LContainer.\n   *\n   * The host could be an LView if this container is on a component node.\n   * In that case, the component LView is its HOST.\n   */\n  readonly [HOST]: RElement | RComment | LView;\n\n  /**\n   * This is a type field which allows us to differentiate `LContainer` from `StylingContext` in an\n   * efficient way. The value is always set to `true`\n   */\n  [TYPE]: true;\n\n  /** Flags for this container. See LContainerFlags for more info. */\n  [FLAGS]: LContainerFlags;\n\n  /**\n   * Access to the parent view is necessary so we can propagate back\n   * up from inside a container to parent[NEXT].\n   */\n  [PARENT]: LView;\n\n  /**\n   * This allows us to jump from a container to a sibling container or component\n   * view with the same parent, so we can remove listeners efficiently.\n   */\n  [NEXT]: LView | LContainer | null;\n\n  /**\n   * A collection of views created based on the underlying `<ng-template>` element but inserted into\n   * a different `LContainer`. We need to track views created from a given declaration point since\n   * queries collect matches from the embedded view declaration point and _not_ the insertion point.\n   */\n  [MOVED_VIEWS]: LView[] | null;\n\n  /**\n   * Pointer to the `TNode` which represents the host of the container.\n   */\n  [T_HOST]: TNode;\n\n  /** The comment element that serves as an anchor for this LContainer. */\n  [NATIVE]: RComment;\n\n  /**\n   * Array of `ViewRef`s used by any `ViewContainerRef`s that point to this container.\n   *\n   * This is lazily initialized by `ViewContainerRef` when the first view is inserted.\n   *\n   * NOTE: This is stored as `any[]` because render3 should really not be aware of `ViewRef` and\n   * doing so creates circular dependency.\n   */\n  [VIEW_REFS]: unknown[] | null;\n\n  /**\n   * Array of dehydrated views within this container.\n   *\n   * This information is used during the hydration process on the client.\n   * The hydration logic tries to find a matching dehydrated view, \"claim\" it\n   * and use this information to do further matching. After that, this \"claimed\"\n   * view is removed from the list. The remaining \"unclaimed\" views are\n   * \"garbage-collected\" later on, i.e. removed from the DOM once the hydration\n   * logic finishes.\n   */\n  [DEHYDRATED_VIEWS]: DehydratedContainerView[] | null;\n}\n\n/** Flags associated with an LContainer (saved in LContainer[FLAGS]) */\nexport const enum LContainerFlags {\n  None = 0,\n  /**\n   * Flag to signify that this `LContainer` may have transplanted views which need to be change\n   * detected. (see: `LView[DECLARATION_COMPONENT_VIEW])`.\n   *\n   * This flag, once set, is never unset for the `LContainer`.\n   */\n  HasTransplantedViews = 1 << 1,\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {LContainer, TYPE} from './container';\nimport {ComponentDef, DirectiveDef} from './definition';\nimport {TNode, TNodeFlags, TNodeType} from './node';\nimport {RNode} from './renderer_dom';\nimport {FLAGS, LView, LViewFlags} from './view';\n\n/**\n * True if `value` is `LView`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nexport function isLView(value: RNode | LView | LContainer | {} | null): value is LView {\n  return Array.isArray(value) && typeof value[TYPE] === 'object';\n}\n\n/**\n * True if `value` is `LContainer`.\n * @param value wrapped value of `RNode`, `<PERSON>View`, `LContainer`\n */\nexport function isLContainer(value: RNode | LView | LContainer | {} | null): value is LContainer {\n  return Array.isArray(value) && value[TYPE] === true;\n}\n\nexport function isContentQueryHost(tNode: TNode): boolean {\n  return (tNode.flags & TNodeFlags.hasContentQuery) !== 0;\n}\n\nexport function isComponentHost(tNode: TNode): boolean {\n  return tNode.componentOffset > -1;\n}\n\nexport function isDirectiveHost(tNode: TNode): boolean {\n  return (tNode.flags & TNodeFlags.isDirectiveHost) === TNodeFlags.isDirectiveHost;\n}\n\nexport function isComponentDef<T>(def: DirectiveDef<T>): def is ComponentDef<T> {\n  return !!(def as ComponentDef<T>).template;\n}\n\nexport function isRootView(target: LView): boolean {\n  // Determines whether a given LView is marked as a root view.\n  return (target[FLAGS] & LViewFlags.IsRoot) !== 0;\n}\n\nexport function isProjectionTNode(tNode: TNode): boolean {\n  return (tNode.type & TNodeType.Projection) === TNodeType.Projection;\n}\n\nexport function hasI18n(lView: LView): boolean {\n  return (lView[FLAGS] & LViewFlags.HasI18n) === LViewFlags.HasI18n;\n}\n\nexport function isDestroyed(lView: LView): boolean {\n  // Determines whether a given LView is marked as destroyed.\n  return (lView[FLAGS] & LViewFlags.Destroyed) === LViewFlags.Destroyed;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {RuntimeError, RuntimeErrorCode} from '../errors';\nimport {assertDefined, assertEqual, assertNumber, throwError} from '../util/assert';\n\nimport {getComponentDef, getNgModuleDef} from './def_getters';\nimport {LContainer} from './interfaces/container';\nimport {DirectiveDef} from './interfaces/definition';\nimport {TIcu} from './interfaces/i18n';\nimport {NodeInjectorOffset} from './interfaces/injector';\nimport {TNode} from './interfaces/node';\nimport {isLContainer, isLView} from './interfaces/type_checks';\nimport {\n  DECLARATION_COMPONENT_VIEW,\n  FLAGS,\n  HEADER_OFFSET,\n  LView,\n  LViewFlags,\n  T_HOST,\n  TVIEW,\n  TView,\n} from './interfaces/view';\n\n// [Assert functions do not constraint type when they are guarded by a truthy\n// expression.](https://github.com/microsoft/TypeScript/issues/37295)\n\nexport function assertTNodeForLView(tNode: TNode, lView: LView) {\n  assertTNodeForTView(tNode, lView[TVIEW]);\n}\n\nexport function assertTNodeForTView(tNode: TNode, tView: TView) {\n  assertTNode(tNode);\n  const tData = tView.data;\n  for (let i = HEADER_OFFSET; i < tData.length; i++) {\n    if (tData[i] === tNode) {\n      return;\n    }\n  }\n  throwError('This TNode does not belong to this TView.');\n}\n\nexport function assertTNode(tNode: TNode) {\n  assertDefined(tNode, 'TNode must be defined');\n  if (!(tNode && typeof tNode === 'object' && tNode.hasOwnProperty('directiveStylingLast'))) {\n    throwError('Not of type TNode, got: ' + tNode);\n  }\n}\n\nexport function assertTIcu(tIcu: TIcu) {\n  assertDefined(tIcu, 'Expected TIcu to be defined');\n  if (!(typeof tIcu.currentCaseLViewIndex === 'number')) {\n    throwError('Object is not of TIcu type.');\n  }\n}\n\nexport function assertComponentType(\n  actual: any,\n  msg: string = \"Type passed in is not ComponentType, it does not have 'ɵcmp' property.\",\n) {\n  if (!getComponentDef(actual)) {\n    throwError(msg);\n  }\n}\n\nexport function assertNgModuleType(\n  actual: any,\n  msg: string = \"Type passed in is not NgModuleType, it does not have 'ɵmod' property.\",\n) {\n  if (!getNgModuleDef(actual)) {\n    throwError(msg);\n  }\n}\n\nexport function assertCurrentTNodeIsParent(isParent: boolean) {\n  assertEqual(isParent, true, 'currentTNode should be a parent');\n}\n\nexport function assertHasParent(tNode: TNode | null) {\n  assertDefined(tNode, 'currentTNode should exist!');\n  assertDefined(tNode!.parent, 'currentTNode should have a parent');\n}\n\nexport function assertLContainer(value: any): asserts value is LContainer {\n  assertDefined(value, 'LContainer must be defined');\n  assertEqual(isLContainer(value), true, 'Expecting LContainer');\n}\n\nexport function assertLViewOrUndefined(value: any): asserts value is LView | null | undefined {\n  value && assertEqual(isLView(value), true, 'Expecting LView or undefined or null');\n}\n\nexport function assertLView(value: any): asserts value is LView {\n  assertDefined(value, 'LView must be defined');\n  assertEqual(isLView(value), true, 'Expecting LView');\n}\n\nexport function assertFirstCreatePass(tView: TView, errMessage?: string) {\n  assertEqual(\n    tView.firstCreatePass,\n    true,\n    errMessage || 'Should only be called in first create pass.',\n  );\n}\n\nexport function assertFirstUpdatePass(tView: TView, errMessage?: string) {\n  assertEqual(\n    tView.firstUpdatePass,\n    true,\n    errMessage || 'Should only be called in first update pass.',\n  );\n}\n\n/**\n * This is a basic sanity check that an object is probably a directive def. DirectiveDef is\n * an interface, so we can't do a direct instanceof check.\n */\nexport function assertDirectiveDef<T>(obj: any): asserts obj is DirectiveDef<T> {\n  if (obj.type === undefined || obj.selectors == undefined || obj.inputs === undefined) {\n    throwError(\n      `Expected a DirectiveDef/ComponentDef and this object does not seem to have the expected shape.`,\n    );\n  }\n}\n\nexport function assertIndexInDeclRange(tView: TView, index: number) {\n  assertBetween(HEADER_OFFSET, tView.bindingStartIndex, index);\n}\n\nexport function assertIndexInExpandoRange(lView: LView, index: number) {\n  const tView = lView[1];\n  assertBetween(tView.expandoStartIndex, lView.length, index);\n}\n\nexport function assertBetween(lower: number, upper: number, index: number) {\n  if (!(lower <= index && index < upper)) {\n    throwError(`Index out of range (expecting ${lower} <= ${index} < ${upper})`);\n  }\n}\n\nexport function assertProjectionSlots(lView: LView, errMessage?: string) {\n  assertDefined(lView[DECLARATION_COMPONENT_VIEW], 'Component views should exist.');\n  assertDefined(\n    lView[DECLARATION_COMPONENT_VIEW][T_HOST]!.projection,\n    errMessage ||\n      'Components with projection nodes (<ng-content>) must have projection slots defined.',\n  );\n}\n\nexport function assertParentView(lView: LView | null, errMessage?: string) {\n  assertDefined(\n    lView,\n    errMessage || \"Component views should always have a parent view (component's host view)\",\n  );\n}\n\nexport function assertNoDuplicateDirectives(directives: DirectiveDef<unknown>[]): void {\n  // The array needs at least two elements in order to have duplicates.\n  if (directives.length < 2) {\n    return;\n  }\n\n  const seenDirectives = new Set<DirectiveDef<unknown>>();\n\n  for (const current of directives) {\n    if (seenDirectives.has(current)) {\n      throw new RuntimeError(\n        RuntimeErrorCode.DUPLICATE_DIRECTIVE,\n        `Directive ${current.type.name} matches multiple times on the same element. ` +\n          `Directives can only match an element once.`,\n      );\n    }\n    seenDirectives.add(current);\n  }\n}\n\n/**\n * This is a basic sanity check that the `injectorIndex` seems to point to what looks like a\n * NodeInjector data structure.\n *\n * @param lView `LView` which should be checked.\n * @param injectorIndex index into the `LView` where the `NodeInjector` is expected.\n */\nexport function assertNodeInjector(lView: LView, injectorIndex: number) {\n  assertIndexInExpandoRange(lView, injectorIndex);\n  assertIndexInExpandoRange(lView, injectorIndex + NodeInjectorOffset.PARENT);\n  assertNumber(lView[injectorIndex + 0], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 1], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 2], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 3], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 4], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 5], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 6], 'injectorIndex should point to a bloom filter');\n  assertNumber(lView[injectorIndex + 7], 'injectorIndex should point to a bloom filter');\n  assertNumber(\n    lView[injectorIndex + NodeInjectorOffset.PARENT],\n    'injectorIndex should point to parent injector',\n  );\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport const SVG_NAMESPACE = 'svg';\nexport const MATH_ML_NAMESPACE = 'math';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NotificationSource} from '../../change_detection/scheduling/zoneless_scheduling';\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\nimport {\n  assertDefined,\n  assertGreater<PERSON>han,\n  assertGreater<PERSON>hanOrEqual,\n  assertIndexInRange,\n  assertLessThan,\n} from '../../util/assert';\nimport {assertLView, assertTNode, assertTNodeForLView} from '../assert';\nimport {LContainer, TYPE} from '../interfaces/container';\nimport {TConstants, TNode} from '../interfaces/node';\nimport {RNode} from '../interfaces/renderer_dom';\nimport {isDestroyed, isLContainer, isLView} from '../interfaces/type_checks';\nimport {\n  CLEANUP,\n  DECLARATION_VIEW,\n  ENVIRONMENT,\n  FLAGS,\n  HEADER_OFFSET,\n  HOST,\n  LView,\n  LViewFlags,\n  ON_DESTROY_HOOKS,\n  PARENT,\n  PREORDER_HOOK_FLAGS,\n  PreOrderHookFlags,\n  REACTIVE_TEMPLATE_CONSUMER,\n  TData,\n  TView,\n} from '../interfaces/view';\n\n/**\n * For efficiency reasons we often put several different data types (`RNode`, `LView`, `LContainer`)\n * in same location in `LView`. This is because we don't want to pre-allocate space for it\n * because the storage is sparse. This file contains utilities for dealing with such data types.\n *\n * How do we know what is stored at a given location in `LView`.\n * - `Array.isArray(value) === false` => `RNode` (The normal storage value)\n * - `Array.isArray(value) === true` => then the `value[0]` represents the wrapped value.\n *   - `typeof value[TYPE] === 'object'` => `LView`\n *      - This happens when we have a component at a given location\n *   - `typeof value[TYPE] === true` => `LContainer`\n *      - This happens when we have `LContainer` binding at a given location.\n *\n *\n * NOTE: it is assumed that `Array.isArray` and `typeof` operations are very efficient.\n */\n\n/**\n * Returns `RNode`.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nexport function unwrapRNode(value: RNode | LView | LContainer): RNode {\n  while (Array.isArray(value)) {\n    value = value[HOST] as any;\n  }\n  return value as RNode;\n}\n\n/**\n * Returns `LView` or `null` if not found.\n * @param value wrapped value of `RNode`, `LView`, `LContainer`\n */\nexport function unwrapLView(value: RNode | LView | LContainer): LView | null {\n  while (Array.isArray(value)) {\n    // This check is same as `isLView()` but we don't call at as we don't want to call\n    // `Array.isArray()` twice and give JITer more work for inlining.\n    if (typeof value[TYPE] === 'object') return value as LView;\n    value = value[HOST] as any;\n  }\n  return null;\n}\n\n/**\n * Retrieves an element value from the provided `viewData`, by unwrapping\n * from any containers, component views, or style contexts.\n */\nexport function getNativeByIndex(index: number, lView: LView): RNode {\n  ngDevMode && assertIndexInRange(lView, index);\n  ngDevMode && assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Expected to be past HEADER_OFFSET');\n  return unwrapRNode(lView[index]);\n}\n\n/**\n * Retrieve an `RNode` for a given `TNode` and `LView`.\n *\n * This function guarantees in dev mode to retrieve a non-null `RNode`.\n *\n * @param tNode\n * @param lView\n */\nexport function getNativeByTNode(tNode: TNode, lView: LView): RNode {\n  ngDevMode && assertTNodeForLView(tNode, lView);\n  ngDevMode && assertIndexInRange(lView, tNode.index);\n  const node: RNode = unwrapRNode(lView[tNode.index]);\n  return node;\n}\n\n/**\n * Retrieve an `RNode` or `null` for a given `TNode` and `LView`.\n *\n * Some `TNode`s don't have associated `RNode`s. For example `Projection`\n *\n * @param tNode\n * @param lView\n */\nexport function getNativeByTNodeOrNull(tNode: TNode | null, lView: LView): RNode | null {\n  const index = tNode === null ? -1 : tNode.index;\n  if (index !== -1) {\n    ngDevMode && assertTNodeForLView(tNode!, lView);\n    const node: RNode | null = unwrapRNode(lView[index]);\n    return node;\n  }\n  return null;\n}\n\n// fixme(misko): The return Type should be `TNode|null`\nexport function getTNode(tView: TView, index: number): TNode {\n  ngDevMode && assertGreaterThan(index, -1, 'wrong index for TNode');\n  ngDevMode && assertLessThan(index, tView.data.length, 'wrong index for TNode');\n  const tNode = tView.data[index] as TNode;\n  ngDevMode && tNode !== null && assertTNode(tNode);\n  return tNode;\n}\n\n/** Retrieves a value from any `LView` or `TData`. */\nexport function load<T>(view: LView | TData, index: number): T {\n  ngDevMode && assertIndexInRange(view, index);\n  return view[index];\n}\n\n/** Store a value in the `data` at a given `index`. */\nexport function store<T>(tView: TView, lView: LView, index: number, value: T): void {\n  // We don't store any static data for local variables, so the first time\n  // we see the template, we should store as null to avoid a sparse array\n  if (index >= tView.data.length) {\n    tView.data[index] = null;\n    tView.blueprint[index] = null;\n  }\n  lView[index] = value;\n}\n\nexport function getComponentLViewByIndex(nodeIndex: number, hostView: LView): LView {\n  // Could be an LView or an LContainer. If LContainer, unwrap to find LView.\n  ngDevMode && assertIndexInRange(hostView, nodeIndex);\n  const slotValue = hostView[nodeIndex];\n  const lView = isLView(slotValue) ? slotValue : slotValue[HOST];\n  return lView;\n}\n\n/** Checks whether a given view is in creation mode */\nexport function isCreationMode(view: LView): boolean {\n  return (view[FLAGS] & LViewFlags.CreationMode) === LViewFlags.CreationMode;\n}\n\n/**\n * Returns a boolean for whether the view is attached to the change detection tree.\n *\n * Note: This determines whether a view should be checked, not whether it's inserted\n * into a container. For that, you'll want `viewAttachedToContainer` below.\n */\nexport function viewAttachedToChangeDetector(view: LView): boolean {\n  return (view[FLAGS] & LViewFlags.Attached) === LViewFlags.Attached;\n}\n\n/** Returns a boolean for whether the view is attached to a container. */\nexport function viewAttachedToContainer(view: LView): boolean {\n  return isLContainer(view[PARENT]);\n}\n\n/** Returns a constant from `TConstants` instance. */\nexport function getConstant<T>(consts: TConstants | null, index: null | undefined): null;\nexport function getConstant<T>(consts: TConstants, index: number): T | null;\nexport function getConstant<T>(\n  consts: TConstants | null,\n  index: number | null | undefined,\n): T | null;\nexport function getConstant<T>(\n  consts: TConstants | null,\n  index: number | null | undefined,\n): T | null {\n  if (index === null || index === undefined) return null;\n  ngDevMode && assertIndexInRange(consts!, index);\n  return consts![index] as unknown as T;\n}\n\n/**\n * Resets the pre-order hook flags of the view.\n * @param lView the LView on which the flags are reset\n */\nexport function resetPreOrderHookFlags(lView: LView) {\n  lView[PREORDER_HOOK_FLAGS] = 0 as PreOrderHookFlags;\n}\n\n/**\n * Adds the `RefreshView` flag from the lView and updates HAS_CHILD_VIEWS_TO_REFRESH flag of\n * parents.\n */\nexport function markViewForRefresh(lView: LView) {\n  if (lView[FLAGS] & LViewFlags.RefreshView) {\n    return;\n  }\n  lView[FLAGS] |= LViewFlags.RefreshView;\n  if (viewAttachedToChangeDetector(lView)) {\n    markAncestorsForTraversal(lView);\n  }\n}\n\n/**\n * Walks up the LView hierarchy.\n * @param nestingLevel Number of times to walk up in hierarchy.\n * @param currentView View from which to start the lookup.\n */\nexport function walkUpViews(nestingLevel: number, currentView: LView): LView {\n  while (nestingLevel > 0) {\n    ngDevMode &&\n      assertDefined(\n        currentView[DECLARATION_VIEW],\n        'Declaration view should be defined if nesting level is greater than 0.',\n      );\n    currentView = currentView[DECLARATION_VIEW]!;\n    nestingLevel--;\n  }\n  return currentView;\n}\n\nexport function requiresRefreshOrTraversal(lView: LView) {\n  return !!(\n    lView[FLAGS] & (LViewFlags.RefreshView | LViewFlags.HasChildViewsToRefresh) ||\n    lView[REACTIVE_TEMPLATE_CONSUMER]?.dirty\n  );\n}\n\n/**\n * Updates the `HasChildViewsToRefresh` flag on the parents of the `LView` as well as the\n * parents above.\n */\nexport function updateAncestorTraversalFlagsOnAttach(lView: LView) {\n  lView[ENVIRONMENT].changeDetectionScheduler?.notify(NotificationSource.ViewAttached);\n  if (lView[FLAGS] & LViewFlags.Dirty) {\n    lView[FLAGS] |= LViewFlags.RefreshView;\n  }\n  if (requiresRefreshOrTraversal(lView)) {\n    markAncestorsForTraversal(lView);\n  }\n}\n\n/**\n * Ensures views above the given `lView` are traversed during change detection even when they are\n * not dirty.\n *\n * This is done by setting the `HAS_CHILD_VIEWS_TO_REFRESH` flag up to the root, stopping when the\n * flag is already `true` or the `lView` is detached.\n */\nexport function markAncestorsForTraversal(lView: LView) {\n  lView[ENVIRONMENT].changeDetectionScheduler?.notify(NotificationSource.MarkAncestorsForTraversal);\n  let parent = getLViewParent(lView);\n  while (parent !== null) {\n    // We stop adding markers to the ancestors once we reach one that already has the marker. This\n    // is to avoid needlessly traversing all the way to the root when the marker already exists.\n    if (parent[FLAGS] & LViewFlags.HasChildViewsToRefresh) {\n      break;\n    }\n\n    parent[FLAGS] |= LViewFlags.HasChildViewsToRefresh;\n    if (!viewAttachedToChangeDetector(parent)) {\n      break;\n    }\n    parent = getLViewParent(parent);\n  }\n}\n\n/**\n * Stores a LView-specific destroy callback.\n */\nexport function storeLViewOnDestroy(lView: LView, onDestroyCallback: () => void) {\n  if (isDestroyed(lView)) {\n    throw new RuntimeError(\n      RuntimeErrorCode.VIEW_ALREADY_DESTROYED,\n      ngDevMode && 'View has already been destroyed.',\n    );\n  }\n  if (lView[ON_DESTROY_HOOKS] === null) {\n    lView[ON_DESTROY_HOOKS] = [];\n  }\n  lView[ON_DESTROY_HOOKS].push(onDestroyCallback);\n}\n\n/**\n * Removes previously registered LView-specific destroy callback.\n */\nexport function removeLViewOnDestroy(lView: LView, onDestroyCallback: () => void) {\n  if (lView[ON_DESTROY_HOOKS] === null) return;\n\n  const destroyCBIdx = lView[ON_DESTROY_HOOKS].indexOf(onDestroyCallback);\n  if (destroyCBIdx !== -1) {\n    lView[ON_DESTROY_HOOKS].splice(destroyCBIdx, 1);\n  }\n}\n\n/**\n * Gets the parent LView of the passed LView, if the PARENT is an LContainer, will get the parent of\n * that LContainer, which is an LView\n * @param lView the lView whose parent to get\n */\nexport function getLViewParent(lView: LView): LView | null {\n  ngDevMode && assertLView(lView);\n  const parent = lView[PARENT];\n  return isLContainer(parent) ? parent[PARENT] : parent;\n}\n\nexport function getOrCreateLViewCleanup(view: LView): any[] {\n  // top level variables should not be exported for performance reasons (PERF_NOTES.md)\n  return (view[CLEANUP] ??= []);\n}\n\nexport function getOrCreateTViewCleanup(tView: TView): any[] {\n  return (tView.cleanup ??= []);\n}\n\n/**\n * Saves context for this cleanup function in LView.cleanupInstances.\n *\n * On the first template pass, saves in TView:\n * - Cleanup function\n * - Index of context we just saved in LView.cleanupInstances\n */\nexport function storeCleanupWithContext(\n  tView: TView,\n  lView: LView,\n  context: any,\n  cleanupFn: Function,\n): void {\n  const lCleanup = getOrCreateLViewCleanup(lView);\n\n  // Historically the `storeCleanupWithContext` was used to register both framework-level and\n  // user-defined cleanup callbacks, but over time those two types of cleanups were separated.\n  // This dev mode checks assures that user-level cleanup callbacks are _not_ stored in data\n  // structures reserved for framework-specific hooks.\n  ngDevMode &&\n    assertDefined(\n      context,\n      'Cleanup context is mandatory when registering framework-level destroy hooks',\n    );\n  lCleanup.push(context);\n\n  if (tView.firstCreatePass) {\n    getOrCreateTViewCleanup(tView).push(cleanupFn, lCleanup.length - 1);\n  } else {\n    // Make sure that no new framework-level cleanup functions are registered after the first\n    // template pass is done (and TView data structures are meant to fully constructed).\n    if (ngDevMode) {\n      Object.freeze(getOrCreateTViewCleanup(tView));\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InternalInjectFlags} from '../di/interface/injector';\nimport {\n  assertDefined,\n  assertEqual,\n  assertGreater<PERSON>han<PERSON>r<PERSON>qual,\n  assertLess<PERSON>han,\n  assertNotEqual,\n  throwError,\n} from '../util/assert';\n\nimport {assertLViewOrUndefined, assertTNodeForLView, assertTNodeForTView} from './assert';\nimport {DirectiveDef} from './interfaces/definition';\nimport {TNode, TNodeType} from './interfaces/node';\nimport {\n  CONTEXT,\n  DECLARATION_VIEW,\n  HEADER_OFFSET,\n  LView,\n  OpaqueViewState,\n  T_HOST,\n  TData,\n  TVIEW,\n  TView,\n  TViewType,\n} from './interfaces/view';\nimport {MATH_ML_NAMESPACE, SVG_NAMESPACE} from './namespaces';\nimport {getTNode, walkUpViews} from './util/view_utils';\n\n/**\n *\n */\ninterface LFrame {\n  /**\n   * Parent LFrame.\n   *\n   * This is needed when `leaveView` is called to restore the previous state.\n   */\n  parent: LFrame;\n\n  /**\n   * Child LFrame.\n   *\n   * This is used to cache existing LFrames to relieve the memory pressure.\n   */\n  child: LFrame | null;\n\n  /**\n   * State of the current view being processed.\n   *\n   * An array of nodes (text, element, container, etc), pipes, their bindings, and\n   * any local variables that need to be stored between invocations.\n   */\n  lView: LView;\n\n  /**\n   * Current `TView` associated with the `LFrame.lView`.\n   *\n   * One can get `TView` from `lFrame[TVIEW]` however because it is so common it makes sense to\n   * store it in `LFrame` for perf reasons.\n   */\n  tView: TView;\n\n  /**\n   * Used to set the parent property when nodes are created and track query results.\n   *\n   * This is used in conjunction with `isParent`.\n   */\n  currentTNode: TNode | null;\n\n  /**\n   * If `isParent` is:\n   *  - `true`: then `currentTNode` points to a parent node.\n   *  - `false`: then `currentTNode` points to previous node (sibling).\n   */\n  isParent: boolean;\n\n  /**\n   * Index of currently selected element in LView.\n   *\n   * Used by binding instructions. Updated as part of advance instruction.\n   */\n  selectedIndex: number;\n\n  /**\n   * Current pointer to the binding index.\n   */\n  bindingIndex: number;\n\n  /**\n   * The last viewData retrieved by nextContext().\n   * Allows building nextContext() and reference() calls.\n   *\n   * e.g. const inner = x().$implicit; const outer = x().$implicit;\n   */\n  contextLView: LView | null;\n\n  /**\n   * Store the element depth count. This is used to identify the root elements of the template\n   * so that we can then attach patch data `LView` to only those elements. We know that those\n   * are the only places where the patch data could change, this way we will save on number\n   * of places where tha patching occurs.\n   */\n  elementDepthCount: number;\n\n  /**\n   * Current namespace to be used when creating elements\n   */\n  currentNamespace: string | null;\n\n  /**\n   * The root index from which pure function instructions should calculate their binding\n   * indices. In component views, this is TView.bindingStartIndex. In a host binding\n   * context, this is the TView.expandoStartIndex + any dirs/hostVars before the given dir.\n   */\n  bindingRootIndex: number;\n\n  /**\n   * Current index of a View or Content Query which needs to be processed next.\n   * We iterate over the list of Queries and increment current query index at every step.\n   */\n  currentQueryIndex: number;\n\n  /**\n   * When host binding is executing this points to the directive index.\n   * `TView.data[currentDirectiveIndex]` is `DirectiveDef`\n   * `LView[currentDirectiveIndex]` is directive instance.\n   */\n  currentDirectiveIndex: number;\n\n  /**\n   * Are we currently in i18n block as denoted by `ɵɵelementStart` and `ɵɵelementEnd`.\n   *\n   * This information is needed because while we are in i18n block all elements must be pre-declared\n   * in the translation. (i.e. `Hello �#2�World�/#2�!` pre-declares element at `�#2�` location.)\n   * This allocates `TNodeType.Placeholder` element at location `2`. If translator removes `�#2�`\n   * from translation than the runtime must also ensure tha element at `2` does not get inserted\n   * into the DOM. The translation does not carry information about deleted elements. Therefor the\n   * only way to know that an element is deleted is that it was not pre-declared in the translation.\n   *\n   * This flag works by ensuring that elements which are created without pre-declaration\n   * (`TNodeType.Placeholder`) are not inserted into the DOM render tree. (It does mean that the\n   * element still gets instantiated along with all of its behavior [directives])\n   */\n  inI18n: boolean;\n}\n\n/**\n * All implicit instruction state is stored here.\n *\n * It is useful to have a single object where all of the state is stored as a mental model\n * (rather it being spread across many different variables.)\n *\n * PERF NOTE: Turns out that writing to a true global variable is slower than\n * having an intermediate object with properties.\n */\ninterface InstructionState {\n  /**\n   * Current `LFrame`\n   *\n   * `null` if we have not called `enterView`\n   */\n  lFrame: LFrame;\n\n  /**\n   * Stores whether directives should be matched to elements.\n   *\n   * When template contains `ngNonBindable` then we need to prevent the runtime from matching\n   * directives on children of that element.\n   *\n   * Example:\n   * ```html\n   * <my-comp my-directive>\n   *   Should match component / directive.\n   * </my-comp>\n   * <div ngNonBindable>\n   *   <my-comp my-directive>\n   *     Should not match component / directive because we are in ngNonBindable.\n   *   </my-comp>\n   * </div>\n   * ```\n   */\n  bindingsEnabled: boolean;\n\n  /**\n   * Stores the root TNode that has the 'ngSkipHydration' attribute on it for later reference.\n   *\n   * Example:\n   * ```html\n   * <my-comp ngSkipHydration>\n   *   Should reference this root node\n   * </my-comp>\n   * ```\n   */\n  skipHydrationRootTNode: TNode | null;\n}\n\nconst instructionState: InstructionState = {\n  lFrame: createLFrame(null),\n  bindingsEnabled: true,\n  skipHydrationRootTNode: null,\n};\n\nexport enum CheckNoChangesMode {\n  Off,\n  Exhaustive,\n  OnlyDirtyViews,\n}\n\n/**\n * In this mode, any changes in bindings will throw an ExpressionChangedAfterChecked error.\n *\n * Necessary to support ChangeDetectorRef.checkNoChanges().\n *\n * The `checkNoChanges` function is invoked only in ngDevMode=true and verifies that no unintended\n * changes exist in the change detector or its children.\n */\nlet _checkNoChangesMode: CheckNoChangesMode = 0; /* CheckNoChangesMode.Off */\n\n/**\n * Flag used to indicate that we are in the middle running change detection on a view\n *\n * @see detectChangesInViewWhileDirty\n */\nlet _isRefreshingViews = false;\n\n/**\n * Returns true if the instruction state stack is empty.\n *\n * Intended to be called from tests only (tree shaken otherwise).\n */\nexport function specOnlyIsInstructionStateEmpty(): boolean {\n  return instructionState.lFrame.parent === null;\n}\n\nexport function getElementDepthCount() {\n  return instructionState.lFrame.elementDepthCount;\n}\n\nexport function increaseElementDepthCount() {\n  instructionState.lFrame.elementDepthCount++;\n}\n\nexport function decreaseElementDepthCount() {\n  instructionState.lFrame.elementDepthCount--;\n}\n\nexport function getBindingsEnabled(): boolean {\n  return instructionState.bindingsEnabled;\n}\n\n/**\n * Returns true if currently inside a skip hydration block.\n * @returns boolean\n */\nexport function isInSkipHydrationBlock(): boolean {\n  return instructionState.skipHydrationRootTNode !== null;\n}\n\n/**\n * Returns true if this is the root TNode of the skip hydration block.\n * @param tNode the current TNode\n * @returns boolean\n */\nexport function isSkipHydrationRootTNode(tNode: TNode): boolean {\n  return instructionState.skipHydrationRootTNode === tNode;\n}\n\n/**\n * Enables directive matching on elements.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nexport function ɵɵenableBindings(): void {\n  instructionState.bindingsEnabled = true;\n}\n\n/**\n * Sets a flag to specify that the TNode is in a skip hydration block.\n * @param tNode the current TNode\n */\nexport function enterSkipHydrationBlock(tNode: TNode): void {\n  instructionState.skipHydrationRootTNode = tNode;\n}\n\n/**\n * Disables directive matching on element.\n *\n *  * Example:\n * ```html\n * <my-comp my-directive>\n *   Should match component / directive.\n * </my-comp>\n * <div ngNonBindable>\n *   <!-- ɵɵdisableBindings() -->\n *   <my-comp my-directive>\n *     Should not match component / directive because we are in ngNonBindable.\n *   </my-comp>\n *   <!-- ɵɵenableBindings() -->\n * </div>\n * ```\n *\n * @codeGenApi\n */\nexport function ɵɵdisableBindings(): void {\n  instructionState.bindingsEnabled = false;\n}\n\n/**\n * Clears the root skip hydration node when leaving a skip hydration block.\n */\nexport function leaveSkipHydrationBlock(): void {\n  instructionState.skipHydrationRootTNode = null;\n}\n\n/**\n * Return the current `LView`.\n */\nexport function getLView<T>(): LView<T> {\n  return instructionState.lFrame.lView as LView<T>;\n}\n\n/**\n * Return the current `TView`.\n */\nexport function getTView(): TView {\n  return instructionState.lFrame.tView;\n}\n\n/**\n * Restores `contextViewData` to the given OpaqueViewState instance.\n *\n * Used in conjunction with the getCurrentView() instruction to save a snapshot\n * of the current view and restore it when listeners are invoked. This allows\n * walking the declaration view tree in listeners to get vars from parent views.\n *\n * @param viewToRestore The OpaqueViewState instance to restore.\n * @returns Context of the restored OpaqueViewState instance.\n *\n * @codeGenApi\n */\nexport function ɵɵrestoreView<T = any>(viewToRestore: OpaqueViewState): T {\n  instructionState.lFrame.contextLView = viewToRestore as any as LView;\n  return (viewToRestore as any as LView)[CONTEXT] as unknown as T;\n}\n\n/**\n * Clears the view set in `ɵɵrestoreView` from memory. Returns the passed in\n * value so that it can be used as a return value of an instruction.\n *\n * @codeGenApi\n */\nexport function ɵɵresetView<T>(value?: T): T | undefined {\n  instructionState.lFrame.contextLView = null;\n  return value;\n}\n\nexport function getCurrentTNode(): TNode | null {\n  let currentTNode = getCurrentTNodePlaceholderOk();\n  while (currentTNode !== null && currentTNode.type === TNodeType.Placeholder) {\n    currentTNode = currentTNode.parent;\n  }\n  return currentTNode;\n}\n\nexport function getCurrentTNodePlaceholderOk(): TNode | null {\n  return instructionState.lFrame.currentTNode;\n}\n\nexport function getCurrentParentTNode(): TNode | null {\n  const lFrame = instructionState.lFrame;\n  const currentTNode = lFrame.currentTNode;\n  return lFrame.isParent ? currentTNode : currentTNode!.parent;\n}\n\nexport function setCurrentTNode(tNode: TNode | null, isParent: boolean) {\n  ngDevMode && tNode && assertTNodeForTView(tNode, instructionState.lFrame.tView);\n  const lFrame = instructionState.lFrame;\n  lFrame.currentTNode = tNode;\n  lFrame.isParent = isParent;\n}\n\nexport function isCurrentTNodeParent(): boolean {\n  return instructionState.lFrame.isParent;\n}\n\nexport function setCurrentTNodeAsNotParent(): void {\n  instructionState.lFrame.isParent = false;\n}\n\nexport function getContextLView(): LView {\n  const contextLView = instructionState.lFrame.contextLView;\n  ngDevMode && assertDefined(contextLView, 'contextLView must be defined.');\n  return contextLView!;\n}\n\nexport function isInCheckNoChangesMode(): boolean {\n  !ngDevMode && throwError('Must never be called in production mode');\n  return _checkNoChangesMode !== CheckNoChangesMode.Off;\n}\n\nexport function isExhaustiveCheckNoChanges(): boolean {\n  !ngDevMode && throwError('Must never be called in production mode');\n  return _checkNoChangesMode === CheckNoChangesMode.Exhaustive;\n}\n\nexport function setIsInCheckNoChangesMode(mode: CheckNoChangesMode): void {\n  !ngDevMode && throwError('Must never be called in production mode');\n  _checkNoChangesMode = mode;\n}\n\nexport function isRefreshingViews(): boolean {\n  return _isRefreshingViews;\n}\n\nexport function setIsRefreshingViews(mode: boolean): boolean {\n  const prev = _isRefreshingViews;\n  _isRefreshingViews = mode;\n  return prev;\n}\n\n// top level variables should not be exported for performance reasons (PERF_NOTES.md)\nexport function getBindingRoot() {\n  const lFrame = instructionState.lFrame;\n  let index = lFrame.bindingRootIndex;\n  if (index === -1) {\n    index = lFrame.bindingRootIndex = lFrame.tView.bindingStartIndex;\n  }\n  return index;\n}\n\nexport function getBindingIndex(): number {\n  return instructionState.lFrame.bindingIndex;\n}\n\nexport function setBindingIndex(value: number): number {\n  return (instructionState.lFrame.bindingIndex = value);\n}\n\nexport function nextBindingIndex(): number {\n  return instructionState.lFrame.bindingIndex++;\n}\n\nexport function incrementBindingIndex(count: number): number {\n  const lFrame = instructionState.lFrame;\n  const index = lFrame.bindingIndex;\n  lFrame.bindingIndex = lFrame.bindingIndex + count;\n  return index;\n}\n\nexport function isInI18nBlock() {\n  return instructionState.lFrame.inI18n;\n}\n\nexport function setInI18nBlock(isInI18nBlock: boolean): void {\n  instructionState.lFrame.inI18n = isInI18nBlock;\n}\n\n/**\n * Set a new binding root index so that host template functions can execute.\n *\n * Bindings inside the host template are 0 index. But because we don't know ahead of time\n * how many host bindings we have we can't pre-compute them. For this reason they are all\n * 0 index and we just shift the root so that they match next available location in the LView.\n *\n * @param bindingRootIndex Root index for `hostBindings`\n * @param currentDirectiveIndex `TData[currentDirectiveIndex]` will point to the current directive\n *        whose `hostBindings` are being processed.\n */\nexport function setBindingRootForHostBindings(\n  bindingRootIndex: number,\n  currentDirectiveIndex: number,\n) {\n  const lFrame = instructionState.lFrame;\n  lFrame.bindingIndex = lFrame.bindingRootIndex = bindingRootIndex;\n  setCurrentDirectiveIndex(currentDirectiveIndex);\n}\n\n/**\n * When host binding is executing this points to the directive index.\n * `TView.data[getCurrentDirectiveIndex()]` is `DirectiveDef`\n * `LView[getCurrentDirectiveIndex()]` is directive instance.\n */\nexport function getCurrentDirectiveIndex(): number {\n  return instructionState.lFrame.currentDirectiveIndex;\n}\n\n/**\n * Sets an index of a directive whose `hostBindings` are being processed.\n *\n * @param currentDirectiveIndex `TData` index where current directive instance can be found.\n */\nexport function setCurrentDirectiveIndex(currentDirectiveIndex: number): void {\n  instructionState.lFrame.currentDirectiveIndex = currentDirectiveIndex;\n}\n\n/**\n * Retrieve the current `DirectiveDef` which is active when `hostBindings` instruction is being\n * executed.\n *\n * @param tData Current `TData` where the `DirectiveDef` will be looked up at.\n */\nexport function getCurrentDirectiveDef(tData: TData): DirectiveDef<any> | null {\n  const currentDirectiveIndex = instructionState.lFrame.currentDirectiveIndex;\n  return currentDirectiveIndex === -1 ? null : (tData[currentDirectiveIndex] as DirectiveDef<any>);\n}\n\nexport function getCurrentQueryIndex(): number {\n  return instructionState.lFrame.currentQueryIndex;\n}\n\nexport function setCurrentQueryIndex(value: number): void {\n  instructionState.lFrame.currentQueryIndex = value;\n}\n\n/**\n * Returns a `TNode` of the location where the current `LView` is declared at.\n *\n * @param lView an `LView` that we want to find parent `TNode` for.\n */\nfunction getDeclarationTNode(lView: LView): TNode | null {\n  const tView = lView[TVIEW];\n\n  // Return the declaration parent for embedded views\n  if (tView.type === TViewType.Embedded) {\n    ngDevMode && assertDefined(tView.declTNode, 'Embedded TNodes should have declaration parents.');\n    return tView.declTNode;\n  }\n\n  // Components don't have `TView.declTNode` because each instance of component could be\n  // inserted in different location, hence `TView.declTNode` is meaningless.\n  // Falling back to `T_HOST` in case we cross component boundary.\n  if (tView.type === TViewType.Component) {\n    return lView[T_HOST];\n  }\n\n  // Remaining TNode type is `TViewType.Root` which doesn't have a parent TNode.\n  return null;\n}\n\n/**\n * This is a light weight version of the `enterView` which is needed by the DI system.\n *\n * @param lView `LView` location of the DI context.\n * @param tNode `TNode` for DI context\n * @param flags DI context flags. if `SkipSelf` flag is set than we walk up the declaration\n *     tree from `tNode`  until we find parent declared `TElementNode`.\n * @returns `true` if we have successfully entered DI associated with `tNode` (or with declared\n *     `TNode` if `flags` has  `SkipSelf`). Failing to enter DI implies that no associated\n *     `NodeInjector` can be found and we should instead use `ModuleInjector`.\n *     - If `true` than this call must be fallowed by `leaveDI`\n *     - If `false` than this call failed and we should NOT call `leaveDI`\n */\nexport function enterDI(lView: LView, tNode: TNode, flags: InternalInjectFlags) {\n  ngDevMode && assertLViewOrUndefined(lView);\n\n  if (flags & InternalInjectFlags.SkipSelf) {\n    ngDevMode && assertTNodeForTView(tNode, lView[TVIEW]);\n\n    let parentTNode = tNode as TNode | null;\n    let parentLView = lView;\n\n    while (true) {\n      ngDevMode && assertDefined(parentTNode, 'Parent TNode should be defined');\n      parentTNode = parentTNode!.parent as TNode | null;\n      if (parentTNode === null && !(flags & InternalInjectFlags.Host)) {\n        parentTNode = getDeclarationTNode(parentLView);\n        if (parentTNode === null) break;\n\n        // In this case, a parent exists and is definitely an element. So it will definitely\n        // have an existing lView as the declaration view, which is why we can assume it's defined.\n        ngDevMode && assertDefined(parentLView, 'Parent LView should be defined');\n        parentLView = parentLView[DECLARATION_VIEW]!;\n\n        // In Ivy there are Comment nodes that correspond to ngIf and NgFor embedded directives\n        // We want to skip those and look only at Elements and ElementContainers to ensure\n        // we're looking at true parent nodes, and not content or other types.\n        if (parentTNode.type & (TNodeType.Element | TNodeType.ElementContainer)) {\n          break;\n        }\n      } else {\n        break;\n      }\n    }\n    if (parentTNode === null) {\n      // If we failed to find a parent TNode this means that we should use module injector.\n      return false;\n    } else {\n      tNode = parentTNode;\n      lView = parentLView;\n    }\n  }\n\n  ngDevMode && assertTNodeForLView(tNode, lView);\n  const lFrame = (instructionState.lFrame = allocLFrame());\n  lFrame.currentTNode = tNode;\n  lFrame.lView = lView;\n\n  return true;\n}\n\n/**\n * Swap the current lView with a new lView.\n *\n * For performance reasons we store the lView in the top level of the module.\n * This way we minimize the number of properties to read. Whenever a new view\n * is entered we have to store the lView for later, and when the view is\n * exited the state has to be restored\n *\n * @param newView New lView to become active\n * @returns the previously active lView;\n */\nexport function enterView(newView: LView): void {\n  ngDevMode && assertNotEqual(newView[0], newView[1] as any, '????');\n  ngDevMode && assertLViewOrUndefined(newView);\n  const newLFrame = allocLFrame();\n  if (ngDevMode) {\n    assertEqual(newLFrame.isParent, true, 'Expected clean LFrame');\n    assertEqual(newLFrame.lView, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.tView, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.selectedIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.elementDepthCount, 0, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentDirectiveIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentNamespace, null, 'Expected clean LFrame');\n    assertEqual(newLFrame.bindingRootIndex, -1, 'Expected clean LFrame');\n    assertEqual(newLFrame.currentQueryIndex, 0, 'Expected clean LFrame');\n  }\n  const tView = newView[TVIEW];\n  instructionState.lFrame = newLFrame;\n  ngDevMode && tView.firstChild && assertTNodeForTView(tView.firstChild, tView);\n  newLFrame.currentTNode = tView.firstChild!;\n  newLFrame.lView = newView;\n  newLFrame.tView = tView;\n  newLFrame.contextLView = newView;\n  newLFrame.bindingIndex = tView.bindingStartIndex;\n  newLFrame.inI18n = false;\n}\n\n/**\n * Allocates next free LFrame. This function tries to reuse the `LFrame`s to lower memory pressure.\n */\nfunction allocLFrame() {\n  const currentLFrame = instructionState.lFrame;\n  const childLFrame = currentLFrame === null ? null : currentLFrame.child;\n  const newLFrame = childLFrame === null ? createLFrame(currentLFrame) : childLFrame;\n  return newLFrame;\n}\n\nfunction createLFrame(parent: LFrame | null): LFrame {\n  const lFrame: LFrame = {\n    currentTNode: null,\n    isParent: true,\n    lView: null!,\n    tView: null!,\n    selectedIndex: -1,\n    contextLView: null,\n    elementDepthCount: 0,\n    currentNamespace: null,\n    currentDirectiveIndex: -1,\n    bindingRootIndex: -1,\n    bindingIndex: -1,\n    currentQueryIndex: 0,\n    parent: parent!,\n    child: null,\n    inI18n: false,\n  };\n  parent !== null && (parent.child = lFrame); // link the new LFrame for reuse.\n  return lFrame;\n}\n\n/**\n * A lightweight version of leave which is used with DI.\n *\n * This function only resets `currentTNode` and `LView` as those are the only properties\n * used with DI (`enterDI()`).\n *\n * NOTE: This function is reexported as `leaveDI`. However `leaveDI` has return type of `void` where\n * as `leaveViewLight` has `LFrame`. This is so that `leaveViewLight` can be used in `leaveView`.\n */\nfunction leaveViewLight(): LFrame {\n  const oldLFrame = instructionState.lFrame;\n  instructionState.lFrame = oldLFrame.parent;\n  oldLFrame.currentTNode = null!;\n  oldLFrame.lView = null!;\n  return oldLFrame;\n}\n\n/**\n * This is a lightweight version of the `leaveView` which is needed by the DI system.\n *\n * NOTE: this function is an alias so that we can change the type of the function to have `void`\n * return type.\n */\nexport const leaveDI: () => void = leaveViewLight;\n\n/**\n * Leave the current `LView`\n *\n * This pops the `LFrame` with the associated `LView` from the stack.\n *\n * IMPORTANT: We must zero out the `LFrame` values here otherwise they will be retained. This is\n * because for performance reasons we don't release `LFrame` but rather keep it for next use.\n */\nexport function leaveView() {\n  const oldLFrame = leaveViewLight();\n  oldLFrame.isParent = true;\n  oldLFrame.tView = null!;\n  oldLFrame.selectedIndex = -1;\n  oldLFrame.contextLView = null;\n  oldLFrame.elementDepthCount = 0;\n  oldLFrame.currentDirectiveIndex = -1;\n  oldLFrame.currentNamespace = null;\n  oldLFrame.bindingRootIndex = -1;\n  oldLFrame.bindingIndex = -1;\n  oldLFrame.currentQueryIndex = 0;\n}\n\nexport function nextContextImpl<T = any>(level: number): T {\n  const contextLView = (instructionState.lFrame.contextLView = walkUpViews(\n    level,\n    instructionState.lFrame.contextLView!,\n  ));\n  return contextLView[CONTEXT] as unknown as T;\n}\n\n/**\n * Gets the currently selected element index.\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n */\nexport function getSelectedIndex() {\n  return instructionState.lFrame.selectedIndex;\n}\n\n/**\n * Sets the most recent index passed to {@link select}\n *\n * Used with {@link property} instruction (and more in the future) to identify the index in the\n * current `LView` to act on.\n *\n * (Note that if an \"exit function\" was set earlier (via `setElementExitFn()`) then that will be\n * run if and when the provided `index` value is different from the current selected index value.)\n */\nexport function setSelectedIndex(index: number) {\n  ngDevMode &&\n    index !== -1 &&\n    assertGreaterThanOrEqual(index, HEADER_OFFSET, 'Index must be past HEADER_OFFSET (or -1).');\n  ngDevMode &&\n    assertLessThan(\n      index,\n      instructionState.lFrame.lView.length,\n      \"Can't set index passed end of LView\",\n    );\n  instructionState.lFrame.selectedIndex = index;\n}\n\n/**\n * Gets the `tNode` that represents currently selected element.\n */\nexport function getSelectedTNode() {\n  const lFrame = instructionState.lFrame;\n  return getTNode(lFrame.tView, lFrame.selectedIndex);\n}\n\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/2000/svg'` in global state.\n *\n * @codeGenApi\n */\nexport function ɵɵnamespaceSVG() {\n  instructionState.lFrame.currentNamespace = SVG_NAMESPACE;\n}\n\n/**\n * Sets the namespace used to create elements to `'http://www.w3.org/1998/MathML/'` in global state.\n *\n * @codeGenApi\n */\nexport function ɵɵnamespaceMathML() {\n  instructionState.lFrame.currentNamespace = MATH_ML_NAMESPACE;\n}\n\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n *\n * @codeGenApi\n */\nexport function ɵɵnamespaceHTML() {\n  namespaceHTMLInternal();\n}\n\n/**\n * Sets the namespace used to create elements to `null`, which forces element creation to use\n * `createElement` rather than `createElementNS`.\n */\nexport function namespaceHTMLInternal() {\n  instructionState.lFrame.currentNamespace = null;\n}\n\nexport function getNamespace(): string | null {\n  return instructionState.lFrame.currentNamespace;\n}\n\nlet _wasLastNodeCreated = true;\n\n/**\n * Retrieves a global flag that indicates whether the most recent DOM node\n * was created or hydrated.\n */\nexport function wasLastNodeCreated(): boolean {\n  return _wasLastNodeCreated;\n}\n\n/**\n * Sets a global flag to indicate whether the most recent DOM node\n * was created or hydrated.\n */\nexport function lastNodeWasCreated(flag: boolean): void {\n  _wasLastNodeCreated = flag;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EMPTY_ARRAY} from '../util/empty';\nimport {stringify} from '../util/stringify';\n\nimport type {Injector} from './injector';\nimport type {Provider, StaticProvider} from './interface/provider';\nimport {importProvidersFrom} from './provider_collection';\nimport {getNullInjector, R3Injector} from './r3_injector';\nimport {InjectorScope} from './scope';\n\n/**\n * Create a new `Injector` which is configured using a `defType` of `InjectorType<any>`s.\n */\nexport function createInjector(\n  defType: /* InjectorType<any> */ any,\n  parent: Injector | null = null,\n  additionalProviders: Array<Provider | StaticProvider> | null = null,\n  name?: string,\n): Injector {\n  const injector = createInjectorWithoutInjectorInstances(\n    defType,\n    parent,\n    additionalProviders,\n    name,\n  );\n  injector.resolveInjectorInitializers();\n  return injector;\n}\n\n/**\n * Creates a new injector without eagerly resolving its injector types. Can be used in places\n * where resolving the injector types immediately can lead to an infinite loop. The injector types\n * should be resolved at a later point by calling `_resolveInjectorDefTypes`.\n */\nexport function createInjectorWithoutInjectorInstances(\n  defType: /* InjectorType<any> */ any,\n  parent: Injector | null = null,\n  additionalProviders: Array<Provider | StaticProvider> | null = null,\n  name?: string,\n  scopes = new Set<InjectorScope>(),\n): R3Injector {\n  const providers = [additionalProviders || EMPTY_ARRAY, importProvidersFrom(defType)];\n  name = name || (typeof defType === 'object' ? undefined : stringify(defType));\n\n  return new R3Injector(providers, parent || getNullInjector(), name || null, scopes);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {createInjector} from './create_injector';\nimport {THROW_IF_NOT_FOUND, ɵɵinject} from './injector_compatibility';\nimport {InjectorMarkers} from './injector_marker';\nimport {INJECTOR} from './injector_token';\nimport {ɵɵdefineInjectable} from './interface/defs';\nimport {InjectOptions} from './interface/injector';\nimport {Provider, StaticProvider} from './interface/provider';\nimport {NullInjector} from './null_injector';\nimport {ProviderToken} from './provider_token';\n\n/**\n * Concrete injectors implement this interface. Injectors are configured\n * with [providers](guide/di/dependency-injection-providers) that associate\n * dependencies of various types with [injection tokens](guide/di/dependency-injection-providers).\n *\n * @see [DI Providers](guide/di/dependency-injection-providers).\n * @see {@link StaticProvider}\n *\n * @usageNotes\n *\n *  The following example creates a service injector instance.\n *\n * {@example core/di/ts/provider_spec.ts region='ConstructorProvider'}\n *\n * ### Usage example\n *\n * {@example core/di/ts/injector_spec.ts region='Injector'}\n *\n * `Injector` returns itself when given `Injector` as a token:\n *\n * {@example core/di/ts/injector_spec.ts region='injectInjector'}\n *\n * @publicApi\n */\nexport abstract class Injector {\n  static THROW_IF_NOT_FOUND = THROW_IF_NOT_FOUND;\n  static NULL: Injector = /* @__PURE__ */ new NullInjector();\n\n  /**\n   * Retrieves an instance from the injector based on the provided token.\n   * @returns The instance from the injector if defined, otherwise the `notFoundValue`.\n   * @throws When the `notFoundValue` is `undefined` or `Injector.THROW_IF_NOT_FOUND`.\n   */\n  abstract get<T>(\n    token: ProviderToken<T>,\n    notFoundValue: undefined,\n    options: InjectOptions & {\n      optional?: false;\n    },\n  ): T;\n  /**\n   * Retrieves an instance from the injector based on the provided token.\n   * @returns The instance from the injector if defined, otherwise the `notFoundValue`.\n   * @throws When the `notFoundValue` is `undefined` or `Injector.THROW_IF_NOT_FOUND`.\n   */\n  abstract get<T>(\n    token: ProviderToken<T>,\n    notFoundValue: null | undefined,\n    options: InjectOptions,\n  ): T | null;\n  /**\n   * Retrieves an instance from the injector based on the provided token.\n   * @returns The instance from the injector if defined, otherwise the `notFoundValue`.\n   * @throws When the `notFoundValue` is `undefined` or `Injector.THROW_IF_NOT_FOUND`.\n   */\n  abstract get<T>(token: ProviderToken<T>, notFoundValue?: T, options?: InjectOptions): T;\n  /**\n   * @deprecated from v4.0.0 use ProviderToken<T>\n   * @suppress {duplicate}\n   */\n  abstract get<T>(token: string | ProviderToken<T>, notFoundValue?: any): any;\n\n  /**\n   * @deprecated from v5 use the new signature Injector.create(options)\n   */\n  static create(providers: StaticProvider[], parent?: Injector): Injector;\n\n  /**\n   * Creates a new injector instance that provides one or more dependencies,\n   * according to a given type or types of `StaticProvider`.\n   *\n   * @param options An object with the following properties:\n   * * `providers`: An array of providers of the [StaticProvider type](api/core/StaticProvider).\n   * * `parent`: (optional) A parent injector.\n   * * `name`: (optional) A developer-defined identifying name for the new injector.\n   *\n   * @returns The new injector instance.\n   *\n   */\n  static create(options: {\n    providers: Array<Provider | StaticProvider>;\n    parent?: Injector;\n    name?: string;\n  }): DestroyableInjector;\n\n  static create(\n    options:\n      | StaticProvider[]\n      | {providers: Array<Provider | StaticProvider>; parent?: Injector; name?: string},\n    parent?: Injector,\n  ): Injector {\n    if (Array.isArray(options)) {\n      return createInjector({name: ''}, parent, options, '');\n    } else {\n      const name = options.name ?? '';\n      return createInjector({name}, options.parent, options.providers, name);\n    }\n  }\n\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n    token: Injector,\n    providedIn: 'any',\n    factory: () => ɵɵinject(INJECTOR),\n  });\n\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static __NG_ELEMENT_ID__ = InjectorMarkers.Injector;\n}\n\n/**\n * An Injector that the owner can destroy and trigger the DestroyRef.destroy hooks.\n *\n * @publicApi\n */\nexport interface DestroyableInjector extends Injector {\n  destroy(): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from './di';\n\n/**\n * A DI Token representing the main rendering context.\n * In a browser and SSR this is the DOM Document.\n * When using SSR, that document is created by [<PERSON><PERSON>](https://github.com/angular/domino).\n *\n * @publicApi\n */\nexport const DOCUMENT = new InjectionToken<Document>(ngDevMode ? 'DocumentToken' : '');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {EnvironmentInjector} from '../di';\nimport {isDestroyed} from '../render3/interfaces/type_checks';\nimport {LView} from '../render3/interfaces/view';\nimport {getLView} from '../render3/state';\nimport {removeLViewOnDestroy, storeLViewOnDestroy} from '../render3/util/view_utils';\n\nconst EXECUTE_CALLBACK_IF_ALREADY_DESTROYED = false;\n\n/**\n * `DestroyRef` lets you set callbacks to run for any cleanup or destruction behavior.\n * The scope of this destruction depends on where `DestroyRef` is injected. If `DestroyRef`\n * is injected in a component or directive, the callbacks run when that component or\n * directive is destroyed. Otherwise the callbacks run when a corresponding injector is destroyed.\n *\n * @publicApi\n */\nexport abstract class DestroyRef {\n  // Here the `DestroyRef` acts primarily as a DI token. There are (currently) types of objects that\n  // can be returned from the injector when asking for this token:\n  // - `NodeInjectorDestroyRef` when retrieved from a node injector;\n  // - `EnvironmentInjector` when retrieved from an environment injector\n\n  /**\n   * Registers a destroy callback in a given lifecycle scope.  Returns a cleanup function that can\n   * be invoked to unregister the callback.\n   *\n   * @usageNotes\n   * ### Example\n   * ```ts\n   * const destroyRef = inject(DestroyRef);\n   *\n   * // register a destroy callback\n   * const unregisterFn = destroyRef.onDestroy(() => doSomethingOnDestroy());\n   *\n   * // stop the destroy callback from executing if needed\n   * unregisterFn();\n   * ```\n   */\n  abstract onDestroy(callback: () => void): () => void;\n\n  /** @internal */\n  abstract get destroyed(): boolean;\n\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static __NG_ELEMENT_ID__: () => DestroyRef = injectDestroyRef;\n\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static __NG_ENV_ID__: (injector: EnvironmentInjector) => DestroyRef = (injector) => injector;\n}\n\nexport class NodeInjectorDestroyRef extends DestroyRef {\n  constructor(readonly _lView: LView) {\n    super();\n  }\n\n  override get destroyed() {\n    return isDestroyed(this._lView);\n  }\n\n  override onDestroy(callback: () => void): () => void {\n    const lView = this._lView;\n\n    // TODO(atscott): Remove once g3 cleanup is complete\n    if (EXECUTE_CALLBACK_IF_ALREADY_DESTROYED && isDestroyed(lView)) {\n      callback();\n      return () => {};\n    }\n\n    storeLViewOnDestroy(lView, callback);\n    return () => removeLViewOnDestroy(lView, callback);\n  }\n}\n\nfunction injectDestroyRef(): DestroyRef {\n  return new NodeInjectorDestroyRef(getLView());\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ENVIRONMENT_INITIALIZER} from './di/initializer_token';\nimport {InjectionToken} from './di/injection_token';\nimport {inject} from './di/injector_compatibility';\nimport type {EnvironmentProviders} from './di/interface/provider';\nimport {makeEnvironmentProviders, provideEnvironmentInitializer} from './di/provider_collection';\nimport {EnvironmentInjector} from './di/r3_injector';\nimport {DOCUMENT} from './document';\nimport {DestroyRef} from './linker/destroy_ref';\n\n/**\n * Provides a hook for centralized exception handling.\n *\n * The default implementation of `ErrorHandler` prints error messages to the `console`. To\n * intercept error handling, write a custom exception handler that replaces this default as\n * appropriate for your app.\n *\n * @usageNotes\n * ### Example\n *\n * ```ts\n * class MyErrorHandler implements <PERSON>rrorHandler {\n *   handleError(error) {\n *     // do something with the exception\n *   }\n * }\n *\n * // Provide in standalone apps\n * bootstrapApplication(AppComponent, {\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n *\n * // Provide in module-based apps\n * @NgModule({\n *   providers: [{provide: ErrorHandler, useClass: MyErrorHandler}]\n * })\n * class MyModule {}\n * ```\n *\n * @publicApi\n */\nexport class ErrorHandler {\n  /**\n   * @internal\n   */\n  _console: Console = console;\n\n  handleError(error: any): void {\n    this._console.error('ERROR', error);\n  }\n}\n\n/**\n * `InjectionToken` used to configure how to call the `ErrorHandler`.\n */\nexport const INTERNAL_APPLICATION_ERROR_HANDLER = new InjectionToken<(e: any) => void>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'internal error handler' : '',\n  {\n    providedIn: 'root',\n    factory: () => {\n      // The user's error handler may depend on things that create a circular dependency\n      // so we inject it lazily.\n      const injector = inject(EnvironmentInjector);\n      let userErrorHandler: ErrorHandler;\n      return (e: unknown) => {\n        if (injector.destroyed && !userErrorHandler) {\n          setTimeout(() => {\n            throw e;\n          });\n        } else {\n          userErrorHandler ??= injector.get(ErrorHandler);\n          userErrorHandler.handleError(e);\n        }\n      };\n    },\n  },\n);\n\nexport const errorHandlerEnvironmentInitializer = {\n  provide: ENVIRONMENT_INITIALIZER,\n  useValue: () => void inject(ErrorHandler),\n  multi: true,\n};\n\nconst globalErrorListeners = new InjectionToken<void>(ngDevMode ? 'GlobalErrorListeners' : '', {\n  providedIn: 'root',\n  factory: () => {\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n      return;\n    }\n    const window = inject(DOCUMENT).defaultView;\n    if (!window) {\n      return;\n    }\n\n    const errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n    const rejectionListener = (e: PromiseRejectionEvent) => {\n      errorHandler(e.reason);\n      e.preventDefault();\n    };\n    const errorListener = (e: ErrorEvent) => {\n      if (e.error) {\n        errorHandler(e.error);\n      } else {\n        errorHandler(\n          new Error(\n            ngDevMode\n              ? `An ErrorEvent with no error occurred. See Error.cause for details: ${e.message}`\n              : e.message,\n            {cause: e},\n          ),\n        );\n      }\n      e.preventDefault();\n    };\n\n    const setupEventListeners = () => {\n      window.addEventListener('unhandledrejection', rejectionListener);\n      window.addEventListener('error', errorListener);\n    };\n\n    // Angular doesn't have to run change detection whenever any asynchronous tasks are invoked in\n    // the scope of this functionality.\n    if (typeof Zone !== 'undefined') {\n      Zone.root.run(setupEventListeners);\n    } else {\n      setupEventListeners();\n    }\n\n    inject(DestroyRef).onDestroy(() => {\n      window.removeEventListener('error', errorListener);\n      window.removeEventListener('unhandledrejection', rejectionListener);\n    });\n  },\n});\n\n/**\n * Provides an environment initializer which forwards unhandled errors to the ErrorHandler.\n *\n * The listeners added are for the window's 'unhandledrejection' and 'error' events.\n *\n * @publicApi\n */\nexport function provideBrowserGlobalErrorListeners(): EnvironmentProviders {\n  return makeEnvironmentProviders([\n    provideEnvironmentInitializer(() => void inject(globalErrorListeners)),\n  ]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {SIGNAL} from '../../../primitives/signals';\n\n/**\n * A reactive value which notifies consumers of any changes.\n *\n * Signals are functions which returns their current value. To access the current value of a signal,\n * call it.\n *\n * Ordinary values can be turned into `Signal`s with the `signal` function.\n *\n * @publicApi 17.0\n */\nexport type Signal<T> = (() => T) & {\n  [SIGNAL]: unknown;\n};\n\n/**\n * Checks if the given `value` is a reactive `Signal`.\n *\n * @publicApi 17.0\n */\nexport function isSignal(value: unknown): value is Signal<unknown> {\n  return typeof value === 'function' && (value as Signal<unknown>)[SIGNAL] !== undefined;\n}\n\n/**\n * A comparison function which can determine if two values are equal.\n */\nexport type ValueEqualityFn<T> = (a: T, b: T) => boolean;\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  createSignal,\n  SIGNAL,\n  SignalGetter,\n  SignalNode,\n  signalSetFn,\n  signalUpdateFn,\n} from '../../../primitives/signals';\n\nimport {isSignal, Signal, ValueEqualityFn} from './api';\n\n/** Symbol used distinguish `WritableSignal` from other non-writable signals and functions. */\nexport const ɵWRITABLE_SIGNAL: unique symbol = /* @__PURE__ */ Symbol('WRITABLE_SIGNAL');\n\n/**\n * A `Signal` with a value that can be mutated via a setter interface.\n *\n * @publicApi 17.0\n */\nexport interface WritableSignal<T> extends Signal<T> {\n  [ɵWRITABLE_SIGNAL]: T;\n\n  /**\n   * Directly set the signal to a new value, and notify any dependents.\n   */\n  set(value: T): void;\n\n  /**\n   * Update the value of the signal based on its current value, and\n   * notify any dependents.\n   */\n  update(updateFn: (value: T) => T): void;\n\n  /**\n   * Returns a readonly version of this signal. Readonly signals can be accessed to read their value\n   * but can't be changed using set or update methods. The readonly signals do _not_ have\n   * any built-in mechanism that would prevent deep-mutation of their value.\n   */\n  asReadonly(): Signal<T>;\n}\n\n/**\n * Utility function used during template type checking to extract the value from a `WritableSignal`.\n * @codeGenApi\n */\nexport function ɵunwrapWritableSignal<T>(value: T | {[ɵWRITABLE_SIGNAL]: T}): T {\n  // Note: the function uses `WRITABLE_SIGNAL` as a brand instead of `WritableSignal<T>`,\n  // because the latter incorrectly unwraps non-signal getter functions.\n  return null!;\n}\n\n/**\n * Options passed to the `signal` creation function.\n */\nexport interface CreateSignalOptions<T> {\n  /**\n   * A comparison function which defines equality for signal values.\n   */\n  equal?: ValueEqualityFn<T>;\n\n  /**\n   * A debug name for the signal. Used in Angular DevTools to identify the signal.\n   */\n  debugName?: string;\n}\n\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nexport function signal<T>(initialValue: T, options?: CreateSignalOptions<T>): WritableSignal<T> {\n  const [get, set, update] = createSignal(initialValue, options?.equal);\n\n  const signalFn = get as SignalGetter<T> & WritableSignal<T>;\n  const node = signalFn[SIGNAL];\n\n  signalFn.set = set;\n  signalFn.update = update;\n  signalFn.asReadonly = signalAsReadonlyFn.bind(signalFn as any) as () => Signal<T>;\n\n  if (ngDevMode) {\n    signalFn.toString = () => `[Signal: ${signalFn()}]`;\n    node.debugName = options?.debugName;\n  }\n\n  return signalFn as WritableSignal<T>;\n}\n\nexport function signalAsReadonlyFn<T>(this: SignalGetter<T>): Signal<T> {\n  const node = this[SIGNAL] as SignalNode<T> & {readonlyFn?: Signal<T>};\n  if (node.readonlyFn === undefined) {\n    const readonlyFn = () => this();\n    (readonlyFn as any)[SIGNAL] = node;\n    node.readonlyFn = readonlyFn as Signal<T>;\n  }\n  return node.readonlyFn;\n}\n\n/**\n * Checks if the given `value` is a writeable signal.\n */\nexport function isWritableSignal(value: unknown): value is WritableSignal<unknown> {\n  return isSignal(value) && typeof (value as any).set === 'function';\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken} from '../../di/injection_token';\n\nexport const enum NotificationSource {\n  // Change detection needs to run in order to synchronize application state\n  // with the DOM when the following notifications are received:\n  // This operation indicates that a subtree needs to be traversed during change detection.\n  MarkAncestorsForTraversal,\n  // A component/directive gets a new input.\n  SetInput,\n  // Defer block state updates need change detection to fully render the state.\n  DeferBlockStateUpdate,\n  // Debugging tools updated state and have requested change detection.\n  DebugApplyChanges,\n  // ChangeDetectorRef.markForCheck indicates the component is dirty/needs to refresh.\n  MarkForCheck,\n\n  // Bound listener callbacks execute and can update state without causing other notifications from\n  // above.\n  Listener,\n\n  // Custom elements do sometimes require checking directly.\n  CustomElement,\n\n  // The following notifications do not require views to be refreshed\n  // but we should execute render hooks:\n  // Render hooks are guaranteed to execute with the schedulers timing.\n  RenderHook,\n  // Views might be created outside and manipulated in ways that\n  // we cannot be aware of. When a view is attached, <PERSON><PERSON> now \"knows\"\n  // about it and we now know that DOM might have changed (and we should\n  // run render hooks). If the attached view is dirty, the `MarkAncestorsForTraversal`\n  // notification should also be received.\n  ViewAttached,\n  // When DOM removal happens, render hooks may be interested in the new\n  // DOM state but we do not need to refresh any views unless. If change\n  // detection is required after DOM removal, another notification should\n  // be received (i.e. `markForCheck`).\n  ViewDetachedFromDOM,\n  // Applying animations might result in new DOM state and should rerun render hooks\n  AsyncAnimationsLoaded,\n  // The scheduler is notified when a pending task is removed via the public API.\n  // This allows us to make stability async, delayed until the next application tick.\n  PendingTaskRemoved,\n  // An `effect()` outside of the view tree became dirty and might need to run.\n  RootEffect,\n  // An `effect()` within the view tree became dirty.\n  ViewEffect,\n}\n\n/**\n * Injectable that is notified when an `LView` is made aware of changes to application state.\n */\nexport abstract class ChangeDetectionScheduler {\n  abstract notify(source: NotificationSource): void;\n  abstract runningTick: boolean;\n}\n\n/** Token used to indicate if zoneless was enabled via provideZonelessChangeDetection(). */\nexport const ZONELESS_ENABLED = new InjectionToken<boolean>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless enabled' : '',\n  {providedIn: 'root', factory: () => false},\n);\n\n/** Token used to indicate `provideZonelessChangeDetection` was used. */\nexport const PROVIDED_ZONELESS = new InjectionToken<boolean>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'Zoneless provided' : '',\n  {providedIn: 'root', factory: () => false},\n);\n\nexport const ZONELESS_SCHEDULER_DISABLED = new InjectionToken<boolean>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'scheduler disabled' : '',\n);\n\n// TODO(atscott): Remove in v19. Scheduler should be done with runOutsideAngular.\nexport const SCHEDULE_IN_ROOT_ZONE = new InjectionToken<boolean>(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'run changes outside zone in root' : '',\n);\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {getActiveConsumer} from '../../../primitives/signals';\n\nimport {RuntimeError, RuntimeErrorCode} from '../../errors';\n\n/**\n * Asserts that the current stack frame is not within a reactive context. Useful\n * to disallow certain code from running inside a reactive context (see {@link /api/core/rxjs/toSignal toSignal})\n *\n * @param debugFn a reference to the function making the assertion (used for the error message).\n *\n * @publicApi\n */\nexport function assertNotInReactiveContext(debugFn: Function, extraContext?: string): void {\n  // Taking a `Function` instead of a string name here prevents the un-minified name of the function\n  // from being retained in the bundle regardless of minification.\n  if (getActiveConsumer() !== null) {\n    throw new RuntimeError(\n      RuntimeErrorCode.ASSERTION_NOT_INSIDE_REACTIVE_CONTEXT,\n      ngDevMode &&\n        `${debugFn.name}() cannot be called from within a reactive context.${\n          extraContext ? ` ${extraContext}` : ''\n        }`,\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type {TNode} from './interfaces/node';\nimport type {LView} from './interfaces/view';\nimport {getCurrentTNode, getLView} from './state';\n\nexport class ViewContext {\n  constructor(\n    readonly view: LView,\n    readonly node: TNode,\n  ) {}\n\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static __NG_ELEMENT_ID__ = injectViewContext;\n}\n\nexport function injectViewContext(): ViewContext {\n  return new ViewContext(getLView()!, getCurrentTNode()!);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BehaviorSubject, Observable} from 'rxjs';\n\nimport {inject} from './di/injector_compatibility';\nimport {ɵɵdefineInjectable} from './di/interface/defs';\nimport {OnDestroy} from './interface/lifecycle_hooks';\nimport {\n  ChangeDetectionScheduler,\n  NotificationSource,\n} from './change_detection/scheduling/zoneless_scheduling';\nimport {INTERNAL_APPLICATION_ERROR_HANDLER} from './error_handler';\n\n/**\n * Internal implementation of the pending tasks service.\n */\nexport class PendingTasksInternal implements OnDestroy {\n  private taskId = 0;\n  private pendingTasks = new Set<number>();\n  private destroyed = false;\n\n  private pendingTask = new BehaviorSubject<boolean>(false);\n\n  get hasPendingTasks(): boolean {\n    // Accessing the value of a closed `BehaviorSubject` throws an error.\n    return this.destroyed ? false : this.pendingTask.value;\n  }\n\n  /**\n   * In case the service is about to be destroyed, return a self-completing observable.\n   * Otherwise, return the observable that emits the current state of pending tasks.\n   */\n  get hasPendingTasksObservable(): Observable<boolean> {\n    if (this.destroyed) {\n      // Manually creating the observable pulls less symbols from RxJS than `of(false)`.\n      return new Observable<boolean>((subscriber) => {\n        subscriber.next(false);\n        subscriber.complete();\n      });\n    }\n\n    return this.pendingTask;\n  }\n\n  add(): number {\n    // Emitting a value to a closed subject throws an error.\n    if (!this.hasPendingTasks && !this.destroyed) {\n      this.pendingTask.next(true);\n    }\n    const taskId = this.taskId++;\n    this.pendingTasks.add(taskId);\n    return taskId;\n  }\n\n  has(taskId: number): boolean {\n    return this.pendingTasks.has(taskId);\n  }\n\n  remove(taskId: number): void {\n    this.pendingTasks.delete(taskId);\n    if (this.pendingTasks.size === 0 && this.hasPendingTasks) {\n      this.pendingTask.next(false);\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.pendingTasks.clear();\n    if (this.hasPendingTasks) {\n      this.pendingTask.next(false);\n    }\n    // We call `unsubscribe()` to release observers, as users may forget to\n    // unsubscribe manually when subscribing to `isStable`. We do not call\n    // `complete()` because it is unsafe; if someone subscribes using the `first`\n    // operator and the observable completes before emitting a value,\n    // RxJS will throw an error.\n    this.destroyed = true;\n    this.pendingTask.unsubscribe();\n  }\n\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n    token: PendingTasksInternal,\n    providedIn: 'root',\n    factory: () => new PendingTasksInternal(),\n  });\n}\n\n/**\n * Service that keeps track of pending tasks contributing to the stableness of Angular\n * application. While several existing Angular services (ex.: `HttpClient`) will internally manage\n * tasks influencing stability, this API gives control over stability to library and application\n * developers for specific cases not covered by Angular internals.\n *\n * The concept of stability comes into play in several important scenarios:\n * - SSR process needs to wait for the application stability before serializing and sending rendered\n * HTML;\n * - tests might want to delay assertions until the application becomes stable;\n *\n * @usageNotes\n * ```ts\n * const pendingTasks = inject(PendingTasks);\n * const taskCleanup = pendingTasks.add();\n * // do work that should block application's stability and then:\n * taskCleanup();\n * ```\n *\n * @publicApi 20.0\n */\nexport class PendingTasks {\n  private readonly internalPendingTasks = inject(PendingTasksInternal);\n  private readonly scheduler = inject(ChangeDetectionScheduler);\n  private readonly errorHandler = inject(INTERNAL_APPLICATION_ERROR_HANDLER);\n  /**\n   * Adds a new task that should block application's stability.\n   * @returns A cleanup function that removes a task when called.\n   */\n  add(): () => void {\n    const taskId = this.internalPendingTasks.add();\n    return () => {\n      if (!this.internalPendingTasks.has(taskId)) {\n        // This pending task has already been cleared.\n        return;\n      }\n      // Notifying the scheduler will hold application stability open until the next tick.\n      this.scheduler.notify(NotificationSource.PendingTaskRemoved);\n      this.internalPendingTasks.remove(taskId);\n    };\n  }\n\n  /**\n   * Runs an asynchronous function and blocks the application's stability until the function completes.\n   *\n   * ```ts\n   * pendingTasks.run(async () => {\n   *   const userData = await fetch('/api/user');\n   *   this.userData.set(userData);\n   * });\n   * ```\n   *\n   * @param fn The asynchronous function to execute\n   * @developerPreview 19.0\n   */\n  run(fn: () => Promise<unknown>): void {\n    const removeTask = this.add();\n    fn().catch(this.errorHandler).finally(removeTask);\n  }\n\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n    token: PendingTasks,\n    providedIn: 'root',\n    factory: () => new PendingTasks(),\n  });\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport function noop(...args: any[]): any {\n  // Do nothing.\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵɵdefineInjectable} from '../../di/interface/defs';\n\n/**\n * Abstraction that encompasses any kind of effect that can be scheduled.\n */\nexport interface SchedulableEffect {\n  run(): void;\n  zone: {\n    run<T>(fn: () => T): T;\n  } | null;\n  dirty: boolean;\n}\n\n/**\n * A scheduler which manages the execution of effects.\n */\nexport abstract class EffectScheduler {\n  abstract add(e: SchedulableEffect): void;\n\n  /**\n   * Schedule the given effect to be executed at a later time.\n   *\n   * It is an error to attempt to execute any effects synchronously during a scheduling operation.\n   */\n  abstract schedule(e: SchedulableEffect): void;\n\n  /**\n   * Run any scheduled effects.\n   */\n  abstract flush(): void;\n\n  /** Remove a scheduled effect */\n  abstract remove(e: SchedulableEffect): void;\n\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ ɵɵdefineInjectable({\n    token: EffectScheduler,\n    providedIn: 'root',\n    factory: () => new ZoneAwareEffectScheduler(),\n  });\n}\n\n/**\n * A wrapper around `ZoneAwareQueueingScheduler` that schedules flushing via the microtask queue\n * when.\n */\nexport class ZoneAwareEffectScheduler implements EffectScheduler {\n  private dirtyEffectCount = 0;\n  private queues = new Map<Zone | null, Set<SchedulableEffect>>();\n\n  add(handle: SchedulableEffect): void {\n    this.enqueue(handle);\n    this.schedule(handle);\n  }\n\n  schedule(handle: SchedulableEffect): void {\n    if (!handle.dirty) {\n      return;\n    }\n    this.dirtyEffectCount++;\n  }\n\n  remove(handle: SchedulableEffect): void {\n    const zone = handle.zone as Zone | null;\n    const queue = this.queues.get(zone)!;\n    if (!queue.has(handle)) {\n      return;\n    }\n\n    queue.delete(handle);\n    if (handle.dirty) {\n      this.dirtyEffectCount--;\n    }\n  }\n\n  private enqueue(handle: SchedulableEffect): void {\n    const zone = handle.zone as Zone | null;\n    if (!this.queues.has(zone)) {\n      this.queues.set(zone, new Set());\n    }\n\n    const queue = this.queues.get(zone)!;\n    if (queue.has(handle)) {\n      return;\n    }\n    queue.add(handle);\n  }\n\n  /**\n   * Run all scheduled effects.\n   *\n   * Execution order of effects within the same zone is guaranteed to be FIFO, but there is no\n   * ordering guarantee between effects scheduled in different zones.\n   */\n  flush(): void {\n    while (this.dirtyEffectCount > 0) {\n      let ranOneEffect = false;\n      for (const [zone, queue] of this.queues) {\n        // `zone` here must be defined.\n        if (zone === null) {\n          ranOneEffect ||= this.flushQueue(queue);\n        } else {\n          ranOneEffect ||= zone.run(() => this.flushQueue(queue));\n        }\n      }\n\n      // Safeguard against infinite looping if somehow our dirty effect count gets out of sync with\n      // the dirty flag across all the effects.\n      if (!ranOneEffect) {\n        this.dirtyEffectCount = 0;\n      }\n    }\n  }\n\n  private flushQueue(queue: Set<SchedulableEffect>): boolean {\n    let ranOneEffect = false;\n    for (const handle of queue) {\n      if (!handle.dirty) {\n        continue;\n      }\n      this.dirtyEffectCount--;\n      ranOneEffect = true;\n\n      // TODO: what happens if this throws an error?\n      handle.run();\n    }\n    return ranOneEffect;\n  }\n}\n"], "names": ["global", "INJECTOR", "inject", "isNotFound"], "mappings": ";;;;;;;;;;;;AAQA;;;;;;AAMG;AACI,MAAM,2BAA2B,GAAG,4BAA4B;AAEvE;;AAEG;AACI,MAAM,gBAAgB,GAC3B;;ACmIF;;;;;;;;;;;;;;;AAeG;AACG,MAAO,YAAkD,SAAQ,KAAK,CAAA;AAEjE,IAAA,IAAA;IADT,WACS,CAAA,IAAO,EACd,OAA8B,EAAA;QAE9B,KAAK,CAAC,kBAAkB,CAAI,IAAI,EAAE,OAAO,CAAC,CAAC;QAHpC,IAAI,CAAA,IAAA,GAAJ,IAAI;;AAKd;AAEK,SAAU,sBAAsB,CAAsC,IAAO,EAAA;;;;IAIjF,OAAO,CAAA,GAAA,EAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC/B;AAEA;;;AAGG;AACa,SAAA,kBAAkB,CAChC,IAAO,EACP,OAA8B,EAAA;AAE9B,IAAA,MAAM,QAAQ,GAAG,sBAAsB,CAAC,IAAI,CAAC;AAE7C,IAAA,IAAI,YAAY,GAAG,CAAA,EAAG,QAAQ,CAAA,EAAG,OAAO,GAAG,IAAI,GAAG,OAAO,GAAG,EAAE,EAAE;AAEhE,IAAA,IAAI,SAAS,IAAI,IAAI,GAAG,CAAC,EAAE;QACzB,MAAM,kBAAkB,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,YAAY,CAAC;QAC5D,MAAM,SAAS,GAAG,kBAAkB,GAAG,GAAG,GAAG,EAAE;QAC/C,YAAY,GAAG,CAAG,EAAA,YAAY,CAAG,EAAA,SAAS,iBAAiB,2BAA2B,CAAA,CAAA,EAAI,QAAQ,CAAA,CAAE;;AAEtG,IAAA,OAAO,YAAY;AACrB;;AClMM,MAAA,OAAO,GAAQ;;AC6BrB,SAAS,0BAA0B,GAAA;AACjC,IAAA,MAAM,cAAc,GAAG,OAAO,QAAQ,KAAK,WAAW,GAAG,QAAQ,CAAC,QAAQ,EAAE,GAAG,EAAE;AACjF,IAAA,MAAM,WAAW,GAA0B;AACzC,QAAA,aAAa,EAAE,CAAC;AAChB,QAAA,kBAAkB,EAAE,CAAC;AACrB,QAAA,sBAAsB,EAAE,CAAC;AACzB,QAAA,0BAA0B,EAAE,CAAC;AAC7B,QAAA,0BAA0B,EAAE,CAAC;AAC7B,QAAA,mCAAmC,EAAE,CAAC;KACvC;;IAGD,MAAM,kBAAkB,GAAG,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;IAC3E,IAAI,CAAC,kBAAkB,EAAE;AACvB,QAAAA,OAAM,CAAC,WAAW,CAAC,GAAG,KAAK;;SACtB;QACL,IAAI,OAAOA,OAAM,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;AAC3C,YAAAA,OAAM,CAAC,WAAW,CAAC,GAAG,EAAE;;QAE1B,MAAM,CAAC,MAAM,CAACA,OAAM,CAAC,WAAW,CAAC,EAAE,WAAW,CAAC;;AAEjD,IAAA,OAAO,WAAW;AACpB;AAEA;;;;;;;;;;;;;;;;;AAiBG;SACa,aAAa,GAAA;;;;;AAK3B,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACxE,YAAA,0BAA0B,EAAE;;QAE9B,OAAO,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,CAAC,SAAS;;AAExD,IAAA,OAAO,KAAK;AACd;;ACnFM,SAAU,sBAAsB,CAAI,wBAA2B,EAAA;AACnE,IAAA,KAAK,IAAI,GAAG,IAAI,wBAAwB,EAAE;AACxC,QAAA,IAAI,wBAAwB,CAAC,GAAG,CAAC,KAAM,sBAA8B,EAAE;AACrE,YAAA,OAAO,GAAG;;;;;AAKd,IAAA,MAAM,KAAK,CACT,OAAO,SAAS,KAAK,WAAW,IAAI;AAClC,UAAE;UACA,EAAE,CACP;AACH;AAEA;;;;;AAKG;AACa,SAAA,cAAc,CAAC,MAA+B,EAAE,MAA+B,EAAA;AAC7F,IAAA,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AACxB,QAAA,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;YAC7D,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;;;AAG/B;;AC3BM,SAAU,SAAS,CAAC,KAAU,EAAA;AAClC,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,QAAA,OAAO,KAAK;;AAGd,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,QAAA,OAAO,CAAI,CAAA,EAAA,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;;AAG/C,IAAA,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,EAAE,GAAG,KAAK;;IAGnB,MAAM,IAAI,GAAG,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,IAAI;IAC/C,IAAI,IAAI,EAAE;QACR,OAAO,CAAA,EAAG,IAAI,CAAA,CAAE;;AAGlB,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE;AAE/B,IAAA,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,OAAO,EAAE,GAAG,MAAM;;IAGpB,MAAM,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;AACzC,IAAA,OAAO,YAAY,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,MAAM;AACnE;AAEA;;;;;;;AAOG;AACa,SAAA,sBAAsB,CAAC,MAAqB,EAAE,KAAoB,EAAA;AAChF,IAAA,IAAI,CAAC,MAAM;QAAE,OAAO,KAAK,IAAI,EAAE;AAC/B,IAAA,IAAI,CAAC,KAAK;AAAE,QAAA,OAAO,MAAM;AACzB,IAAA,OAAO,CAAG,EAAA,MAAM,CAAI,CAAA,EAAA,KAAK,EAAE;AAC7B;AAEA;;;;;;AAMG;SACa,cAAc,CAAC,GAAW,EAAE,SAAS,GAAG,GAAG,EAAA;IACzD,IAAI,CAAC,GAAG,IAAI,SAAS,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,SAAS;AAAE,QAAA,OAAO,GAAG;IAChE,IAAI,SAAS,IAAI,CAAC;QAAE,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;IAEtD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;IAC3C,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,GAAG,SAAS,CAAC;AACpF;;ACtCA,MAAM,eAAe,GAAG,sBAAsB,CAAC,EAAC,eAAe,EAAE,sBAAsB,EAAC,CAAC;AAEzF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCG;AACG,SAAU,UAAU,CAAC,YAA0B,EAAA;AAC7C,IAAA,YAAa,CAAC,eAAe,GAAG,UAAU;IAC1C,YAAa,CAAC,QAAQ,GAAG,YAAA;AAC7B,QAAA,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC;AAC1B,KAAC;AACD,IAAA,OAAwB,YAAa;AACvC;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,iBAAiB,CAAI,IAAO,EAAA;AAC1C,IAAA,OAAO,YAAY,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,GAAG,IAAI;AAC3C;AAEA;AACM,SAAU,YAAY,CAAC,EAAO,EAAA;AAClC,IAAA,QACE,OAAO,EAAE,KAAK,UAAU;AACxB,QAAA,EAAE,CAAC,cAAc,CAAC,eAAe,CAAC;AAClC,QAAA,EAAE,CAAC,eAAe,KAAK,UAAU;AAErC;;AC5FA;AACA;AACA;AAMgB,SAAA,YAAY,CAAC,MAAW,EAAE,GAAW,EAAA;IACnD,IAAI,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,EAAE;QACjC,UAAU,CAAC,GAAG,EAAE,OAAO,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAEnD;SAEgB,mBAAmB,CACjC,MAAW,EACX,YAAoB,EACpB,YAAoB,EAAA;AAEpB,IAAA,YAAY,CAAC,MAAM,EAAE,mBAAmB,CAAC;AACzC,IAAA,qBAAqB,CAAC,MAAM,EAAE,YAAY,EAAE,6CAA6C,CAAC;AAC1F,IAAA,wBAAwB,CAAC,MAAM,EAAE,YAAY,EAAE,gDAAgD,CAAC;AAClG;AAEgB,SAAA,YAAY,CAAC,MAAW,EAAE,GAAW,EAAA;IACnD,IAAI,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,EAAE;QACjC,UAAU,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAE9E;AAEgB,SAAA,cAAc,CAAC,MAAW,EAAE,GAAW,EAAA;IACrD,IAAI,EAAE,OAAO,MAAM,KAAK,UAAU,CAAC,EAAE;QACnC,UAAU,CAAC,GAAG,EAAE,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO,MAAM,EAAE,UAAU,EAAE,KAAK,CAAC;;AAEhF;SAEgB,WAAW,CAAI,MAAS,EAAE,QAAW,EAAE,GAAW,EAAA;AAChE,IAAA,IAAI,EAAE,MAAM,IAAI,QAAQ,CAAC,EAAE;QACzB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;;AAE3C;SAEgB,cAAc,CAAI,MAAS,EAAE,QAAW,EAAE,GAAW,EAAA;AACnE,IAAA,IAAI,EAAE,MAAM,IAAI,QAAQ,CAAC,EAAE;QACzB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;;AAE3C;SAEgB,UAAU,CAAI,MAAS,EAAE,QAAW,EAAE,GAAW,EAAA;AAC/D,IAAA,IAAI,EAAE,MAAM,KAAK,QAAQ,CAAC,EAAE;QAC1B,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAE5C;SAEgB,aAAa,CAAI,MAAS,EAAE,QAAW,EAAE,GAAW,EAAA;AAClE,IAAA,IAAI,EAAE,MAAM,KAAK,QAAQ,CAAC,EAAE;QAC1B,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAE5C;SAEgB,cAAc,CAAI,MAAS,EAAE,QAAW,EAAE,GAAW,EAAA;AACnE,IAAA,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,EAAE;QACxB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC;;AAE1C;SAEgB,qBAAqB,CAAI,MAAS,EAAE,QAAW,EAAE,GAAW,EAAA;AAC1E,IAAA,IAAI,EAAE,MAAM,IAAI,QAAQ,CAAC,EAAE;QACzB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;;AAE3C;SAEgB,iBAAiB,CAAI,MAAS,EAAE,QAAW,EAAE,GAAW,EAAA;AACtE,IAAA,IAAI,EAAE,MAAM,GAAG,QAAQ,CAAC,EAAE;QACxB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAC;;AAE1C;SAEgB,wBAAwB,CACtC,MAAS,EACT,QAAW,EACX,GAAW,EAAA;AAEX,IAAA,IAAI,EAAE,MAAM,IAAI,QAAQ,CAAC,EAAE;QACzB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC;;AAE3C;AAEgB,SAAA,gBAAgB,CAAI,MAAS,EAAE,GAAW,EAAA;AACxD,IAAA,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;;AAEvC;AAEgB,SAAA,aAAa,CAAI,MAA4B,EAAE,GAAW,EAAA;AACxE,IAAA,IAAI,MAAM,IAAI,IAAI,EAAE;QAClB,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;;AAEvC;AAIM,SAAU,UAAU,CAAC,GAAW,EAAE,MAAY,EAAE,QAAc,EAAE,UAAmB,EAAA;AACvF,IAAA,MAAM,IAAI,KAAK,CACb,CAAA,iBAAA,EAAoB,GAAG,CAAE,CAAA;AACvB,SAAC,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,CAAgB,aAAA,EAAA,QAAQ,IAAI,UAAU,CAAA,CAAA,EAAI,MAAM,CAAY,UAAA,CAAA,CAAC,CAC3F;AACH;AAEM,SAAU,aAAa,CAAC,IAAS,EAAA;AACrC,IAAA,IAAI,EAAE,IAAI,YAAY,IAAI,CAAC,EAAE;QAC3B,UAAU,CAAC,gEAAgE,SAAS,CAAC,IAAI,CAAC,CAAA,CAAE,CAAC;;AAEjG;AAEM,SAAU,aAAa,CAAC,IAAS,EAAA;AACrC,IAAA,IAAI,EAAE,IAAI,YAAY,OAAO,CAAC,EAAE;QAC9B,UAAU,CAAC,iDAAiD,SAAS,CAAC,IAAI,CAAC,CAAA,CAAE,CAAC;;AAElF;AAEgB,SAAA,kBAAkB,CAAC,GAAU,EAAE,KAAa,EAAA;AAC1D,IAAA,aAAa,CAAC,GAAG,EAAE,wBAAwB,CAAC;AAC5C,IAAA,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM;IACzB,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,MAAM,EAAE;AAChC,QAAA,UAAU,CAAC,CAAkC,+BAAA,EAAA,MAAM,YAAY,KAAK,CAAA,CAAE,CAAC;;AAE3E;SAEgB,WAAW,CAAC,KAAU,EAAE,GAAG,WAAkB,EAAA;IAC3D,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAAE,QAAA,OAAO,IAAI;AAClD,IAAA,UAAU,CACR,CAA+B,4BAAA,EAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAY,SAAA,EAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA,CAAA,CAAG,CAC/F;AACH;AAEM,SAAU,iBAAiB,CAAC,EAAU,EAAA;AAC1C,IAAA,IAAI,iBAAiB,EAAE,KAAK,IAAI,EAAE;AAChC,QAAA,UAAU,CAAC,CAAA,EAAG,EAAE,CAAA,gDAAA,CAAkD,CAAC;;AAEvE;;ACAA;;;;;;;;;;;;;;;;;AAiBG;AACG,SAAU,kBAAkB,CAAI,IAIrC,EAAA;IACC,OAAO;QACL,KAAK,EAAE,IAAI,CAAC,KAAK;AACjB,QAAA,UAAU,EAAG,IAAI,CAAC,UAAkB,IAAI,IAAI;QAC5C,OAAO,EAAE,IAAI,CAAC,OAAO;AACrB,QAAA,KAAK,EAAE,SAAS;KACa;AACjC;AAEA;;;;AAIG;AACI,MAAM,gBAAgB,GAAG;AAEhC;;;;;;;;;;;;;;;;AAgBG;AACG,SAAU,gBAAgB,CAAC,OAA6C,EAAA;AAC5E,IAAA,OAAO,EAAC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE,EAAC;AAC7E;AAEA;;;;;AAKG;AACG,SAAU,gBAAgB,CAAI,IAAS,EAAA;AAC3C,IAAA,OAAO,gBAAgB,CAAC,IAAI,EAAE,WAAW,CAAC;AAC5C;AAEM,SAAU,YAAY,CAAC,IAAS,EAAA;AACpC,IAAA,OAAO,gBAAgB,CAAC,IAAI,CAAC,KAAK,IAAI;AACxC;AAEA;;;AAGG;AACH,SAAS,gBAAgB,CAAI,IAAS,EAAE,KAAa,EAAA;;AAEnD,IAAA,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI;AAC5D;AAEA;;;;;;;AAOG;AACG,SAAU,yBAAyB,CAAI,IAAS,EAAA;;IAEpD,MAAM,GAAG,GAAG,IAAI,GAAG,WAAW,CAAC,IAAI,IAAI;IAEvC,IAAI,GAAG,EAAE;QACP,SAAS;AACP,YAAA,OAAO,CAAC,IAAI,CACV,4CAA4C,IAAI,CAAC,IAAI,CAA8E,4EAAA,CAAA;AACjI,gBAAA,CAAA,2FAAA,EAA8F,IAAI,CAAC,IAAI,CAAA,QAAA,CAAU,CACpH;AACH,QAAA,OAAO,GAAG;;SACL;AACL,QAAA,OAAO,IAAI;;AAEf;AAEA;;;;AAIG;AACG,SAAU,cAAc,CAAI,IAAS,EAAA;AACzC,IAAA,OAAO,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAI,IAAY,CAAC,UAAU,CAAC,GAAG,IAAI;AACnF;AAEO,MAAM,WAAW,GAAW,sBAAsB,CAAC,EAAC,KAAK,EAAE,sBAAsB,EAAC;AAClF,MAAM,UAAU,GAAW,sBAAsB,CAAC,EAAC,IAAI,EAAE,sBAAsB,EAAC;;AC3PvF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CG;MACU,cAAc,CAAA;AAab,IAAA,KAAA;;IAXH,cAAc,GAAG,gBAAgB;AAEjC,IAAA,KAAK;AAEd;;;;;AAKG;IACH,WACY,CAAA,KAAa,EACvB,OAGC,EAAA;QAJS,IAAK,CAAA,KAAA,GAAL,KAAK;AAMf,QAAA,IAAI,CAAC,KAAK,GAAG,SAAS;AACtB,QAAA,IAAI,OAAO,OAAO,IAAI,QAAQ,EAAE;AAC9B,YAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,gBAAA,cAAc,CAAC,OAAO,EAAE,CAAC,EAAE,0CAA0C,CAAC;;;AAGvE,YAAA,IAAY,CAAC,iBAAiB,GAAG,OAAO;;AACpC,aAAA,IAAI,OAAO,KAAK,SAAS,EAAE;AAChC,YAAA,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC;AAC9B,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,MAAM;gBACxC,OAAO,EAAE,OAAO,CAAC,OAAO;AACzB,aAAA,CAAC;;;AAIN;;AAEG;AACH,IAAA,IAAI,KAAK,GAAA;AACP,QAAA,OAAO,IAAgC;;IAGzC,QAAQ,GAAA;AACN,QAAA,OAAO,CAAkB,eAAA,EAAA,IAAI,CAAC,KAAK,EAAE;;AAExC;;AC4ED,IAAI,wBAAiD;SACrC,0BAA0B,GAAA;AACxC,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,sEAAsE,CAAC;AAChG,IAAA,OAAO,wBAAwB;AACjC;AAEM,SAAU,0BAA0B,CAAC,OAAgC,EAAA;AACzE,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,sEAAsE,CAAC;IAEhG,MAAM,QAAQ,GAAG,wBAAwB;IACzC,wBAAwB,GAAG,OAAO;AAClC,IAAA,OAAO,QAAQ;AACjB;AAEA,MAAM,yBAAyB,GAAuB,EAAE;AAExD,MAAM,qBAAqB,GAAG,MAAK,GAAG;AAEtC,SAAS,cAAc,CAAC,QAA0B,EAAA;IAChD,MAAM,WAAW,GAAG,yBAAyB,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC/D,IAAA,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;AACtB,QAAA,yBAAyB,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;;AAEpD;AAEA;;;;;;;;;;;;AAYG;AACG,SAAU,mBAAmB,CAAC,gBAAyC,EAAA;AAC3E,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,+DAA+D,CAAC;AAEzF,IAAA,IAAI,gBAAgB,KAAK,IAAI,EAAE;QAC7B,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;AACzD,YAAA,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAElD,QAAA,OAAO,MAAM,cAAc,CAAC,gBAAgB,CAAC;;SACxC;AACL,QAAA,yBAAyB,CAAC,MAAM,GAAG,CAAC;AACpC,QAAA,OAAO,qBAAqB;;AAEhC;AAEA;;;;AAIG;AACG,SAAU,gBAAgB,CAAC,KAA4B,EAAA;AAC3D,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,6DAA6D,CAAC;AAEvF,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,yBAAyB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzD,QAAA,MAAM,wBAAwB,GAAG,yBAAyB,CAAC,CAAC,CAAC;QAC7D,wBAAwB,CAAC,KAAK,CAAC;;AAEnC;AAEA;;;;;AAKG;SACa,2BAA2B,CACzC,aAA6B,EAC7B,iBAA0B,KAAK,EAAA;AAE/B,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,6DAA6D,CAAC;AAEvF,IAAA,IAAI,KAAK;;;AAGT,IAAA,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;QACvC,KAAK,GAAG,aAAa;;;AAGlB,SAAA,IAAI,aAAa,YAAY,cAAc,EAAE;QAChD,KAAK,GAAG,aAAa;;;SAGlB;AACH,QAAA,KAAK,GAAG,iBAAiB,CAAC,aAAa,CAAC,OAAO,CAAC;;IAGlD,IAAI,QAAQ,GAAG,aAAa;;;;AAI5B,IAAA,IAAI,aAAa,YAAY,cAAc,EAAE;AAC3C,QAAA,QAAQ,GAAI,aAAa,CAAC,KAAyB,IAAI,aAAa;;AAGtE,IAAA,gBAAgB,CAAC;AACf,QAAA,IAAI,EAA8C,CAAA;QAClD,OAAO,EAAE,0BAA0B,EAAE;AACrC,QAAA,cAAc,EAAE,EAAC,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAC;AAClD,KAAA,CAAC;AACJ;AAEA;;;;;AAKG;AACG,SAAU,iCAAiC,CAAC,KAA6B,EAAA;AAC7E,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,6DAA6D,CAAC;AAEvF,IAAA,gBAAgB,CAAC;AACf,QAAA,IAAI,EAAyD,CAAA;QAC7D,OAAO,EAAE,0BAA0B,EAAE;AACrC,QAAA,KAAK,EAAE,KAAK;AACb,KAAA,CAAC;AACJ;AAEA;;;;;AAKG;AACG,SAAU,kCAAkC,CAAC,QAAiB,EAAA;AAClE,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,6DAA6D,CAAC;AAEvF,IAAA,gBAAgB,CAAC;AACf,QAAA,IAAI,EAAqD,CAAA;QACzD,OAAO,EAAE,0BAA0B,EAAE;AACrC,QAAA,QAAQ,EAAE,EAAC,KAAK,EAAE,QAAQ,EAAC;AAC5B,KAAA,CAAC;AACJ;AAEA;;;;AAIG;SACa,eAAe,CAC7B,KAAoB,EACpB,KAAc,EACd,KAA0B,EAAA;AAE1B,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,6DAA6D,CAAC;AAEvF,IAAA,gBAAgB,CAAC;AACf,QAAA,IAAI,EAAkC,CAAA;QACtC,OAAO,EAAE,0BAA0B,EAAE;AACrC,QAAA,OAAO,EAAE,EAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAC;AAC/B,KAAA,CAAC;AACJ;AAEM,SAAU,sBAAsB,CAAC,MAAiB,EAAA;AACtD,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,6DAA6D,CAAC;AAEvF,IAAA,gBAAgB,CAAC;AACf,QAAA,IAAI,EAAyC,CAAA;QAC7C,OAAO,EAAE,0BAA0B,EAAE;QACrC,MAAM;AACP,KAAA,CAAC;AACJ;SAEgB,4BAA4B,CAC1C,QAAkB,EAClB,KAAoB,EACpB,QAAoB,EAAA;AAEpB,IAAA,CAAC,SAAS;QACR,UAAU,CAAC,wEAAwE,CAAC;IAEtF,MAAM,iBAAiB,GAAG,0BAA0B,CAAC,EAAC,QAAQ,EAAE,KAAK,EAAC,CAAC;AACvE,IAAA,IAAI;AACF,QAAA,QAAQ,EAAE;;YACF;QACR,0BAA0B,CAAC,iBAAiB,CAAC;;AAEjD;;ACSM,SAAU,sBAAsB,CACpC,KAAqE,EAAA;AAErE,IAAA,OAAO,KAAK,IAAI,CAAC,CAAE,KAAsC,CAAC,UAAU;AACtE;;AChXO,MAAM,WAAW,GAAW,sBAAsB,CAAC,EAAC,IAAI,EAAE,sBAAsB,EAAC;AACjF,MAAM,UAAU,GAAW,sBAAsB,CAAC,EAAC,IAAI,EAAE,sBAAsB,EAAC;AAChF,MAAM,WAAW,GAAW,sBAAsB,CAAC,EAAC,KAAK,EAAE,sBAAsB,EAAC;AAClF,MAAM,UAAU,GAAW,sBAAsB,CAAC,EAAC,IAAI,EAAE,sBAAsB,EAAC;AAChF,MAAM,cAAc,GAAW,sBAAsB,CAAC,EAAC,IAAI,EAAE,sBAAsB,EAAC;AAE3F;;;;AAIG;AACH;AACO,MAAM,aAAa,GAAW,sBAAsB,CAAC;AAC1D,IAAA,iBAAiB,EAAE,sBAAsB;AAC1C,CAAA;AAED;;;;;;;AAOG;AACI,MAAM,SAAS,GAAW,sBAAsB,CAAC,EAAC,aAAa,EAAE,sBAAsB,EAAC,CAAC;;ACvBhG;;;;;AAKG;AACG,SAAU,eAAe,CAAC,KAAU,EAAA;IACxC,IAAI,OAAO,KAAK,KAAK,QAAQ;AAAE,QAAA,OAAO,KAAK;IAC3C,IAAI,KAAK,IAAI,IAAI;AAAE,QAAA,OAAO,EAAE;;;AAG5B,IAAA,OAAO,MAAM,CAAC,KAAK,CAAC;AACtB;AAEA;;;;;AAKG;AACG,SAAU,iBAAiB,CAAC,KAAU,EAAA;IAC1C,IAAI,OAAO,KAAK,KAAK,UAAU;QAAE,OAAO,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACtE,IAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AAClF,QAAA,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;;AAGjD,IAAA,OAAO,eAAe,CAAC,KAAK,CAAC;AAC/B;AAEA;;;;;AAKG;AACG,SAAU,0BAA0B,CAAC,IAAe,EAAA;;;IAGxD,IAAI,YAAY,GAAI,IAAY,CAAC,WAAW,CAAC,IAAI,IAAI;IACrD,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,SAAS,EAAE;AACnD,QAAA,OAAO,0BAA0B,CAAC,YAAY,CAAC,SAAS,CAAC;;AAG3D,IAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC;AAChC;AAEA;AACA;AACA,SAAS,0BAA0B,CAAC,SAAc,EAAA;IAChD,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;QAChD,OAAO,SAAS,CAAC,SAAS;;SACrB;AACL,QAAA,OAAO,CAAG,EAAA,SAAS,CAAC,SAAS,CAAQ,KAAA,EAAA,SAAS,CAAC,QAAQ,CAAI,CAAA,EAAA,SAAS,CAAC,UAAU,GAAG;;AAEtF;;ACjDA;AACgB,SAAA,0BAA0B,CAAC,KAAa,EAAE,IAAe,EAAA;IACvE,MAAM,IAAI,YAAY,CAAA,CAAA,GAAA,8CAEpB;UACI,0CAA0C,KAAK,CAAA,EAAG,IAAI,GAAG,CAAA,mBAAA,EAAsB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,KAAK,EAAE,GAAG,EAAE,CAAE;UACnH,KAAK,CACV;AACH;SAEgB,4BAA4B,GAAA;AAC1C,IAAA,MAAM,IAAI,KAAK,CAAC,CAAA,gDAAA,CAAkD,CAAC;AACrE;SAEgB,yBAAyB,CACvC,YAA4B,EAC5B,SAAiB,EACjB,QAAc,EAAA;AAEd,IAAA,IAAI,YAAY,IAAI,SAAS,EAAE;AAC7B,QAAA,MAAM,cAAc,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AAC3F,QAAA,MAAM,IAAI,KAAK,CACb,CAAsC,mCAAA,EAAA,SAAS,CAC7C,YAAY,CACb,CAA8D,2DAAA,EAAA,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG,CAC5F;;AACI,SAAA,IAAI,sBAAsB,CAAC,QAAQ,CAAC,EAAE;AAC3C,QAAA,IAAI,QAAQ,CAAC,aAAa,EAAE;AAC1B,YAAA,MAAM,IAAI,YAAY,CAEpB,GAAA,mDAAA,CAAA,gJAAA,CAAkJ,CACnJ;;aACI;AACL,YAAA,MAAM,IAAI,YAAY,CAEpB,GAAA,mDAAA,CAAA,sHAAA,CAAwH,CACzH;;;SAEE;AACL,QAAA,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;;AAEvC;AAEA;AACgB,SAAA,0BAA0B,CACxC,KAA6B,EAC7B,YAAqB,EAAA;IAErB,MAAM,YAAY,GAChB,SAAS;AACT,QAAA,CAAA,gBAAA,EAAmB,iBAAiB,CAAC,KAAK,CAAC,CAAA,MAAA,EAAS,YAAY,GAAG,CAAA,IAAA,EAAO,YAAY,CAAE,CAAA,GAAG,EAAE,EAAE;AACjG,IAAA,MAAM,IAAI,YAAY,CAAsC,CAAA,GAAA,4CAAA,YAAY,CAAC;AAC3E;;ACrDA;;;;;;;;AAQG;AACH,IAAI,qBAES;SACG,uBAAuB,GAAA;AACrC,IAAA,OAAO,qBAAqB;AAC9B;AAEA;;AAEG;AACG,SAAU,uBAAuB,CACrC,IAAyF,EAAA;IAEzF,MAAM,QAAQ,GAAG,qBAAqB;IACtC,qBAAqB,GAAG,IAAI;AAC5B,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;;;AAMG;SACa,kBAAkB,CAChC,KAAuB,EACvB,aAA4B,EAC5B,KAA0B,EAAA;AAE1B,IAAA,MAAM,aAAa,GAAsC,gBAAgB,CAAC,KAAK,CAAC;IAChF,IAAI,aAAa,IAAI,aAAa,CAAC,UAAU,IAAI,MAAM,EAAE;AACvD,QAAA,OAAO,aAAa,CAAC,KAAK,KAAK;eAC1B,aAAa,CAAC,KAAK,GAAG,aAAa,CAAC,OAAO,EAAE;AAChD,cAAE,aAAa,CAAC,KAAK;;AAEzB,IAAA,IAAI,KAAK,GAA+B,CAAA;AAAE,QAAA,OAAO,IAAI;IACrD,IAAI,aAAa,KAAK,SAAS;AAAE,QAAA,OAAO,aAAa;AACrD,IAAA,0BAA0B,CAAC,KAAK,EAAE,UAAU,CAAC;AAC/C;AAEA;;;;;;AAMG;AACG,SAAU,kCAAkC,CAChD,EAAyE,EAAA;IAEzE,SAAS;AACP,QAAA,cAAc,CAAC,qBAAqB,EAAE,EAAE,EAAE,iDAAiD,CAAC;AAChG;;AC9CA,MAAM,mBAAmB,GAAG,EAAE;AACvB,MAAM,kBAAkB,GAAG,mBAAmB;AAIrD;;;;AAIG;AACH,MAAM,iBAAiB,GAAG,gBAAgB;AAE1C;;;;AAIG;MACU,kBAAkB,CAAA;AACR,IAAA,QAAA;AAArB,IAAA,WAAA,CAAqB,QAAkB,EAAA;QAAlB,IAAQ,CAAA,QAAA,GAAR,QAAQ;;IAC7B,QAAQ,CAAI,KAAkC,EAAE,OAAgB,EAAA;AAC9D,QAAA,MAAM,KAAK,GACT,iBAAiB,CAAC,OAAoC,CAAC;AACzD,QAAA,IAAI;AACF,YAAA,OAAQ,IAAI,CAAC,QAAwC,CAAC,GAAG,CACvD,KAAqC;;AAErC,aAAC,KAAK,GAAA,CAAA,sCAAkC,IAAI,GAAG,kBAAkB,GACjE,KAAK,CACD;;QACN,OAAO,CAAM,EAAE;AACf,YAAA,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;AACjB,gBAAA,OAAO,CAAC;;AAEV,YAAA,MAAM,CAAC;;;AAGZ;AAEM,MAAM,kBAAkB,GAAG,iBAAiB;AACnD,MAAM,aAAa,GAAG,aAAa;AACnC,MAAM,QAAQ,GAAG,MAAM;AACvB,MAAM,WAAW,GAAG,GAAG;AAChB,MAAM,MAAM,GAAG,UAAU;SAmBhB,kBAAkB,CAChC,KAAuB,EACvB,KAAK,GAA8B,CAAA,oCAAA;AAEnC,IAAA,MAAM,eAAe,GAAG,kBAAkB,EAAE;AAC5C,IAAA,IAAI,eAAe,KAAK,SAAS,EAAE;QACjC,MAAM,IAAI,YAAY,CAAA,CAAA,GAAA,mDAEpB,SAAS;AACP,YAAA,CAAA,MAAA,EAAS,SAAS,CAAC,KAAK,CAAC,CAAA,4MAAA,CAA8M,CAC1O;;AACI,SAAA,IAAI,eAAe,KAAK,IAAI,EAAE;QACnC,OAAO,kBAAkB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC;;SAC7C;AACL,QAAA,MAAM,OAAO,GAAG,sBAAsB,CAAC,KAAK,CAAC;QAC7C,MAAM,KAAK,GAAG,eAAe,CAAC,QAAQ,CAAC,KAAoC,EAAE,OAAO,CAAM;QAC1F,SAAS,IAAI,eAAe,CAAC,KAAsB,EAAE,KAAK,EAAE,KAAK,CAAC;AAClE,QAAA,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AACrB,YAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;AACpB,gBAAA,OAAO,IAAI;;AAEb,YAAA,MAAM,KAAK;;AAEb,QAAA,OAAO,KAAK;;AAEhB;SAoBgB,QAAQ,CACtB,KAA4C,EAC5C,KAAK,GAA8B,CAAA,oCAAA;AAEnC,IAAA,OAAO,CAAC,uBAAuB,EAAE,IAAI,kBAAkB,EACrD,iBAAiB,CAAC,KAAgB,CAAC,EACnC,KAAK,CACN;AACH;AAEA;;;;;;;;AAQG;AACG,SAAU,mBAAmB,CAAC,KAAa,EAAA;IAC/C,MAAM,IAAI,YAAY,CAAA,GAAA,oDAEpB,SAAS;AACP,QAAA,CAAA,qGAAA,EAAwG,KAAK,CAAA;;;2DAGxD,KAAK,CAAA,+FAAA,CAAiG,CAC9J;AACH;AA0DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgEG;AACa,SAAA,MAAM,CAAI,KAA4C,EAAE,OAAuB,EAAA;;;IAG7F,OAAO,QAAQ,CAAC,KAAY,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC3D;AAEA;AACM,SAAU,iBAAiB,CAC/B,KAAsD,EAAA;IAEtD,IAAI,OAAO,KAAK,KAAK,WAAW,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7D,QAAA,OAAO,KAAK;;;;;IAMd,QAAQ;AACL,SAAC,KAAK,CAAC,QAAQ,IAAA,CAAA,oCAA4C;AAC3D,SAAC,KAAK,CAAC,IAAI,IAAA,CAAA,gCAAwC;AACnD,SAAC,KAAK,CAAC,IAAI,IAAA,CAAA,gCAAwC;AACnD,SAAC,KAAK,CAAC,QAAQ,IAAgC,CAAA,oCAAY;AAChE;AAEA;AACA,SAAS,sBAAsB,CAAC,KAA0B,EAAA;IACxD,OAAO;AACL,QAAA,QAAQ,EAAE,CAAC,EAAE,KAAK,wCAAgC;AAClD,QAAA,IAAI,EAAE,CAAC,EAAE,KAAK,oCAA4B;AAC1C,QAAA,IAAI,EAAE,CAAC,EAAE,KAAK,oCAA4B;AAC1C,QAAA,QAAQ,EAAE,CAAC,EAAE,KAAK,wCAAgC;KACnD;AACH;AAEM,SAAU,UAAU,CAAC,KAAqC,EAAA;IAC9D,MAAM,IAAI,GAAU,EAAE;AACtB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,MAAM,GAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACtB,YAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;AACpB,gBAAA,MAAM,IAAI,YAAY,CAAA,GAAA,8CAEpB,SAAS,IAAI,sCAAsC,CACpD;;YAEH,IAAI,IAAI,GAA0B,SAAS;YAC3C,IAAI,KAAK;AAET,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnC,gBAAA,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACnB,gBAAA,MAAM,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;AAChC,gBAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;oBAE5B,IAAI,IAAI,KAA0B,CAAA,CAAA,8BAAE;AAClC,wBAAA,IAAI,GAAG,IAAI,CAAC,KAAK;;yBACZ;wBACL,KAAK,IAAI,IAAI;;;qBAEV;oBACL,IAAI,GAAG,IAAI;;;YAIf,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAK,EAAE,KAAK,CAAC,CAAC;;aAC5B;YACL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;;;AAG5B,IAAA,OAAO,IAAI;AACb;AAEA;;;;;;;;;AASG;AACa,SAAA,gBAAgB,CAAC,SAAc,EAAE,IAA0C,EAAA;AACzF,IAAA,SAAS,CAAC,iBAAiB,CAAC,GAAG,IAAI;AACnC,IAAA,SAAS,CAAC,SAAS,CAAC,iBAAiB,CAAC,GAAG,IAAI;AAC7C,IAAA,OAAO,SAAS;AAClB;AAEA;;;;AAIG;AACG,SAAU,aAAa,CAAC,KAAU,EAAA;AACtC,IAAA,OAAO,KAAK,CAAC,iBAAiB,CAAC;AACjC;AAEM,SAAU,kBAAkB,CAChC,CAAM,EACN,KAAU,EACV,iBAAyB,EACzB,MAAqB,EAAA;AAErB,IAAA,MAAM,SAAS,GAAU,CAAC,CAAC,kBAAkB,CAAC;AAC9C,IAAA,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACjB,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;;AAElC,IAAA,CAAC,CAAC,OAAO,GAAG,WAAW,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,CAAC;AAC/E,IAAA,CAAC,CAAC,aAAa,CAAC,GAAG,SAAS;AAC5B,IAAA,CAAC,CAAC,kBAAkB,CAAC,GAAG,IAAI;AAC5B,IAAA,MAAM,CAAC;AACT;AAEM,SAAU,WAAW,CACzB,IAAY,EACZ,GAAQ,EACR,iBAAyB,EACzB,MAAA,GAAwB,IAAI,EAAA;AAE5B,IAAA,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;AAC9F,IAAA,IAAI,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC;AAC5B,IAAA,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;AACtB,QAAA,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;;AACpC,SAAA,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;QAClC,IAAI,KAAK,GAAa,EAAE;AACxB,QAAA,KAAK,IAAI,GAAG,IAAI,GAAG,EAAE;AACnB,YAAA,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;AAC3B,gBAAA,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;AACpB,gBAAA,KAAK,CAAC,IAAI,CACR,GAAG,GAAG,GAAG,IAAI,OAAO,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CACnF;;;QAGL,OAAO,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAA,CAAG;;AAEnC,IAAA,OAAO,CAAG,EAAA,iBAAiB,CAAG,EAAA,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,EAAE,CAAI,CAAA,EAAA,OAAO,CAAM,GAAA,EAAA,IAAI,CAAC,OAAO,CACzF,QAAQ,EACR,MAAM,CACP,CAAA,CAAE;AACL;;AC5YgB,SAAA,aAAa,CAAI,IAAS,EAAE,aAAuB,EAAA;IACjE,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC;IACzD,IAAI,CAAC,aAAa,IAAI,aAAa,KAAK,IAAI,IAAI,SAAS,EAAE;QACzD,MAAM,IAAI,KAAK,CAAC,CAAQ,KAAA,EAAA,SAAS,CAAC,IAAI,CAAC,CAAiC,+BAAA,CAAA,CAAC;;AAE3E,IAAA,OAAO,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,IAAI;AACpD;;AC1BA;;;;;;;AAOG;SACa,WAAW,CAAI,CAAM,EAAE,CAAM,EAAE,gBAAwC,EAAA;AACrF,IAAA,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;AAAE,QAAA,OAAO,KAAK;AACvC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjC,QAAA,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACjB,QAAA,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;QACjB,IAAI,gBAAgB,EAAE;AACpB,YAAA,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAQ;AACxC,YAAA,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAQ;;AAE1C,QAAA,IAAI,MAAM,KAAK,MAAM,EAAE;AACrB,YAAA,OAAO,KAAK;;;AAGhB,IAAA,OAAO,IAAI;AACb;AAEA;;AAEG;AACG,SAAU,OAAO,CAAC,IAAW,EAAA;IACjC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC;AAC5C;AAEgB,SAAA,WAAW,CAAI,KAAoB,EAAE,EAAsB,EAAA;AACzE,IAAA,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,MAAM,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACvF;SAEgB,UAAU,CAAC,GAAU,EAAE,KAAa,EAAE,KAAU,EAAA;;AAE9D,IAAA,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,EAAE;AACvB,QAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;;SACV;QACL,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC;;AAE/B;AAEgB,SAAA,eAAe,CAAC,GAAU,EAAE,KAAa,EAAA;;IAEvD,IAAI,KAAK,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3B,QAAA,OAAO,GAAG,CAAC,GAAG,EAAE;;SACX;QACL,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;AAElC;AAIgB,SAAA,QAAQ,CAAI,IAAY,EAAE,KAAS,EAAA;IACjD,MAAM,IAAI,GAAQ,EAAE;AACpB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,KAAM,CAAC;;AAEnB,IAAA,OAAO,IAAI;AACb;AAEA;;;;;;;;;;;;AAYG;SACa,WAAW,CAAC,KAAY,EAAE,KAAa,EAAE,KAAa,EAAA;AACpE,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK;AACnC,IAAA,OAAO,KAAK,GAAG,MAAM,EAAE;QACrB,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACnC,QAAA,KAAK,EAAE;;IAET,OAAO,KAAK,EAAE,EAAE;AACd,QAAA,KAAK,CAAC,GAAG,EAAE,CAAC;;AAEhB;AAwBA;;;;;;;;;;;AAWG;AACG,SAAU,YAAY,CAAC,KAAY,EAAE,KAAa,EAAE,MAAW,EAAE,MAAW,EAAA;IAChF,SAAS,IAAI,qBAAqB,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,EAAE,8BAA8B,CAAC;AACvF,IAAA,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM;AACtB,IAAA,IAAI,GAAG,IAAI,KAAK,EAAE;;AAEhB,QAAA,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;;AACrB,SAAA,IAAI,GAAG,KAAK,CAAC,EAAE;;QAEpB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAA,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM;;SACZ;AACL,QAAA,GAAG,EAAE;AACL,QAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,QAAA,OAAO,GAAG,GAAG,KAAK,EAAE;AAClB,YAAA,MAAM,WAAW,GAAG,GAAG,GAAG,CAAC;YAC3B,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC;AAC/B,YAAA,GAAG,EAAE;;AAEP,QAAA,KAAK,CAAC,KAAK,CAAC,GAAG,MAAM;AACrB,QAAA,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,MAAM;;AAE7B;AAmCA;;;;;;;AAOG;SACa,gBAAgB,CAC9B,aAA+B,EAC/B,GAAW,EACX,KAAQ,EAAA;IAER,IAAI,KAAK,GAAG,oBAAoB,CAAC,aAAa,EAAE,GAAG,CAAC;AACpD,IAAA,IAAI,KAAK,IAAI,CAAC,EAAE;;AAEd,QAAA,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK;;SAC3B;QACL,KAAK,GAAG,CAAC,KAAK;QACd,YAAY,CAAC,aAAa,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC;;AAEhD,IAAA,OAAO,KAAK;AACd;AAEA;;;;;;AAMG;AACa,SAAA,gBAAgB,CAAI,aAA+B,EAAE,GAAW,EAAA;IAC9E,MAAM,KAAK,GAAG,oBAAoB,CAAC,aAAa,EAAE,GAAG,CAAC;AACtD,IAAA,IAAI,KAAK,IAAI,CAAC,EAAE;;AAEd,QAAA,OAAO,aAAa,CAAC,KAAK,GAAG,CAAC,CAAM;;AAEtC,IAAA,OAAO,SAAS;AAClB;AAEA;;;;;;;;;AASG;AACa,SAAA,oBAAoB,CAAI,aAA+B,EAAE,GAAW,EAAA;IAClF,OAAO,mBAAmB,CAAC,aAAyB,EAAE,GAAG,EAAE,CAAC,CAAC;AAC/D;AAqBA;;;;;;;;;;;;;;;;AAgBG;AACH,SAAS,mBAAmB,CAAC,KAAe,EAAE,KAAa,EAAE,KAAa,EAAA;AACxE,IAAA,SAAS,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,oBAAoB,CAAC;IAC1E,IAAI,KAAK,GAAG,CAAC;AACb,IAAA,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK;AAC/B,IAAA,OAAO,GAAG,KAAK,KAAK,EAAE;AACpB,QAAA,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC;AACtC,QAAA,IAAI,KAAK,KAAK,OAAO,EAAE;YACrB,OAAO,MAAM,IAAI,KAAK;;AACjB,aAAA,IAAI,OAAO,GAAG,KAAK,EAAE;YAC1B,GAAG,GAAG,MAAM;;aACP;AACL,YAAA,KAAK,GAAG,MAAM,GAAG,CAAC,CAAC;;;AAGvB,IAAA,OAAO,EAAE,GAAG,IAAI,KAAK,CAAC;AACxB;;AC5RA;;;;;AAKG;AAEI,MAAM,SAAS,GAAU;AACzB,MAAM,WAAW,GAAU;AAElC;AACA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,aAAa,EAAE,EAAE;;;;AAItE,IAAA,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;;AAExB,IAAA,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;AAC5B;;ACjBA;;;;;;;;;;;;AAYG;AACU,MAAA,uBAAuB,GAAG,IAAI,cAAc,CACvD,SAAS,GAAG,yBAAyB,GAAG,EAAE;;ACZ5C;;;;;;;AAOG;AACI,MAAMC,UAAQ,GAAG,IAAI,cAAc,CACxC,SAAS,GAAG,UAAU,GAAG,EAAE;AAC3B;AACA;AACA,CAAA,CAAA;;ACZW,MAAA,kBAAkB,GAAG,IAAI,cAAc,CAClD,SAAS,GAAG,oBAAoB,GAAG,EAAE;;MCA1B,YAAY,CAAA;AACvB,IAAA,GAAG,CAAC,KAAU,EAAE,aAAA,GAAqB,kBAAkB,EAAA;AACrD,QAAA,IAAI,aAAa,KAAK,kBAAkB,EAAE;AACxC,YAAA,MAAM,KAAK,GAAG,IAAI,aAAa,CAAC,CAAA,mCAAA,EAAsC,SAAS,CAAC,KAAK,CAAC,CAAG,CAAA,CAAA,CAAC;AAC1F,YAAA,MAAM,KAAK;;AAEb,QAAA,OAAO,aAAa;;AAEvB;;ACNK,SAAU,cAAc,CAAI,IAAS,EAAA;AACzC,IAAA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;AACjC;AAEM,SAAU,qBAAqB,CAAI,IAAS,EAAA;AAChD,IAAA,MAAM,WAAW,GAAG,cAAc,CAAI,IAAI,CAAC;IAC3C,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,IAAI,YAAY,CAAA,GAAA,sDAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,YAAA,CAAA,KAAA,EAAQ,SAAS,CAAC,IAAI,CAAC,CAAA,+BAAA,CAAiC,CAC3D;;AAEH,IAAA,OAAO,WAAW;AACpB;AAEA;;;;AAIG;AAEG,SAAU,eAAe,CAAI,IAAS,EAAA;AAC1C,IAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;AAClC;AAEM,SAAU,sBAAsB,CAAI,IAAS,EAAA;AACjD,IAAA,MAAM,GAAG,GAAG,eAAe,CAAI,IAAI,CAAC;IACpC,IAAI,CAAC,GAAG,EAAE;QACR,MAAM,IAAI,YAAY,CAAA,GAAA,sDAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,YAAA,CAAA,KAAA,EAAQ,SAAS,CAAC,IAAI,CAAC,CAAA,+BAAA,CAAiC,CAC3D;;AAEH,IAAA,OAAO,GAAG;AACZ;AAEM,SAAU,eAAe,CAAI,IAAS,EAAA;AAC1C,IAAA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;AACjC;AAEM,SAAU,UAAU,CAAI,IAAS,EAAA;AACrC,IAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;AAClC;AAEA;;;;;;;AAOG;AACG,SAAU,YAAY,CAAC,IAAmB,EAAA;AAC9C,IAAA,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAC9E,IAAA,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,CAAC,UAAU;AACvC;;ACjCA;;;;;AAKG;AACG,SAAU,wBAAwB,CACtC,SAA8C,EAAA;IAE9C,OAAO;AACL,QAAA,UAAU,EAAE,SAAS;KACa;AACtC;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACG,SAAU,6BAA6B,CAAC,aAAyB,EAAA;AACrE,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,uBAAuB;AAChC,YAAA,KAAK,EAAE,IAAI;AACX,YAAA,QAAQ,EAAE,aAAa;AACxB,SAAA;AACF,KAAA,CAAC;AACJ;AAiBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCG;AACa,SAAA,mBAAmB,CAAC,GAAG,OAAgC,EAAA;IACrE,OAAO;AACL,QAAA,UAAU,EAAE,2BAA2B,CAAC,IAAI,EAAE,OAAO,CAAC;AACtD,QAAA,aAAa,EAAE,IAAI;KACY;AACnC;SAEgB,2BAA2B,CACzC,qBAA8B,EAC9B,GAAG,OAAgC,EAAA;IAEnC,MAAM,YAAY,GAAqB,EAAE;AACzC,IAAA,MAAM,KAAK,GAAG,IAAI,GAAG,EAAiB,CAAC;AACvC,IAAA,IAAI,0BAA4E;AAEhF,IAAA,MAAM,gBAAgB,GAA4B,CAAC,QAAQ,KAAI;AAC7D,QAAA,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7B,KAAC;AAED,IAAA,WAAW,CAAC,OAAO,EAAE,CAAC,MAAM,KAAI;QAC9B,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,qBAAqB,EAAE;AAC5E,YAAA,MAAM,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;AACtC,YAAA,IAAI,MAAM,EAAE,UAAU,EAAE;gBACtB,MAAM,IAAI,YAAY,CAAA,GAAA,0DAEpB,CAAgG,6FAAA,EAAA,iBAAiB,CAC/G,MAAM,CACP,CAAG,CAAA,CAAA,CACL;;;;QAKL,MAAM,cAAc,GAAG,MAA4D;QACnF,IAAI,gBAAgB,CAAC,cAAc,EAAE,gBAAgB,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE;YACjE,0BAA0B,KAAK,EAAE;AACjC,YAAA,0BAA0B,CAAC,IAAI,CAAC,cAAc,CAAC;;AAEnD,KAAC,CAAC;;AAEF,IAAA,IAAI,0BAA0B,KAAK,SAAS,EAAE;AAC5C,QAAA,iCAAiC,CAAC,0BAA0B,EAAE,gBAAgB,CAAC;;AAGjF,IAAA,OAAO,YAAY;AACrB;AAEA;;;AAGG;AACH,SAAS,iCAAiC,CACxC,kBAAwD,EACxD,OAAgC,EAAA;AAEhC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAClD,MAAM,EAAC,QAAQ,EAAE,SAAS,EAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC;AACnD,QAAA,mBAAmB,CACjB,SAA4D,EAC5D,CAAC,QAAQ,KAAI;YACX,SAAS,IAAI,gBAAgB,CAAC,QAAQ,EAAE,SAAS,IAAI,WAAW,EAAE,QAAQ,CAAC;AAC3E,YAAA,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAC7B,SAAC,CACF;;AAEL;AAcA;;;;;;;;AAQG;AACG,SAAU,gBAAgB,CAC9B,SAA6D,EAC7D,OAAgC,EAChC,OAAwB,EACxB,KAAyB,EAAA;AAEzB,IAAA,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;AACxC,IAAA,IAAI,CAAC,SAAS;AAAE,QAAA,OAAO,KAAK;;;IAI5B,IAAI,OAAO,GAAyB,IAAI;AAExC,IAAA,IAAI,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC;IACtC,MAAM,MAAM,GAAG,CAAC,MAAM,IAAI,eAAe,CAAC,SAAS,CAAC;AACpD,IAAA,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;;;;;;QAMtB,MAAM,QAAQ,GAA+B;AAC1C,aAAA,QAAqC;AACxC,QAAA,MAAM,GAAG,cAAc,CAAC,QAAQ,CAAC;QACjC,IAAI,MAAM,EAAE;YACV,OAAO,GAAG,QAAS;;aACd;;AAEL,YAAA,OAAO,KAAK;;;AAET,SAAA,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AACvC,QAAA,OAAO,KAAK;;SACP;QACL,OAAO,GAAG,SAA0B;;;AAItC,IAAA,IAAI,SAAS,IAAI,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AAChD,QAAA,MAAM,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;QAClC,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC;AACnC,QAAA,0BAA0B,CAAC,OAAO,EAAE,IAAI,CAAC;;;IAI3C,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;IAEtC,IAAI,MAAM,EAAE;QACV,IAAI,WAAW,EAAE;;AAEf,YAAA,OAAO,KAAK;;AAEd,QAAA,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AAElB,QAAA,IAAI,MAAM,CAAC,YAAY,EAAE;YACvB,MAAM,IAAI,GACR,OAAO,MAAM,CAAC,YAAY,KAAK,UAAU,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,MAAM,CAAC,YAAY;AACzF,YAAA,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;gBACtB,gBAAgB,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC;;;;SAG7C,IAAI,MAAM,EAAE;;QAEjB,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;;;AAG1C,YAAA,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;;AAElC,YAAA,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AAElB,YAAA,IAAI,wBAAsE;AAC1E,YAAA,IAAI;gBACF,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,QAAQ,KAAI;oBACvC,IAAI,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;wBACvD,wBAAwB,KAAK,EAAE;;;AAG/B,wBAAA,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC;;AAE3C,iBAAC,CAAC;;oBACM;;AAER,gBAAA,SAAS,IAAI,OAAO,CAAC,GAAG,EAAE;;;;;AAM5B,YAAA,IAAI,wBAAwB,KAAK,SAAS,EAAE;AAC1C,gBAAA,iCAAiC,CAAC,wBAAwB,EAAE,OAAO,CAAC;;;QAIxE,IAAI,CAAC,WAAW,EAAE;;;AAGhB,YAAA,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,KAAK,MAAM,IAAI,OAAQ,EAAE,CAAC;;;;;AAOhE,YAAA,OAAO,CAAC,EAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAC,EAAE,OAAO,CAAC;;AAG5E,YAAA,OAAO,CAAC,EAAC,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAC,EAAE,OAAO,CAAC;;YAG/E,OAAO,CACL,EAAC,OAAO,EAAE,uBAAuB,EAAE,QAAQ,EAAE,MAAMC,QAAM,CAAC,OAAQ,CAAC,EAAE,KAAK,EAAE,IAAI,EAAC,EACjF,OAAO,CACR;;;AAIH,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,SAAiE;AAC7F,QAAA,IAAI,YAAY,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;YACxC,MAAM,YAAY,GAAG,SAA8B;AACnD,YAAA,mBAAmB,CAAC,YAAY,EAAE,CAAC,QAAQ,KAAI;gBAC7C,SAAS,IAAI,gBAAgB,CAAC,QAA0B,EAAE,YAAY,EAAE,YAAY,CAAC;AACrF,gBAAA,OAAO,CAAC,QAAQ,EAAE,YAAY,CAAC;AACjC,aAAC,CAAC;;;SAEC;;AAEL,QAAA,OAAO,KAAK;;IAGd,QACE,OAAO,KAAK,SAAS,IAAK,SAA4C,CAAC,SAAS,KAAK,SAAS;AAElG;AAEA,SAAS,gBAAgB,CACvB,QAAwB,EACxB,SAA+D,EAC/D,aAA4B,EAAA;IAE5B,IACE,cAAc,CAAC,QAAQ,CAAC;QACxB,eAAe,CAAC,QAAQ,CAAC;QACzB,iBAAiB,CAAC,QAAQ,CAAC;AAC3B,QAAA,kBAAkB,CAAC,QAAQ,CAAC,EAC5B;QACA;;;AAIF,IAAA,MAAM,QAAQ,GAAG,iBAAiB,CAChC,QAAQ,KAAM,QAAgD,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,CAC7F;IACD,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,yBAAyB,CAAC,aAAa,EAAE,SAAS,EAAE,QAAQ,CAAC;;AAEjE;AAEA,SAAS,mBAAmB,CAC1B,SAAyD,EACzD,EAAsC,EAAA;AAEtC,IAAA,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;AAC9B,QAAA,IAAI,sBAAsB,CAAC,QAAQ,CAAC,EAAE;AACpC,YAAA,QAAQ,GAAG,QAAQ,CAAC,UAAU;;AAEhC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC3B,YAAA,mBAAmB,CAAC,QAAQ,EAAE,EAAE,CAAC;;aAC5B;YACL,EAAE,CAAC,QAAQ,CAAC;;;AAGlB;AAEO,MAAM,SAAS,GAAW,sBAAsB,CAAgB;AACrE,IAAA,OAAO,EAAE,MAAM;AACf,IAAA,QAAQ,EAAE,sBAAsB;AACjC,CAAA,CAAC;AAEI,SAAU,eAAe,CAAC,KAAqB,EAAA;AACnD,IAAA,OAAO,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,QAAQ,IAAI,SAAS,IAAI,KAAK;AACzE;AAEM,SAAU,kBAAkB,CAAC,KAAqB,EAAA;IACtD,OAAO,CAAC,EAAE,KAAK,IAAK,KAA0B,CAAC,WAAW,CAAC;AAC7D;AAEM,SAAU,iBAAiB,CAAC,KAAqB,EAAA;IACrD,OAAO,CAAC,EAAE,KAAK,IAAK,KAAyB,CAAC,UAAU,CAAC;AAC3D;AAEM,SAAU,cAAc,CAAC,KAAqB,EAAA;AAClD,IAAA,OAAO,OAAO,KAAK,KAAK,UAAU;AACpC;AAEM,SAAU,eAAe,CAAC,KAAqB,EAAA;AACnD,IAAA,OAAO,CAAC,CAAE,KAA6C,CAAC,QAAQ;AAClE;;AChaA;;;;AAIG;AACU,MAAA,cAAc,GAAG,IAAI,cAAc,CAC9C,SAAS,GAAG,qBAAqB,GAAG,EAAE;;ACmExC;;AAEG;AACH,MAAM,OAAO,GAAG,EAAE;AAElB;;;;;;AAMG;AACH,MAAM,QAAQ,GAAG,EAAE;AAEnB;;AAEG;AACH,IAAI,aAAa,GAAyB,SAAS;SAEnC,eAAe,GAAA;AAC7B,IAAA,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,QAAA,aAAa,GAAG,IAAI,YAAY,EAAE;;AAEpC,IAAA,OAAO,aAAa;AACtB;AAYA;;;;;AAKG;MACmB,mBAAmB,CAAA;AAyDxC;AAEK,MAAO,UAAW,SAAQ,mBAAmB,CAAA;AA2BtC,IAAA,MAAA;AACA,IAAA,MAAA;AACA,IAAA,MAAA;AA5BX;;;;AAIG;AACK,IAAA,OAAO,GAAG,IAAI,GAAG,EAA0C;AAEnE;;AAEG;AACK,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAAa;IAExC,eAAe,GAAsB,EAAE;AAE/C;;AAEG;AACH,IAAA,IAAa,SAAS,GAAA;QACpB,OAAO,IAAI,CAAC,UAAU;;IAEhB,UAAU,GAAG,KAAK;AAElB,IAAA,gBAAgB;AAExB,IAAA,WAAA,CACE,SAAiD,EACxC,MAAgB,EAChB,MAAqB,EACrB,MAA0B,EAAA;AAEnC,QAAA,KAAK,EAAE;QAJE,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAM,CAAA,MAAA,GAAN,MAAM;;AAIf,QAAA,qBAAqB,CAAC,SAA2D,EAAE,CAAC,QAAQ,KAC1F,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAC/B;;AAGD,QAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAACD,UAAQ,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;;AAGvD,QAAA,IAAI,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;AAC7B,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,UAAU,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;;;;QAKpE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAiC;QAC/E,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,EAAE;YACtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,KAAsB,CAAC;;QAGhD,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,WAAW,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;;IAG1F,QAAQ,CAAI,KAAkC,EAAE,OAAiB,EAAA;AAC/D,QAAA,MAAM,KAAK,GACT,iBAAiB,CAAC,OAAoC,CAAC;AACzD,QAAA,IAAI;AACF,YAAA,OAAQ,IAAoC,CAAC,GAAG,CAC9C,KAAqC;;YAErC,kBAAuB,EACvB,KAAK,CACN;;QACD,OAAO,CAAM,EAAE;AACf,YAAA,IAAIE,YAAU,CAAC,CAAC,CAAC,EAAE;AACjB,gBAAA,OAAO,CAAC;;AAEV,YAAA,MAAM,CAAC;;;AAIX;;;;;AAKG;IACM,OAAO,GAAA;QACd,kBAAkB,CAAC,IAAI,CAAC;;AAGxB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;AACtB,QAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAC5C,QAAA,IAAI;;AAEF,YAAA,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAC5C,OAAO,CAAC,WAAW,EAAE;;AAEvB,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe;;;AAG3C,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;AACzB,YAAA,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE;AACjC,gBAAA,IAAI,EAAE;;;gBAEA;;AAER,YAAA,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AACpB,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;AAC9B,YAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;YAC7B,iBAAiB,CAAC,YAAY,CAAC;;;AAI1B,IAAA,SAAS,CAAC,QAAoB,EAAA;QACrC,kBAAkB,CAAC,IAAI,CAAC;AACxB,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;QACnC,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC;;AAGpC,IAAA,YAAY,CAAU,EAAiB,EAAA;QAC9C,kBAAkB,CAAC,IAAI,CAAC;AAExB,QAAA,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,IAAI,CAAC;AACjD,QAAA,MAAM,4BAA4B,GAAG,uBAAuB,CAAC,SAAS,CAAC;AAEvE,QAAA,IAAI,iBAAsD;QAC1D,IAAI,SAAS,EAAE;AACb,YAAA,iBAAiB,GAAG,0BAA0B,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;;AAG/E,QAAA,IAAI;YACF,OAAO,EAAE,EAAE;;gBACH;YACR,kBAAkB,CAAC,gBAAgB,CAAC;YACpC,uBAAuB,CAAC,4BAA4B,CAAC;AACrD,YAAA,SAAS,IAAI,0BAA0B,CAAC,iBAAkB,CAAC;;;AAItD,IAAA,GAAG,CACV,KAAuB,EACvB,aAAqB,GAAA,kBAAkB,EACvC,OAAuB,EAAA;QAEvB,kBAAkB,CAAC,IAAI,CAAC;AAExB,QAAA,IAAI,KAAK,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE;AACnC,YAAA,OAAQ,KAAa,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC;;AAGxC,QAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,OAAO,CAAwB;;AAG/D,QAAA,IAAI,iBAA0C;QAC9C,IAAI,SAAS,EAAE;AACb,YAAA,iBAAiB,GAAG,0BAA0B,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAgB,EAAC,CAAC;;AAE3F,QAAA,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,IAAI,CAAC;AACjD,QAAA,MAAM,4BAA4B,GAAG,uBAAuB,CAAC,SAAS,CAAC;AACvE,QAAA,IAAI;;AAEF,YAAA,IAAI,EAAE,KAAK,GAA+B,CAAA,oCAAC,EAAE;;gBAE3C,IAAI,MAAM,GAAiC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AAClE,gBAAA,IAAI,MAAM,KAAK,SAAS,EAAE;;;oBAGxB,MAAM,GAAG,GAAG,qBAAqB,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC;oBACnE,IAAI,GAAG,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,EAAE;;;wBAIzC,IAAI,SAAS,EAAE;AACb,4BAAA,4BAA4B,CAAC,IAAI,EAAE,KAAgB,EAAE,MAAK;gCACxD,2BAA2B,CAAC,KAAqB,CAAC;AACpD,6BAAC,CAAC;;wBAGJ,MAAM,GAAG,UAAU,CAAC,iCAAiC,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;;yBACjE;wBACL,MAAM,GAAG,IAAI;;oBAEf,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;;;AAGjC,gBAAA,IAAI,MAAM,IAAI,IAAI,8BAA8B;oBAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;;;;AAMtC,YAAA,MAAM,YAAY,GAAG,EAAE,KAAK,GAAA,CAAA,gCAA4B,GAAG,IAAI,CAAC,MAAM,GAAG,eAAe,EAAE;;;YAG1F,aAAa;AACX,gBAAA,KAAK,GAA+B,CAAA,uCAAI,aAAa,KAAK;AACxD,sBAAE;sBACA,aAAa;YACnB,OAAO,YAAY,CAAC,GAAG,CAAC,KAAK,EAAE,aAAa,CAAC;;QAC7C,OAAO,CAAM,EAAE;AACf,YAAA,IAAIA,YAAU,CAAC,CAAC,CAAC,EAAE;;AAEjB,gBAAA,MAAM,IAAI,IAAW,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBACzE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC9B,IAAI,gBAAgB,EAAE;;AAEpB,oBAAA,MAAM,CAAC;;qBACF;;AAEL,oBAAA,OAAO,kBAAkB,CAAC,CAAC,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC;;;iBAEhE;AACL,gBAAA,MAAM,CAAC;;;gBAED;;YAER,uBAAuB,CAAC,4BAA4B,CAAC;YACrD,kBAAkB,CAAC,gBAAgB,CAAC;AACpC,YAAA,SAAS,IAAI,0BAA0B,CAAC,iBAAkB,CAAC;;;;IAK/D,2BAA2B,GAAA;AACzB,QAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAC5C,QAAA,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,IAAI,CAAC;AACjD,QAAA,MAAM,4BAA4B,GAAG,uBAAuB,CAAC,SAAS,CAAC;AACvE,QAAA,IAAI,iBAAsD;QAC1D,IAAI,SAAS,EAAE;AACb,YAAA,iBAAiB,GAAG,0BAA0B,CAAC,EAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;;AAG/E,QAAA,IAAI;AACF,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,WAAW,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC;YACjF,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC7C,MAAM,IAAI,YAAY,CAAA,CAAA,GAAA,gDAEpB,+DAA+D;oBAC7D,CAA+B,4BAAA,EAAA,OAAO,YAAY,CAAK,GAAA,CAAA;oBACvD,2EAA2E;AAC3E,oBAAA,yBAAyB,CAC5B;;AAEH,YAAA,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;AACtC,gBAAA,WAAW,EAAE;;;gBAEP;YACR,kBAAkB,CAAC,gBAAgB,CAAC;YACpC,uBAAuB,CAAC,4BAA4B,CAAC;AACrD,YAAA,SAAS,IAAI,0BAA0B,CAAC,iBAAkB,CAAC;YAC3D,iBAAiB,CAAC,YAAY,CAAC;;;IAI1B,QAAQ,GAAA;QACf,MAAM,MAAM,GAAa,EAAE;AAC3B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;QAC5B,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE;YAClC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;;QAE/B,OAAO,CAAA,WAAA,EAAc,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;;AAG3C;;AAEG;AACK,IAAA,eAAe,CAAC,QAAwB,EAAA;;;AAG9C,QAAA,QAAQ,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AACtC,QAAA,IAAI,KAAK,GAAQ,cAAc,CAAC,QAAQ;AACtC,cAAE;cACA,iBAAiB,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC;;AAGnD,QAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC;QACzC,IAAI,SAAS,EAAE;AACb,YAAA,4BAA4B,CAAC,IAAI,EAAE,KAAK,EAAE,MAAK;;;;AAI7C,gBAAA,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;oBAC7B,iCAAiC,CAAC,KAAK,CAAC;AACxC,oBAAA,kCAAkC,CAAC,QAAQ,CAAC,QAAQ,CAAC;;gBAGvD,2BAA2B,CAAC,QAAQ,CAAC;AACvC,aAAC,CAAC;;AAGJ,QAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,KAAK,KAAK,IAAI,EAAE;;;YAGxD,IAAI,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;YACzC,IAAI,WAAW,EAAE;;gBAEf,IAAI,SAAS,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE;AAChD,oBAAA,4BAA4B,EAAE;;;iBAE3B;gBACL,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;AAClD,gBAAA,WAAW,CAAC,OAAO,GAAG,MAAM,UAAU,CAAC,WAAY,CAAC,KAAM,CAAC;gBAC3D,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,CAAC;;YAEtC,KAAK,GAAG,QAAQ;AAChB,YAAA,WAAW,CAAC,KAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;;aAC5B;YACL,IAAI,SAAS,EAAE;gBACb,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;gBACxC,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,KAAK,SAAS,EAAE;AAC5C,oBAAA,4BAA4B,EAAE;;;;QAIpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;;IAGzB,OAAO,CAAI,KAAuB,EAAE,MAAiB,EAAA;AAC3D,QAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC;AAC5C,QAAA,IAAI;AACF,YAAA,IAAI,MAAM,CAAC,KAAK,KAAK,QAAQ,EAAE;AAC7B,gBAAA,0BAA0B,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;;AACvC,iBAAA,IAAI,MAAM,CAAC,KAAK,KAAK,OAAO,EAAE;AACnC,gBAAA,MAAM,CAAC,KAAK,GAAG,QAAQ;gBAEvB,IAAI,SAAS,EAAE;AACb,oBAAA,4BAA4B,CAAC,IAAI,EAAE,KAAgB,EAAE,MAAK;wBACxD,iCAAiC,CAAC,KAAK,CAAC;AACxC,wBAAA,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,OAAQ,EAAE;AAChC,wBAAA,kCAAkC,CAAC,MAAM,CAAC,KAAK,CAAC;AAClD,qBAAC,CAAC;;qBACG;AACL,oBAAA,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,OAAQ,EAAE;;;AAGpC,YAAA,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,KAAK,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAClF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;;YAE1C,OAAO,MAAM,CAAC,KAAU;;gBAChB;YACR,iBAAiB,CAAC,YAAY,CAAC;;;AAI3B,IAAA,oBAAoB,CAAC,GAAiC,EAAA;AAC5D,QAAA,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;AACnB,YAAA,OAAO,KAAK;;QAEd,MAAM,UAAU,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC;AACpD,QAAA,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAClC,YAAA,OAAO,UAAU,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC;;aACrD;YACL,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC;;;AAIxC,IAAA,eAAe,CAAC,QAAoB,EAAA;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC3D,QAAA,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;;;AAGjD;AAED,SAAS,iCAAiC,CAAC,KAAyB,EAAA;;AAElE,IAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAC7C,IAAA,MAAM,OAAO,GAAG,aAAa,KAAK,IAAI,GAAG,aAAa,CAAC,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC;AAErF,IAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,QAAA,OAAO,OAAO;;;;AAKhB,IAAA,IAAI,KAAK,YAAY,cAAc,EAAE;AACnC,QAAA,MAAM,IAAI,YAAY,CAEpB,GAAA,iDAAA,SAAS,IAAI,CAAA,MAAA,EAAS,SAAS,CAAC,KAAK,CAAC,CAAiC,+BAAA,CAAA,CACxE;;;AAIH,IAAA,IAAI,KAAK,YAAY,QAAQ,EAAE;AAC7B,QAAA,OAAO,+BAA+B,CAAC,KAAK,CAAC;;;AAI/C,IAAA,MAAM,IAAI,YAAY,CAAA,GAAA,iDAA2C,SAAS,IAAI,aAAa,CAAC;AAC9F;AAEA,SAAS,+BAA+B,CAAC,KAAe,EAAA;;AAEtD,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM;AAChC,IAAA,IAAI,WAAW,GAAG,CAAC,EAAE;QACnB,MAAM,IAAI,YAAY,CAAA,GAAA,iDAEpB,SAAS;AACP,YAAA,CAAA,iCAAA,EAAoC,SAAS,CAAC,KAAK,CAAC,CAAM,GAAA,EAAA,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CACvF,IAAI,CACL,CAAA,EAAA,CAAI,CACR;;;;;;;AAQH,IAAA,MAAM,sBAAsB,GAAG,yBAAyB,CAAC,KAAK,CAAC;AAC/D,IAAA,IAAI,sBAAsB,KAAK,IAAI,EAAE;QACnC,OAAO,MAAM,sBAAsB,CAAC,OAAO,CAAC,KAAkB,CAAC;;SAC1D;AACL,QAAA,OAAO,MAAM,IAAK,KAAmB,EAAE;;AAE3C;AAEA,SAAS,gBAAgB,CAAC,QAAwB,EAAA;AAChD,IAAA,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;QAC7B,OAAO,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC;;SAC1C;AACL,QAAA,MAAM,OAAO,GAA4B,iBAAiB,CAAC,QAAQ,CAAC;AACpE,QAAA,OAAO,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC;;AAEvC;AAEA;;;;AAIG;SACa,iBAAiB,CAC/B,QAAwB,EACxB,YAAgC,EAChC,SAAiB,EAAA;IAEjB,IAAI,OAAO,GAA4B,SAAS;AAChD,IAAA,IAAI,SAAS,IAAI,sBAAsB,CAAC,QAAQ,CAAC,EAAE;AACjD,QAAA,yBAAyB,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;;AAG3D,IAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE;AAC5B,QAAA,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,QAAQ,CAAC;QACrD,OAAO,aAAa,CAAC,iBAAiB,CAAC,IAAI,iCAAiC,CAAC,iBAAiB,CAAC;;SAC1F;AACL,QAAA,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;YAC7B,OAAO,GAAG,MAAM,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC;;AAC/C,aAAA,IAAI,iBAAiB,CAAC,QAAQ,CAAC,EAAE;AACtC,YAAA,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;;AAClE,aAAA,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE;AACvC,YAAA,OAAO,GAAG,MAAM,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;;aAC5D;AACL,YAAA,MAAM,QAAQ,GAAG,iBAAiB,CAChC,QAAQ;iBACJ,QAAgD,CAAC,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,CACnF;AACD,YAAA,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;AAC1B,gBAAA,yBAAyB,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,CAAC;;AAE9D,YAAA,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE;AACrB,gBAAA,OAAO,GAAG,MAAM,IAAI,QAAQ,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;;iBACrD;gBACL,OAAO,aAAa,CAAC,QAAQ,CAAC,IAAI,iCAAiC,CAAC,QAAQ,CAAC;;;;AAInF,IAAA,OAAO,OAAO;AAChB;AAEM,SAAU,kBAAkB,CAAC,QAAoB,EAAA;AACrD,IAAA,IAAI,QAAQ,CAAC,SAAS,EAAE;AACtB,QAAA,MAAM,IAAI,YAAY,CAAA,GAAA,oDAEpB,SAAS,IAAI,sCAAsC,CACpD;;AAEL;AAEA,SAAS,UAAU,CACjB,OAA8B,EAC9B,KAAa,EACb,QAAiB,KAAK,EAAA;IAEtB,OAAO;AACL,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK,GAAG,EAAE,GAAG,SAAS;KAC9B;AACH;AAEA,SAAS,OAAO,CACd,KAAgE,EAAA;AAEhE,IAAA,OAAO,CAAC,CAAE,KAAa,CAAC,IAAI;AAC9B;AAEA,SAAS,YAAY,CAAC,KAAU,EAAA;IAC9B,QACE,KAAK,KAAK,IAAI;QACd,OAAO,KAAK,KAAK,QAAQ;AACzB,QAAA,OAAQ,KAAmB,CAAC,WAAW,KAAK,UAAU;AAE1D;AAEA,SAAS,qBAAqB,CAAC,KAAU,EAAA;AACvC,IAAA,QACE,OAAO,KAAK,KAAK,UAAU;AAC3B,SAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,cAAc,KAAK,gBAAgB,CAAC;AAE5E;AAEA,SAAS,qBAAqB,CAC5B,SAAiD,EACjD,EAAsC,EAAA;AAEtC,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC3B,YAAA,qBAAqB,CAAC,QAAQ,EAAE,EAAE,CAAC;;AAC9B,aAAA,IAAI,QAAQ,IAAI,sBAAsB,CAAC,QAAQ,CAAC,EAAE;AACvD,YAAA,qBAAqB,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC;;aACzC;YACL,EAAE,CAAC,QAA0B,CAAC;;;AAGpC;;AC5qBA;;;;;;;;;;;;;AAaG;AACa,SAAA,qBAAqB,CAAU,QAAkB,EAAE,EAAiB,EAAA;AAClF,IAAA,IAAI,gBAAoC;AACxC,IAAA,IAAI,QAAQ,YAAY,UAAU,EAAE;QAClC,kBAAkB,CAAC,QAAQ,CAAC;QAC5B,gBAAgB,GAAG,QAAQ;;SACtB;AACL,QAAA,gBAAgB,GAAG,IAAI,kBAAkB,CAAC,QAAQ,CAAC;;AAGrD,IAAA,IAAI,2BAAoD;IACxD,IAAI,SAAS,EAAE;QACb,2BAA2B,GAAG,0BAA0B,CAAC,EAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC;;AAEnF,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,gBAAgB,CAAC;AACzD,IAAA,MAAM,4BAA4B,GAAG,uBAAuB,CAAC,SAAS,CAAC;AACvE,IAAA,IAAI;QACF,OAAO,EAAE,EAAE;;YACH;QACR,kBAAkB,CAAC,YAAY,CAAC;AAChC,QAAA,SAAS,IAAI,0BAA0B,CAAC,2BAA4B,CAAC;QACrE,uBAAuB,CAAC,4BAA4B,CAAC;;AAEzD;AAEA;;AAEG;SACa,oBAAoB,GAAA;IAClC,OAAO,uBAAuB,EAAE,KAAK,SAAS,IAAI,kBAAkB,EAAE,IAAI,IAAI;AAChF;AACA;;;;;;;AAOG;AACG,SAAU,wBAAwB,CAAC,OAAiB,EAAA;;;AAGxD,IAAA,IAAI,CAAC,oBAAoB,EAAE,EAAE;QAC3B,MAAM,IAAI,YAAY,CAAA,CAAA,GAAA,mDAEpB,SAAS;AACP,YAAA,OAAO,CAAC,IAAI;AACV,gBAAA,iKAAiK,CACtK;;AAEL;;AC9CA;AACA;AACA;AACO,MAAM,IAAI,GAAG;AACb,MAAM,KAAK,GAAG;AAErB;AACO,MAAM,KAAK,GAAG;AACd,MAAM,MAAM,GAAG;AACf,MAAM,IAAI,GAAG;AACb,MAAM,MAAM,GAAG;AACtB;AAEO,MAAM,SAAS,GAAG;AAClB,MAAM,OAAO,GAAG;AAChB,MAAM,OAAO,GAAG;AAChB,MAAM,QAAQ,GAAG;AACjB,MAAM,WAAW,GAAG;AACpB,MAAM,QAAQ,GAAG;AACjB,MAAM,UAAU,GAAG;AACnB,MAAM,UAAU,GAAG;AAC1B;AACO,MAAM,gBAAgB,GAAG;AACzB,MAAM,0BAA0B,GAAG;AACnC,MAAM,sBAAsB,GAAG;AAC/B,MAAM,mBAAmB,GAAG;AAC5B,MAAM,OAAO,GAAG;AAChB,MAAM,EAAE,GAAG;AACX,MAAM,sBAAsB,GAAG;AAC/B,MAAM,gBAAgB,GAAG;AACzB,MAAM,mBAAmB,GAAG;AAC5B,MAAM,OAAO,GAAG;AAChB,MAAM,0BAA0B,GAAG;AACnC,MAAM,6BAA6B,GAAG;AAE7C;;;;;;AAMG;AACI,MAAM,aAAa,GAAG;;ACjE7B;;;;AAIG;AACI,MAAM,IAAI,GAAG,CAAC;AAErB;;;;AAIG;AAEH;AACA;AAEO,MAAM,gBAAgB,GAAG;AACzB,MAAM,MAAM,GAAG;AACf,MAAM,SAAS,GAAG;AAClB,MAAM,WAAW,GAAG;AAE3B;;;;;AAKG;AACI,MAAM,uBAAuB,GAAG;;AC3BvC;;;AAGG;AACG,SAAU,OAAO,CAAC,KAA6C,EAAA;AACnE,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,QAAQ;AAChE;AAEA;;;AAGG;AACG,SAAU,YAAY,CAAC,KAA6C,EAAA;AACxE,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,IAAI;AACrD;AAEM,SAAU,kBAAkB,CAAC,KAAY,EAAA;IAC7C,OAAO,CAAC,KAAK,CAAC,KAAK,2CAAmC,CAAC;AACzD;AAEM,SAAU,eAAe,CAAC,KAAY,EAAA;AAC1C,IAAA,OAAO,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC;AACnC;AAEM,SAAU,eAAe,CAAC,KAAY,EAAA;AAC1C,IAAA,OAAO,CAAC,KAAK,CAAC,KAAK,GAA6B,CAAA;AAClD;AAEM,SAAU,cAAc,CAAI,GAAoB,EAAA;AACpD,IAAA,OAAO,CAAC,CAAE,GAAuB,CAAC,QAAQ;AAC5C;AAEM,SAAU,UAAU,CAAC,MAAa,EAAA;;IAEtC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAoB,GAAA,8BAAM,CAAC;AAClD;AAEM,SAAU,iBAAiB,CAAC,KAAY,EAAA;AAC5C,IAAA,OAAO,CAAC,KAAK,CAAC,IAAI,GAAuB,EAAA;AAC3C;AAEM,SAAU,OAAO,CAAC,KAAY,EAAA;IAClC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAqB,EAAA;AAC3C;AAEM,SAAU,WAAW,CAAC,KAAY,EAAA;;IAEtC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAuB,GAAA;AAC7C;;ACjCA;AACA;AAEgB,SAAA,mBAAmB,CAAC,KAAY,EAAE,KAAY,EAAA;IAC5D,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1C;AAEgB,SAAA,mBAAmB,CAAC,KAAY,EAAE,KAAY,EAAA;IAC5D,WAAW,CAAC,KAAK,CAAC;AAClB,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI;AACxB,IAAA,KAAK,IAAI,CAAC,GAAG,aAAa,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,QAAA,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;YACtB;;;IAGJ,UAAU,CAAC,2CAA2C,CAAC;AACzD;AAEM,SAAU,WAAW,CAAC,KAAY,EAAA;AACtC,IAAA,aAAa,CAAC,KAAK,EAAE,uBAAuB,CAAC;AAC7C,IAAA,IAAI,EAAE,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,cAAc,CAAC,sBAAsB,CAAC,CAAC,EAAE;AACzF,QAAA,UAAU,CAAC,0BAA0B,GAAG,KAAK,CAAC;;AAElD;AAEM,SAAU,UAAU,CAAC,IAAU,EAAA;AACnC,IAAA,aAAa,CAAC,IAAI,EAAE,6BAA6B,CAAC;IAClD,IAAI,EAAE,OAAO,IAAI,CAAC,qBAAqB,KAAK,QAAQ,CAAC,EAAE;QACrD,UAAU,CAAC,6BAA6B,CAAC;;AAE7C;SAEgB,mBAAmB,CACjC,MAAW,EACX,MAAc,wEAAwE,EAAA;AAEtF,IAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;QAC5B,UAAU,CAAC,GAAG,CAAC;;AAEnB;SAEgB,kBAAkB,CAChC,MAAW,EACX,MAAc,uEAAuE,EAAA;AAErF,IAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;QAC3B,UAAU,CAAC,GAAG,CAAC;;AAEnB;AAMM,SAAU,eAAe,CAAC,KAAmB,EAAA;AACjD,IAAA,aAAa,CAAC,KAAK,EAAE,4BAA4B,CAAC;AAClD,IAAA,aAAa,CAAC,KAAM,CAAC,MAAM,EAAE,mCAAmC,CAAC;AACnE;AAEM,SAAU,gBAAgB,CAAC,KAAU,EAAA;AACzC,IAAA,aAAa,CAAC,KAAK,EAAE,4BAA4B,CAAC;IAClD,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,sBAAsB,CAAC;AAChE;AAEM,SAAU,sBAAsB,CAAC,KAAU,EAAA;AAC/C,IAAA,KAAK,IAAI,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,sCAAsC,CAAC;AACpF;AAEM,SAAU,WAAW,CAAC,KAAU,EAAA;AACpC,IAAA,aAAa,CAAC,KAAK,EAAE,uBAAuB,CAAC;IAC7C,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC;AACtD;AAEgB,SAAA,qBAAqB,CAAC,KAAY,EAAE,UAAmB,EAAA;IACrE,WAAW,CACT,KAAK,CAAC,eAAe,EACrB,IAAI,EACJ,UAAU,IAAI,6CAA6C,CAC5D;AACH;AAEgB,SAAA,qBAAqB,CAAC,KAAY,EAAE,UAAmB,EAAA;IACrE,WAAW,CACT,KAAK,CAAC,eAAe,EACrB,IAAI,EACU,6CAA6C,CAC5D;AACH;AAEA;;;AAGG;AACG,SAAU,kBAAkB,CAAI,GAAQ,EAAA;AAC5C,IAAA,IAAI,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,GAAG,CAAC,SAAS,IAAI,SAAS,IAAI,GAAG,CAAC,MAAM,KAAK,SAAS,EAAE;QACpF,UAAU,CACR,CAAgG,8FAAA,CAAA,CACjG;;AAEL;AAEgB,SAAA,sBAAsB,CAAC,KAAY,EAAE,KAAa,EAAA;IAChE,aAAa,CAAC,aAAa,EAAE,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC;AAC9D;AAEgB,SAAA,yBAAyB,CAAC,KAAY,EAAE,KAAa,EAAA;AACnE,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;IACtB,aAAa,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;AAC7D;SAEgB,aAAa,CAAC,KAAa,EAAE,KAAa,EAAE,KAAa,EAAA;IACvE,IAAI,EAAE,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC,EAAE;QACtC,UAAU,CAAC,iCAAiC,KAAK,CAAA,IAAA,EAAO,KAAK,CAAM,GAAA,EAAA,KAAK,CAAG,CAAA,CAAA,CAAC;;AAEhF;AAEgB,SAAA,qBAAqB,CAAC,KAAY,EAAE,UAAmB,EAAA;IACrE,aAAa,CAAC,KAAK,CAAC,0BAA0B,CAAC,EAAE,+BAA+B,CAAC;IACjF,aAAa,CACX,KAAK,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAE,CAAC,UAAU,EAEnD,qFAAqF,CACxF;AACH;AAEgB,SAAA,gBAAgB,CAAC,KAAmB,EAAE,UAAmB,EAAA;AACvE,IAAA,aAAa,CACX,KAAK,EACS,0EAA0E,CACzF;AACH;AAsBA;;;;;;AAMG;AACa,SAAA,kBAAkB,CAAC,KAAY,EAAE,aAAqB,EAAA;AACpE,IAAA,yBAAyB,CAAC,KAAK,EAAE,aAAa,CAAC;AAC/C,IAAA,yBAAyB,CAAC,KAAK,EAAE,aAAa,GAAA,CAAA,iCAA6B;IAC3E,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE,8CAA8C,CAAC;IACtF,YAAY,CACV,KAAK,CAAC,aAAa,qCAA6B,EAChD,+CAA+C,CAChD;AACH;;ACnMO,MAAM,aAAa,GAAG;AACtB,MAAM,iBAAiB,GAAG;;AC+BjC;;;;;;;;;;;;;;;AAeG;AAEH;;;AAGG;AACG,SAAU,WAAW,CAAC,KAAiC,EAAA;AAC3D,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC3B,QAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAQ;;AAE5B,IAAA,OAAO,KAAc;AACvB;AAEA;;;AAGG;AACG,SAAU,WAAW,CAAC,KAAiC,EAAA;AAC3D,IAAA,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;;;AAG3B,QAAA,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,QAAQ;AAAE,YAAA,OAAO,KAAc;AAC1D,QAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAQ;;AAE5B,IAAA,OAAO,IAAI;AACb;AAEA;;;AAGG;AACa,SAAA,gBAAgB,CAAC,KAAa,EAAE,KAAY,EAAA;AAC1D,IAAA,SAAS,IAAI,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC;IAC7C,SAAS,IAAI,wBAAwB,CAAC,KAAK,EAAE,aAAa,EAAE,mCAAmC,CAAC;AAChG,IAAA,OAAO,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAClC;AAEA;;;;;;;AAOG;AACa,SAAA,gBAAgB,CAAC,KAAY,EAAE,KAAY,EAAA;AACzD,IAAA,SAAS,IAAI,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC;IAC9C,SAAS,IAAI,kBAAkB,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;IACnD,MAAM,IAAI,GAAU,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnD,IAAA,OAAO,IAAI;AACb;AAEA;;;;;;;AAOG;AACa,SAAA,sBAAsB,CAAC,KAAmB,EAAE,KAAY,EAAA;AACtE,IAAA,MAAM,KAAK,GAAG,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK;AAC/C,IAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAChB,QAAA,SAAS,IAAI,mBAAmB,CAAC,KAAM,EAAE,KAAK,CAAC;QAC/C,MAAM,IAAI,GAAiB,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpD,QAAA,OAAO,IAAI;;AAEb,IAAA,OAAO,IAAI;AACb;AAEA;AACgB,SAAA,QAAQ,CAAC,KAAY,EAAE,KAAa,EAAA;IAClD,SAAS,IAAI,iBAAiB,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;AAClE,IAAA,SAAS,IAAI,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,uBAAuB,CAAC;IAC9E,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAU;IACxC,SAAS,IAAI,KAAK,KAAK,IAAI,IAAI,WAAW,CAAC,KAAK,CAAC;AACjD,IAAA,OAAO,KAAK;AACd;AAEA;AACgB,SAAA,IAAI,CAAI,IAAmB,EAAE,KAAa,EAAA;AACxD,IAAA,SAAS,IAAI,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC;AAC5C,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB;AAEA;AACM,SAAU,KAAK,CAAI,KAAY,EAAE,KAAY,EAAE,KAAa,EAAE,KAAQ,EAAA;;;IAG1E,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;AAC9B,QAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI;AACxB,QAAA,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI;;AAE/B,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK;AACtB;AAEgB,SAAA,wBAAwB,CAAC,SAAiB,EAAE,QAAe,EAAA;;AAEzE,IAAA,SAAS,IAAI,kBAAkB,CAAC,QAAQ,EAAE,SAAS,CAAC;AACpD,IAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;AACrC,IAAA,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC;AAC9D,IAAA,OAAO,KAAK;AACd;AAEA;AACM,SAAU,cAAc,CAAC,IAAW,EAAA;IACxC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAA0B,CAAA;AAC/C;AAEA;;;;;AAKG;AACG,SAAU,4BAA4B,CAAC,IAAW,EAAA;IACtD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAsB,GAAA;AAC3C;AAEA;AACM,SAAU,uBAAuB,CAAC,IAAW,EAAA;AACjD,IAAA,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACnC;AASgB,SAAA,WAAW,CACzB,MAAyB,EACzB,KAAgC,EAAA;AAEhC,IAAA,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;AAAE,QAAA,OAAO,IAAI;AACtD,IAAA,SAAS,IAAI,kBAAkB,CAAC,MAAO,EAAE,KAAK,CAAC;AAC/C,IAAA,OAAO,MAAO,CAAC,KAAK,CAAiB;AACvC;AAEA;;;AAGG;AACG,SAAU,sBAAsB,CAAC,KAAY,EAAA;AACjD,IAAA,KAAK,CAAC,mBAAmB,CAAC,GAAG,CAAsB;AACrD;AAEA;;;AAGG;AACG,SAAU,kBAAkB,CAAC,KAAY,EAAA;AAC7C,IAAA,IAAI,KAAK,CAAC,KAAK,CAAC,GAAA,IAAA,+BAA2B;QACzC;;AAEF,IAAA,KAAK,CAAC,KAAK,CAAC,IAAA,IAAA;AACZ,IAAA,IAAI,4BAA4B,CAAC,KAAK,CAAC,EAAE;QACvC,yBAAyB,CAAC,KAAK,CAAC;;AAEpC;AAEA;;;;AAIG;AACa,SAAA,WAAW,CAAC,YAAoB,EAAE,WAAkB,EAAA;AAClE,IAAA,OAAO,YAAY,GAAG,CAAC,EAAE;QACvB,SAAS;YACP,aAAa,CACX,WAAW,CAAC,gBAAgB,CAAC,EAC7B,wEAAwE,CACzE;AACH,QAAA,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAE;AAC5C,QAAA,YAAY,EAAE;;AAEhB,IAAA,OAAO,WAAW;AACpB;AAEM,SAAU,0BAA0B,CAAC,KAAY,EAAA;IACrD,OAAO,CAAC,EACN,KAAK,CAAC,KAAK,CAAC,IAAI,IAAA,gCAAA,IAAA,yCAA2D;AAC3E,QAAA,KAAK,CAAC,0BAA0B,CAAC,EAAE,KAAK,CACzC;AACH;AAEA;;;AAGG;AACG,SAAU,oCAAoC,CAAC,KAAY,EAAA;IAC/D,KAAK,CAAC,WAAW,CAAC,CAAC,wBAAwB,EAAE,MAAM,yCAAiC;AACpF,IAAA,IAAI,KAAK,CAAC,KAAK,CAAC,GAAA,EAAA,yBAAqB;AACnC,QAAA,KAAK,CAAC,KAAK,CAAC,IAAA,IAAA;;AAEd,IAAA,IAAI,0BAA0B,CAAC,KAAK,CAAC,EAAE;QACrC,yBAAyB,CAAC,KAAK,CAAC;;AAEpC;AAEA;;;;;;AAMG;AACG,SAAU,yBAAyB,CAAC,KAAY,EAAA;IACpD,KAAK,CAAC,WAAW,CAAC,CAAC,wBAAwB,EAAE,MAAM,sDAA8C;AACjG,IAAA,IAAI,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC;AAClC,IAAA,OAAO,MAAM,KAAK,IAAI,EAAE;;;AAGtB,QAAA,IAAI,MAAM,CAAC,KAAK,CAAC,GAAA,IAAA,0CAAsC;YACrD;;AAGF,QAAA,MAAM,CAAC,KAAK,CAAC,IAAA,IAAA;AACb,QAAA,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,EAAE;YACzC;;AAEF,QAAA,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;;AAEnC;AAEA;;AAEG;AACa,SAAA,mBAAmB,CAAC,KAAY,EAAE,iBAA6B,EAAA;AAC7E,IAAA,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACtB,QAAA,MAAM,IAAI,YAAY,CAAA,GAAA,gDAEpB,SAAS,IAAI,kCAAkC,CAChD;;AAEH,IAAA,IAAI,KAAK,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;AACpC,QAAA,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE;;IAE9B,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;AACjD;AAEA;;AAEG;AACa,SAAA,oBAAoB,CAAC,KAAY,EAAE,iBAA6B,EAAA;AAC9E,IAAA,IAAI,KAAK,CAAC,gBAAgB,CAAC,KAAK,IAAI;QAAE;IAEtC,MAAM,YAAY,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC;AACvE,IAAA,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;;AAEnD;AAEA;;;;AAIG;AACG,SAAU,cAAc,CAAC,KAAY,EAAA;AACzC,IAAA,SAAS,IAAI,WAAW,CAAC,KAAK,CAAC;AAC/B,IAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAC5B,IAAA,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM;AACvD;AAEM,SAAU,uBAAuB,CAAC,IAAW,EAAA;;IAEjD,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAC9B;AAEM,SAAU,uBAAuB,CAAC,KAAY,EAAA;AAClD,IAAA,QAAQ,KAAK,CAAC,OAAO,KAAK,EAAE;AAC9B;AAEA;;;;;;AAMG;AACG,SAAU,uBAAuB,CACrC,KAAY,EACZ,KAAY,EACZ,OAAY,EACZ,SAAmB,EAAA;AAEnB,IAAA,MAAM,QAAQ,GAAG,uBAAuB,CAAC,KAAK,CAAC;;;;;IAM/C,SAAS;AACP,QAAA,aAAa,CACX,OAAO,EACP,6EAA6E,CAC9E;AACH,IAAA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAEtB,IAAA,IAAI,KAAK,CAAC,eAAe,EAAE;AACzB,QAAA,uBAAuB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;;SAC9D;;;QAGL,IAAI,SAAS,EAAE;YACb,MAAM,CAAC,MAAM,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;;;AAGnD;;AChKA,MAAM,gBAAgB,GAAqB;AACzC,IAAA,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC;AAC1B,IAAA,eAAe,EAAE,IAAI;AACrB,IAAA,sBAAsB,EAAE,IAAI;CAC7B;IAEW;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC5B,IAAA,kBAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,kBAAA,CAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,kBAAA,CAAA,kBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc;AAChB,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,GAI7B,EAAA,CAAA,CAAA;AAED;;;;;;;AAOG;AACH,IAAI,mBAAmB,GAAuB,CAAC,CAAC;AAEhD;;;;AAIG;AACH,IAAI,kBAAkB,GAAG,KAAK;SAWd,oBAAoB,GAAA;AAClC,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,iBAAiB;AAClD;SAEgB,yBAAyB,GAAA;AACvC,IAAA,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,EAAE;AAC7C;SAEgB,yBAAyB,GAAA;AACvC,IAAA,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,EAAE;AAC7C;SAEgB,kBAAkB,GAAA;IAChC,OAAO,gBAAgB,CAAC,eAAe;AACzC;AAEA;;;AAGG;SACa,sBAAsB,GAAA;AACpC,IAAA,OAAO,gBAAgB,CAAC,sBAAsB,KAAK,IAAI;AACzD;AAEA;;;;AAIG;AACG,SAAU,wBAAwB,CAAC,KAAY,EAAA;AACnD,IAAA,OAAO,gBAAgB,CAAC,sBAAsB,KAAK,KAAK;AAC1D;AAEA;;;;;;;;;;;;;;;;;;AAkBG;SACa,gBAAgB,GAAA;AAC9B,IAAA,gBAAgB,CAAC,eAAe,GAAG,IAAI;AACzC;AAEA;;;AAGG;AACG,SAAU,uBAAuB,CAAC,KAAY,EAAA;AAClD,IAAA,gBAAgB,CAAC,sBAAsB,GAAG,KAAK;AACjD;AAEA;;;;;;;;;;;;;;;;;;AAkBG;SACa,iBAAiB,GAAA;AAC/B,IAAA,gBAAgB,CAAC,eAAe,GAAG,KAAK;AAC1C;AAEA;;AAEG;SACa,uBAAuB,GAAA;AACrC,IAAA,gBAAgB,CAAC,sBAAsB,GAAG,IAAI;AAChD;AAEA;;AAEG;SACa,QAAQ,GAAA;AACtB,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,KAAiB;AAClD;AAEA;;AAEG;SACa,QAAQ,GAAA;AACtB,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,KAAK;AACtC;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,aAAa,CAAU,aAA8B,EAAA;AACnE,IAAA,gBAAgB,CAAC,MAAM,CAAC,YAAY,GAAG,aAA6B;AACpE,IAAA,OAAQ,aAA8B,CAAC,OAAO,CAAiB;AACjE;AAEA;;;;;AAKG;AACG,SAAU,WAAW,CAAI,KAAS,EAAA;AACtC,IAAA,gBAAgB,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI;AAC3C,IAAA,OAAO,KAAK;AACd;SAEgB,eAAe,GAAA;AAC7B,IAAA,IAAI,YAAY,GAAG,4BAA4B,EAAE;IACjD,OAAO,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,IAAI,KAA0B,EAAA,8BAAE;AAC3E,QAAA,YAAY,GAAG,YAAY,CAAC,MAAM;;AAEpC,IAAA,OAAO,YAAY;AACrB;SAEgB,4BAA4B,GAAA;AAC1C,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,YAAY;AAC7C;SAEgB,qBAAqB,GAAA;AACnC,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM;AACtC,IAAA,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY;AACxC,IAAA,OAAO,MAAM,CAAC,QAAQ,GAAG,YAAY,GAAG,YAAa,CAAC,MAAM;AAC9D;AAEgB,SAAA,eAAe,CAAC,KAAmB,EAAE,QAAiB,EAAA;AACpE,IAAA,SAAS,IAAI,KAAK,IAAI,mBAAmB,CAAC,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC;AAC/E,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM;AACtC,IAAA,MAAM,CAAC,YAAY,GAAG,KAAK;AAC3B,IAAA,MAAM,CAAC,QAAQ,GAAG,QAAQ;AAC5B;SAEgB,oBAAoB,GAAA;AAClC,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,QAAQ;AACzC;SAEgB,0BAA0B,GAAA;AACxC,IAAA,gBAAgB,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK;AAC1C;SAEgB,eAAe,GAAA;AAC7B,IAAA,MAAM,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY;AACzD,IAAA,SAAS,IAAI,aAAa,CAAC,YAAY,EAAE,+BAA+B,CAAC;AACzE,IAAA,OAAO,YAAa;AACtB;SAEgB,sBAAsB,GAAA;AACpC,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,yCAAyC,CAAC;AACnE,IAAA,OAAO,mBAAmB,KAAK,kBAAkB,CAAC,GAAG;AACvD;SAEgB,0BAA0B,GAAA;AACxC,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,yCAAyC,CAAC;AACnE,IAAA,OAAO,mBAAmB,KAAK,kBAAkB,CAAC,UAAU;AAC9D;AAEM,SAAU,yBAAyB,CAAC,IAAwB,EAAA;AAChE,IAAA,CAAC,SAAS,IAAI,UAAU,CAAC,yCAAyC,CAAC;IACnE,mBAAmB,GAAG,IAAI;AAC5B;SAEgB,iBAAiB,GAAA;AAC/B,IAAA,OAAO,kBAAkB;AAC3B;AAEM,SAAU,oBAAoB,CAAC,IAAa,EAAA;IAChD,MAAM,IAAI,GAAG,kBAAkB;IAC/B,kBAAkB,GAAG,IAAI;AACzB,IAAA,OAAO,IAAI;AACb;AAEA;SACgB,cAAc,GAAA;AAC5B,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM;AACtC,IAAA,IAAI,KAAK,GAAG,MAAM,CAAC,gBAAgB;AACnC,IAAA,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,KAAK,GAAG,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,KAAK,CAAC,iBAAiB;;AAElE,IAAA,OAAO,KAAK;AACd;SAEgB,eAAe,GAAA;AAC7B,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,YAAY;AAC7C;AAEM,SAAU,eAAe,CAAC,KAAa,EAAA;IAC3C,QAAQ,gBAAgB,CAAC,MAAM,CAAC,YAAY,GAAG,KAAK;AACtD;SAEgB,gBAAgB,GAAA;AAC9B,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,YAAY,EAAE;AAC/C;AAEM,SAAU,qBAAqB,CAAC,KAAa,EAAA;AACjD,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM;AACtC,IAAA,MAAM,KAAK,GAAG,MAAM,CAAC,YAAY;IACjC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,KAAK;AACjD,IAAA,OAAO,KAAK;AACd;SAEgB,aAAa,GAAA;AAC3B,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,MAAM;AACvC;AAEM,SAAU,cAAc,CAAC,aAAsB,EAAA;AACnD,IAAA,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,aAAa;AAChD;AAEA;;;;;;;;;;AAUG;AACa,SAAA,6BAA6B,CAC3C,gBAAwB,EACxB,qBAA6B,EAAA;AAE7B,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM;IACtC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,gBAAgB,GAAG,gBAAgB;IAChE,wBAAwB,CAAC,qBAAqB,CAAC;AACjD;AAEA;;;;AAIG;SACa,wBAAwB,GAAA;AACtC,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,qBAAqB;AACtD;AAEA;;;;AAIG;AACG,SAAU,wBAAwB,CAAC,qBAA6B,EAAA;AACpE,IAAA,gBAAgB,CAAC,MAAM,CAAC,qBAAqB,GAAG,qBAAqB;AACvE;AAEA;;;;;AAKG;AACG,SAAU,sBAAsB,CAAC,KAAY,EAAA;AACjD,IAAA,MAAM,qBAAqB,GAAG,gBAAgB,CAAC,MAAM,CAAC,qBAAqB;AAC3E,IAAA,OAAO,qBAAqB,KAAK,CAAC,CAAC,GAAG,IAAI,GAAI,KAAK,CAAC,qBAAqB,CAAuB;AAClG;SAEgB,oBAAoB,GAAA;AAClC,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,iBAAiB;AAClD;AAEM,SAAU,oBAAoB,CAAC,KAAa,EAAA;AAChD,IAAA,gBAAgB,CAAC,MAAM,CAAC,iBAAiB,GAAG,KAAK;AACnD;AAEA;;;;AAIG;AACH,SAAS,mBAAmB,CAAC,KAAY,EAAA;AACvC,IAAA,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;;AAG1B,IAAA,IAAI,KAAK,CAAC,IAAI,KAAA,CAAA,2BAAyB;QACrC,SAAS,IAAI,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,kDAAkD,CAAC;QAC/F,OAAO,KAAK,CAAC,SAAS;;;;;AAMxB,IAAA,IAAI,KAAK,CAAC,IAAI,KAAA,CAAA,4BAA0B;AACtC,QAAA,OAAO,KAAK,CAAC,MAAM,CAAC;;;AAItB,IAAA,OAAO,IAAI;AACb;AAEA;;;;;;;;;;;;AAYG;SACa,OAAO,CAAC,KAAY,EAAE,KAAY,EAAE,KAA0B,EAAA;AAC5E,IAAA,SAAS,IAAI,sBAAsB,CAAC,KAAK,CAAC;IAE1C,IAAI,KAAK,GAA+B,CAAA,qCAAE;QACxC,SAAS,IAAI,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAErD,IAAI,WAAW,GAAG,KAAqB;QACvC,IAAI,WAAW,GAAG,KAAK;QAEvB,OAAO,IAAI,EAAE;AACX,YAAA,SAAS,IAAI,aAAa,CAAC,WAAW,EAAE,gCAAgC,CAAC;AACzE,YAAA,WAAW,GAAG,WAAY,CAAC,MAAsB;YACjD,IAAI,WAAW,KAAK,IAAI,IAAI,EAAE,KAAK,GAAA,CAAA,gCAA4B,EAAE;AAC/D,gBAAA,WAAW,GAAG,mBAAmB,CAAC,WAAW,CAAC;gBAC9C,IAAI,WAAW,KAAK,IAAI;oBAAE;;;AAI1B,gBAAA,SAAS,IAAI,aAAa,CAAC,WAAW,EAAE,gCAAgC,CAAC;AACzE,gBAAA,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAE;;;;gBAK5C,IAAI,WAAW,CAAC,IAAI,IAAI,CAA8C,2BAAA,CAAA,kCAAC,EAAE;oBACvE;;;iBAEG;gBACL;;;AAGJ,QAAA,IAAI,WAAW,KAAK,IAAI,EAAE;;AAExB,YAAA,OAAO,KAAK;;aACP;YACL,KAAK,GAAG,WAAW;YACnB,KAAK,GAAG,WAAW;;;AAIvB,IAAA,SAAS,IAAI,mBAAmB,CAAC,KAAK,EAAE,KAAK,CAAC;IAC9C,MAAM,MAAM,IAAI,gBAAgB,CAAC,MAAM,GAAG,WAAW,EAAE,CAAC;AACxD,IAAA,MAAM,CAAC,YAAY,GAAG,KAAK;AAC3B,IAAA,MAAM,CAAC,KAAK,GAAG,KAAK;AAEpB,IAAA,OAAO,IAAI;AACb;AAEA;;;;;;;;;;AAUG;AACG,SAAU,SAAS,CAAC,OAAc,EAAA;AACtC,IAAA,SAAS,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAQ,EAAE,MAAM,CAAC;AAClE,IAAA,SAAS,IAAI,sBAAsB,CAAC,OAAO,CAAC;AAC5C,IAAA,MAAM,SAAS,GAAG,WAAW,EAAE;IAC/B,IAAI,SAAS,EAAE;QACb,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,uBAAuB,CAAC;QAC9D,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,uBAAuB,CAAC;QAC3D,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,uBAAuB,CAAC;QAC3D,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;QACjE,WAAW,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,EAAE,uBAAuB,CAAC;QACpE,WAAW,CAAC,SAAS,CAAC,qBAAqB,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;QACzE,WAAW,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,EAAE,uBAAuB,CAAC;QACtE,WAAW,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,uBAAuB,CAAC;QACpE,WAAW,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAAC,EAAE,uBAAuB,CAAC;;AAEtE,IAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AAC5B,IAAA,gBAAgB,CAAC,MAAM,GAAG,SAAS;AACnC,IAAA,SAAS,IAAI,KAAK,CAAC,UAAU,IAAI,mBAAmB,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC;AAC7E,IAAA,SAAS,CAAC,YAAY,GAAG,KAAK,CAAC,UAAW;AAC1C,IAAA,SAAS,CAAC,KAAK,GAAG,OAAO;AACzB,IAAA,SAAS,CAAC,KAAK,GAAG,KAAK;AACvB,IAAA,SAAS,CAAC,YAAY,GAAG,OAAO;AAChC,IAAA,SAAS,CAAC,YAAY,GAAG,KAAK,CAAC,iBAAiB;AAChD,IAAA,SAAS,CAAC,MAAM,GAAG,KAAK;AAC1B;AAEA;;AAEG;AACH,SAAS,WAAW,GAAA;AAClB,IAAA,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM;AAC7C,IAAA,MAAM,WAAW,GAAG,aAAa,KAAK,IAAI,GAAG,IAAI,GAAG,aAAa,CAAC,KAAK;AACvE,IAAA,MAAM,SAAS,GAAG,WAAW,KAAK,IAAI,GAAG,YAAY,CAAC,aAAa,CAAC,GAAG,WAAW;AAClF,IAAA,OAAO,SAAS;AAClB;AAEA,SAAS,YAAY,CAAC,MAAqB,EAAA;AACzC,IAAA,MAAM,MAAM,GAAW;AACrB,QAAA,YAAY,EAAE,IAAI;AAClB,QAAA,QAAQ,EAAE,IAAI;AACd,QAAA,KAAK,EAAE,IAAK;AACZ,QAAA,KAAK,EAAE,IAAK;QACZ,aAAa,EAAE,CAAC,CAAC;AACjB,QAAA,YAAY,EAAE,IAAI;AAClB,QAAA,iBAAiB,EAAE,CAAC;AACpB,QAAA,gBAAgB,EAAE,IAAI;QACtB,qBAAqB,EAAE,CAAC,CAAC;QACzB,gBAAgB,EAAE,CAAC,CAAC;QACpB,YAAY,EAAE,CAAC,CAAC;AAChB,QAAA,iBAAiB,EAAE,CAAC;AACpB,QAAA,MAAM,EAAE,MAAO;AACf,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,MAAM,EAAE,KAAK;KACd;AACD,IAAA,MAAM,KAAK,IAAI,KAAK,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;AAC3C,IAAA,OAAO,MAAM;AACf;AAEA;;;;;;;;AAQG;AACH,SAAS,cAAc,GAAA;AACrB,IAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM;AACzC,IAAA,gBAAgB,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;AAC1C,IAAA,SAAS,CAAC,YAAY,GAAG,IAAK;AAC9B,IAAA,SAAS,CAAC,KAAK,GAAG,IAAK;AACvB,IAAA,OAAO,SAAS;AAClB;AAEA;;;;;AAKG;AACI,MAAM,OAAO,GAAe;AAEnC;;;;;;;AAOG;SACa,SAAS,GAAA;AACvB,IAAA,MAAM,SAAS,GAAG,cAAc,EAAE;AAClC,IAAA,SAAS,CAAC,QAAQ,GAAG,IAAI;AACzB,IAAA,SAAS,CAAC,KAAK,GAAG,IAAK;AACvB,IAAA,SAAS,CAAC,aAAa,GAAG,CAAC,CAAC;AAC5B,IAAA,SAAS,CAAC,YAAY,GAAG,IAAI;AAC7B,IAAA,SAAS,CAAC,iBAAiB,GAAG,CAAC;AAC/B,IAAA,SAAS,CAAC,qBAAqB,GAAG,CAAC,CAAC;AACpC,IAAA,SAAS,CAAC,gBAAgB,GAAG,IAAI;AACjC,IAAA,SAAS,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC/B,IAAA,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC;AAC3B,IAAA,SAAS,CAAC,iBAAiB,GAAG,CAAC;AACjC;AAEM,SAAU,eAAe,CAAU,KAAa,EAAA;IACpD,MAAM,YAAY,IAAI,gBAAgB,CAAC,MAAM,CAAC,YAAY,GAAG,WAAW,CACtE,KAAK,EACL,gBAAgB,CAAC,MAAM,CAAC,YAAa,CACtC,CAAC;AACF,IAAA,OAAO,YAAY,CAAC,OAAO,CAAiB;AAC9C;AAEA;;;;;AAKG;SACa,gBAAgB,GAAA;AAC9B,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,aAAa;AAC9C;AAEA;;;;;;;;AAQG;AACG,SAAU,gBAAgB,CAAC,KAAa,EAAA;IAC5C,SAAS;QACP,KAAK,KAAK,CAAC,CAAC;AACZ,QAAA,wBAAwB,CAAC,KAAK,EAAE,aAAa,EAAE,2CAA2C,CAAC;IAC7F,SAAS;AACP,QAAA,cAAc,CACZ,KAAK,EACL,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EACpC,qCAAqC,CACtC;AACH,IAAA,gBAAgB,CAAC,MAAM,CAAC,aAAa,GAAG,KAAK;AAC/C;AAEA;;AAEG;SACa,gBAAgB,GAAA;AAC9B,IAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM;IACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,aAAa,CAAC;AACrD;AAEA;;;;AAIG;SACa,cAAc,GAAA;AAC5B,IAAA,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,GAAG,aAAa;AAC1D;AAEA;;;;AAIG;SACa,iBAAiB,GAAA;AAC/B,IAAA,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,GAAG,iBAAiB;AAC9D;AAEA;;;;;AAKG;SACa,eAAe,GAAA;AAC7B,IAAA,qBAAqB,EAAE;AACzB;AAEA;;;AAGG;SACa,qBAAqB,GAAA;AACnC,IAAA,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,GAAG,IAAI;AACjD;SAEgB,YAAY,GAAA;AAC1B,IAAA,OAAO,gBAAgB,CAAC,MAAM,CAAC,gBAAgB;AACjD;AAEA,IAAI,mBAAmB,GAAG,IAAI;AAE9B;;;AAGG;SACa,kBAAkB,GAAA;AAChC,IAAA,OAAO,mBAAmB;AAC5B;AAEA;;;AAGG;AACG,SAAU,kBAAkB,CAAC,IAAa,EAAA;IAC9C,mBAAmB,GAAG,IAAI;AAC5B;;ACzzBA;;AAEG;AACG,SAAU,cAAc,CAC5B,OAAoC,EACpC,MAA0B,GAAA,IAAI,EAC9B,mBAAA,GAA+D,IAAI,EACnE,IAAa,EAAA;AAEb,IAAA,MAAM,QAAQ,GAAG,sCAAsC,CACrD,OAAO,EACP,MAAM,EACN,mBAAmB,EACnB,IAAI,CACL;IACD,QAAQ,CAAC,2BAA2B,EAAE;AACtC,IAAA,OAAO,QAAQ;AACjB;AAEA;;;;AAIG;SACa,sCAAsC,CACpD,OAAoC,EACpC,SAA0B,IAAI,EAC9B,mBAA+D,GAAA,IAAI,EACnE,IAAa,EACb,MAAS,GAAA,IAAI,GAAG,EAAiB,EAAA;AAEjC,IAAA,MAAM,SAAS,GAAG,CAAC,mBAAmB,IAAI,WAAW,EAAE,mBAAmB,CAAC,OAAO,CAAC,CAAC;IACpF,IAAI,GAAG,IAAI,KAAK,OAAO,OAAO,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;AAE7E,IAAA,OAAO,IAAI,UAAU,CAAC,SAAS,EAAE,MAAM,IAAI,eAAe,EAAE,EAAE,IAAI,IAAI,IAAI,EAAE,MAAM,CAAC;AACrF;;AClCA;;;;;;;;;;;;;;;;;;;;;;;AAuBG;MACmB,QAAQ,CAAA;AAC5B,IAAA,OAAO,kBAAkB,GAAG,kBAAkB;AAC9C,IAAA,OAAO,IAAI,GAA6B,IAAI,YAAY,EAAE;AA2D1D,IAAA,OAAO,MAAM,CACX,OAEmF,EACnF,MAAiB,EAAA;AAEjB,QAAA,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC1B,YAAA,OAAO,cAAc,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,CAAC;;aACjD;AACL,YAAA,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE;AAC/B,YAAA,OAAO,cAAc,CAAC,EAAC,IAAI,EAAC,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;;;;IAK1E,OAAO,KAAK,6CAA6C,kBAAkB,CAAC;AAC1E,QAAA,KAAK,EAAE,QAAQ;AACf,QAAA,UAAU,EAAE,KAAK;AACjB,QAAA,OAAO,EAAE,MAAM,QAAQ,CAACF,UAAQ,CAAC;AAClC,KAAA,CAAC;AAEF;;;AAGG;IACH,OAAO,iBAAiB,GAA4B,CAAA,CAAA;;;ACtHtD;;;;;;AAMG;AACU,MAAA,QAAQ,GAAG,IAAI,cAAc,CAAW,SAAS,GAAG,eAAe,GAAG,EAAE;;ACDrF;;;;;;;AAOG;MACmB,UAAU,CAAA;AA2B9B;;;AAGG;AACH,IAAA,OAAO,iBAAiB,GAAqB,gBAAgB;AAE7D;;;AAGG;IACH,OAAO,aAAa,GAAkD,CAAC,QAAQ,KAAK,QAAQ;;AAGxF,MAAO,sBAAuB,SAAQ,UAAU,CAAA;AAC/B,IAAA,MAAA;AAArB,IAAA,WAAA,CAAqB,MAAa,EAAA;AAChC,QAAA,KAAK,EAAE;QADY,IAAM,CAAA,MAAA,GAAN,MAAM;;AAI3B,IAAA,IAAa,SAAS,GAAA;AACpB,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;;AAGxB,IAAA,SAAS,CAAC,QAAoB,EAAA;AACrC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM;AAQzB,QAAA,mBAAmB,CAAC,KAAK,EAAE,QAAQ,CAAC;QACpC,OAAO,MAAM,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC;;AAErD;AAED,SAAS,gBAAgB,GAAA;AACvB,IAAA,OAAO,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;AAC/C;;ACxEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;MACU,YAAY,CAAA;AACvB;;AAEG;IACH,QAAQ,GAAY,OAAO;AAE3B,IAAA,WAAW,CAAC,KAAU,EAAA;QACpB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC;;AAEtC;AAED;;AAEG;MACU,kCAAkC,GAAG,IAAI,cAAc,CAClE,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,wBAAwB,GAAG,EAAE,EAC7E;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;;;AAGZ,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,mBAAmB,CAAC;AAC5C,QAAA,IAAI,gBAA8B;QAClC,OAAO,CAAC,CAAU,KAAI;AACpB,YAAA,IAAI,QAAQ,CAAC,SAAS,IAAI,CAAC,gBAAgB,EAAE;gBAC3C,UAAU,CAAC,MAAK;AACd,oBAAA,MAAM,CAAC;AACT,iBAAC,CAAC;;iBACG;AACL,gBAAA,gBAAgB,KAAK,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC;AAC/C,gBAAA,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;;AAEnC,SAAC;KACF;AACF,CAAA;AAGU,MAAA,kCAAkC,GAAG;AAChD,IAAA,OAAO,EAAE,uBAAuB;IAChC,QAAQ,EAAE,MAAM,KAAK,MAAM,CAAC,YAAY,CAAC;AACzC,IAAA,KAAK,EAAE,IAAI;;AAGb,MAAM,oBAAoB,GAAG,IAAI,cAAc,CAAO,SAAS,GAAG,sBAAsB,GAAG,EAAE,EAAE;AAC7F,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,EAAE;YACvD;;QAEF,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW;QAC3C,IAAI,CAAC,MAAM,EAAE;YACX;;AAGF,QAAA,MAAM,YAAY,GAAG,MAAM,CAAC,kCAAkC,CAAC;AAC/D,QAAA,MAAM,iBAAiB,GAAG,CAAC,CAAwB,KAAI;AACrD,YAAA,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC;YACtB,CAAC,CAAC,cAAc,EAAE;AACpB,SAAC;AACD,QAAA,MAAM,aAAa,GAAG,CAAC,CAAa,KAAI;AACtC,YAAA,IAAI,CAAC,CAAC,KAAK,EAAE;AACX,gBAAA,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC;;iBAChB;AACL,gBAAA,YAAY,CACV,IAAI,KAAK,CACP;AACE,sBAAE,CAAA,mEAAA,EAAsE,CAAC,CAAC,OAAO,CAAE;AACnF,sBAAE,CAAC,CAAC,OAAO,EACb,EAAC,KAAK,EAAE,CAAC,EAAC,CACX,CACF;;YAEH,CAAC,CAAC,cAAc,EAAE;AACpB,SAAC;QAED,MAAM,mBAAmB,GAAG,MAAK;AAC/B,YAAA,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,iBAAiB,CAAC;AAChE,YAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC;AACjD,SAAC;;;AAID,QAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC;;aAC7B;AACL,YAAA,mBAAmB,EAAE;;AAGvB,QAAA,MAAM,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MAAK;AAChC,YAAA,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC;AAClD,YAAA,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,EAAE,iBAAiB,CAAC;AACrE,SAAC,CAAC;KACH;AACF,CAAA,CAAC;AAEF;;;;;;AAMG;SACa,kCAAkC,GAAA;AAChD,IAAA,OAAO,wBAAwB,CAAC;QAC9B,6BAA6B,CAAC,MAAM,KAAK,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACvE,KAAA,CAAC;AACJ;;AClIA;;;;AAIG;AACG,SAAU,QAAQ,CAAC,KAAc,EAAA;IACrC,OAAO,OAAO,KAAK,KAAK,UAAU,IAAK,KAAyB,CAAC,MAAM,CAAC,KAAK,SAAS;AACxF;;ACkBA;;;AAGG;AACG,SAAU,qBAAqB,CAAI,KAAkC,EAAA;;;AAGzE,IAAA,OAAO,IAAK;AACd;AAiBA;;AAEG;AACa,SAAA,MAAM,CAAI,YAAe,EAAE,OAAgC,EAAA;AACzE,IAAA,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC;IAErE,MAAM,QAAQ,GAAG,GAA0C;AAC3D,IAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;AAE7B,IAAA,QAAQ,CAAC,GAAG,GAAG,GAAG;AAClB,IAAA,QAAQ,CAAC,MAAM,GAAG,MAAM;IACxB,QAAQ,CAAC,UAAU,GAAG,kBAAkB,CAAC,IAAI,CAAC,QAAe,CAAoB;IAEjF,IAAI,SAAS,EAAE;QACb,QAAQ,CAAC,QAAQ,GAAG,MAAM,CAAY,SAAA,EAAA,QAAQ,EAAE,CAAA,CAAA,CAAG;AACnD,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO,EAAE,SAAS;;AAGrC,IAAA,OAAO,QAA6B;AACtC;SAEgB,kBAAkB,GAAA;AAChC,IAAA,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAA6C;AACrE,IAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;AACjC,QAAA,MAAM,UAAU,GAAG,MAAM,IAAI,EAAE;AAC9B,QAAA,UAAkB,CAAC,MAAM,CAAC,GAAG,IAAI;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAuB;;IAE3C,OAAO,IAAI,CAAC,UAAU;AACxB;AAEA;;AAEG;AACG,SAAU,gBAAgB,CAAC,KAAc,EAAA;IAC7C,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAQ,KAAa,CAAC,GAAG,KAAK,UAAU;AACpE;;ACrDA;;AAEG;MACmB,wBAAwB,CAAA;AAG7C;AAED;AACO,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAChD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,kBAAkB,GAAG,EAAE,EACvE,EAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,EAAC;AAG5C;AACO,MAAM,iBAAiB,GAAG,IAAI,cAAc,CACjD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,mBAAmB,GAAG,EAAE,EACxE,EAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,EAAC;MAG/B,2BAA2B,GAAG,IAAI,cAAc,CAC3D,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,oBAAoB,GAAG,EAAE;AAG3E;MACa,qBAAqB,GAAG,IAAI,cAAc,CACrD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,kCAAkC,GAAG,EAAE;;ACvEzF;;;;;;;AAOG;AACa,SAAA,0BAA0B,CAAC,OAAiB,EAAE,YAAqB,EAAA;;;AAGjF,IAAA,IAAI,iBAAiB,EAAE,KAAK,IAAI,EAAE;QAChC,MAAM,IAAI,YAAY,CAAA,CAAA,GAAA,+DAEpB,SAAS;AACP,YAAA,CAAA,EAAG,OAAO,CAAC,IAAI,CACb,mDAAA,EAAA,YAAY,GAAG,CAAI,CAAA,EAAA,YAAY,EAAE,GAAG,EACtC,CAAA,CAAE,CACL;;AAEL;;MCpBa,WAAW,CAAA;AAEX,IAAA,IAAA;AACA,IAAA,IAAA;IAFX,WACW,CAAA,IAAW,EACX,IAAW,EAAA;QADX,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAI,CAAA,IAAA,GAAJ,IAAI;;AAGf;;;AAGG;AACH,IAAA,OAAO,iBAAiB,GAAG,iBAAiB;;SAG9B,iBAAiB,GAAA;IAC/B,OAAO,IAAI,WAAW,CAAC,QAAQ,EAAG,EAAE,eAAe,EAAG,CAAC;AACzD;;ACRA;;AAEG;MACU,oBAAoB,CAAA;IACvB,MAAM,GAAG,CAAC;AACV,IAAA,YAAY,GAAG,IAAI,GAAG,EAAU;IAChC,SAAS,GAAG,KAAK;AAEjB,IAAA,WAAW,GAAG,IAAI,eAAe,CAAU,KAAK,CAAC;AAEzD,IAAA,IAAI,eAAe,GAAA;;AAEjB,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK;;AAGxD;;;AAGG;AACH,IAAA,IAAI,yBAAyB,GAAA;AAC3B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;;AAElB,YAAA,OAAO,IAAI,UAAU,CAAU,CAAC,UAAU,KAAI;AAC5C,gBAAA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;gBACtB,UAAU,CAAC,QAAQ,EAAE;AACvB,aAAC,CAAC;;QAGJ,OAAO,IAAI,CAAC,WAAW;;IAGzB,GAAG,GAAA;;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AAC5C,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;;AAE7B,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;AAC5B,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;AAC7B,QAAA,OAAO,MAAM;;AAGf,IAAA,GAAG,CAAC,MAAc,EAAA;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC;;AAGtC,IAAA,MAAM,CAAC,MAAc,EAAA;AACnB,QAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;AAChC,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;AACxD,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;;IAIhC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;AACzB,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;;;;;;;AAO9B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;;;IAIhC,OAAO,KAAK,6CAA6C,kBAAkB,CAAC;AAC1E,QAAA,KAAK,EAAE,oBAAoB;AAC3B,QAAA,UAAU,EAAE,MAAM;AAClB,QAAA,OAAO,EAAE,MAAM,IAAI,oBAAoB,EAAE;AAC1C,KAAA,CAAC;;AAGJ;;;;;;;;;;;;;;;;;;;;AAoBG;MACU,YAAY,CAAA;AACN,IAAA,oBAAoB,GAAG,MAAM,CAAC,oBAAoB,CAAC;AACnD,IAAA,SAAS,GAAG,MAAM,CAAC,wBAAwB,CAAC;AAC5C,IAAA,YAAY,GAAG,MAAM,CAAC,kCAAkC,CAAC;AAC1E;;;AAGG;IACH,GAAG,GAAA;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE;AAC9C,QAAA,OAAO,MAAK;YACV,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;;gBAE1C;;;AAGF,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,gDAAuC;AAC5D,YAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC;AAC1C,SAAC;;AAGH;;;;;;;;;;;;AAYG;AACH,IAAA,GAAG,CAAC,EAA0B,EAAA;AAC5B,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE;AAC7B,QAAA,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;;;IAInD,OAAO,KAAK,6CAA6C,kBAAkB,CAAC;AAC1E,QAAA,KAAK,EAAE,YAAY;AACnB,QAAA,UAAU,EAAE,MAAM;AAClB,QAAA,OAAO,EAAE,MAAM,IAAI,YAAY,EAAE;AAClC,KAAA,CAAC;;;ACtJY,SAAA,IAAI,CAAC,GAAG,IAAW,EAAA;;AAEnC;;ACWA;;AAEG;MACmB,eAAe,CAAA;;IAmBnC,OAAO,KAAK,6CAA6C,kBAAkB,CAAC;AAC1E,QAAA,KAAK,EAAE,eAAe;AACtB,QAAA,UAAU,EAAE,MAAM;AAClB,QAAA,OAAO,EAAE,MAAM,IAAI,wBAAwB,EAAE;AAC9C,KAAA,CAAC;;AAGJ;;;AAGG;MACU,wBAAwB,CAAA;IAC3B,gBAAgB,GAAG,CAAC;AACpB,IAAA,MAAM,GAAG,IAAI,GAAG,EAAuC;AAE/D,IAAA,GAAG,CAAC,MAAyB,EAAA;AAC3B,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACpB,QAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;;AAGvB,IAAA,QAAQ,CAAC,MAAyB,EAAA;AAChC,QAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACjB;;QAEF,IAAI,CAAC,gBAAgB,EAAE;;AAGzB,IAAA,MAAM,CAAC,MAAyB,EAAA;AAC9B,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,IAAmB;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAE;QACpC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACtB;;AAGF,QAAA,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AACpB,QAAA,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,IAAI,CAAC,gBAAgB,EAAE;;;AAInB,IAAA,OAAO,CAAC,MAAyB,EAAA;AACvC,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,IAAmB;QACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC;;QAGlC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAE;AACpC,QAAA,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACrB;;AAEF,QAAA,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;;AAGnB;;;;;AAKG;IACH,KAAK,GAAA;AACH,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE;YAChC,IAAI,YAAY,GAAG,KAAK;YACxB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;;AAEvC,gBAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,oBAAA,YAAY,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;;qBAClC;AACL,oBAAA,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;;;;;YAM3D,IAAI,CAAC,YAAY,EAAE;AACjB,gBAAA,IAAI,CAAC,gBAAgB,GAAG,CAAC;;;;AAKvB,IAAA,UAAU,CAAC,KAA6B,EAAA;QAC9C,IAAI,YAAY,GAAG,KAAK;AACxB,QAAA,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE;AAC1B,YAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBACjB;;YAEF,IAAI,CAAC,gBAAgB,EAAE;YACvB,YAAY,GAAG,IAAI;;YAGnB,MAAM,CAAC,GAAG,EAAE;;AAEd,QAAA,OAAO,YAAY;;AAEtB;;;;"}