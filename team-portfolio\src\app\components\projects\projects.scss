@import '../../../styles/variables';
@import '../../../styles/mixins';

.projects-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #064e3b 100%);
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .container {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
}

// Section Header
.section-header {
  text-align: center;
  margin-bottom: 4rem;

  .section-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(45deg, white, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }

  .section-description {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    opacity: 0.9;
    line-height: 1.6;

    @media (max-width: 768px) {
      font-size: 1rem;
      padding: 0 1rem;
    }
  }

  // Subsection titles
  .subsection-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
  }

  // Featured Projects
  .featured-projects {
    margin-bottom: 4rem;
  }

  .featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .featured-project-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);

    &:hover {
      transform: translateY(-10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }
  }

  .project-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
  }

  .project-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  .project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .featured-project-card:hover & {
      opacity: 1;
    }
  }

  .project-actions {
    display: flex;
    gap: 1rem;
  }

  .action-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &.demo-btn {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
      }
    }

    &.github-btn {
      background: linear-gradient(135deg, #374151, #1f2937);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(55, 65, 81, 0.3);
      }
    }
  }

  .project-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    font-weight: 600;

    &.completed {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    &.in-progress {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
    }

    &.planned {
      background: linear-gradient(135deg, #6366f1, #4f46e5);
      color: white;
    }
  }

  .project-content {
    padding: 1.5rem;
  }

  .project-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .project-category {
    color: #10b981;
    font-weight: 600;
  }

  .team-member {
    color: rgba(255, 255, 255, 0.7);
  }

  .project-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
  }

  .project-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }

  .project-technologies {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .tech-tag {
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    font-size: 0.8rem;
    color: white;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }
  }

  // Project Filters
  .project-filters {
    margin-bottom: 3rem;
  }

  .filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .filter-btn {
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 2rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    &.active {
      background: linear-gradient(135deg, #10b981, #059669);
      border-color: #10b981;
      box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
    }
  }

  // Projects Grid
  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
  }

  .project-card {
    height: 400px;
    perspective: 1000px;
    cursor: pointer;
  }

  .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s;
    transform-style: preserve-3d;

    .project-card:hover & {
      transform: rotateY(180deg);
    }
  }

  .card-front,
  .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 1rem;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .card-back {
    transform: rotateY(180deg);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .project-image-small {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
  }

  .project-status-small {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.7rem;
    font-weight: 600;

    &.completed {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
    }

    &.in-progress {
      background: linear-gradient(135deg, #f59e0b, #d97706);
      color: white;
    }
  }

  .project-info {
    padding: 1rem;
    height: 200px;
    display: flex;
    flex-direction: column;
  }

  .project-meta-small {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
  }

  .category-tag {
    color: #10b981;
    font-weight: 600;
  }

  .member-tag {
    color: rgba(255, 255, 255, 0.7);
  }

  .project-title-small {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
  }

  .project-desc-small {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    flex-grow: 1;
  }

  .tech-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    justify-content: center;
  }

  .tech-chip {
    padding: 0.2rem 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    font-size: 0.7rem;
    color: white;
  }

  .tech-more {
    padding: 0.2rem 0.5rem;
    background: rgba(16, 185, 129, 0.3);
    border-radius: 0.5rem;
    font-size: 0.7rem;
    color: #10b981;
    font-weight: 600;
  }

  // Card Back Styles
  .detailed-info {
    text-align: left;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .project-title-back {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
  }

  .project-long-desc {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.5;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
  }

  .all-technologies {
    margin-bottom: 1.5rem;

    h6 {
      color: white;
      font-weight: 600;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }
  }

  .tech-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .tech-tag-back {
    padding: 0.2rem 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    font-size: 0.7rem;
    color: white;
  }

  .project-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .link-btn {
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &.demo {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
      }
    }

    &.github {
      background: linear-gradient(135deg, #374151, #1f2937);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(55, 65, 81, 0.3);
      }
    }
  }

  // Call to Action
  .projects-cta {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: white;
    }

    p {
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 2rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  .cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    text-decoration: none;
    border-radius: 3rem;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 40px rgba(16, 185, 129, 0.4);
    }

    i {
      font-size: 1.2rem;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .featured-grid,
    .projects-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .filter-buttons {
      justify-content: center;
    }

    .filter-btn {
      padding: 0.5rem 1rem;
      font-size: 0.9rem;
    }

    .project-card {
      height: 350px;
    }

    .projects-cta {
      padding: 2rem 1rem;

      h3 {
        font-size: 1.5rem;
      }

      p {
        font-size: 1rem;
      }
    }
  }
}

// Featured Projects
.featured-projects {
  margin-bottom: $spacing-4xl;

  .subsection-title {
    font-size: 2.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: $spacing-3xl;
    @include text-gradient($text-gradient-accent);

    @include mobile {
      font-size: 2rem;
    }
  }
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: $spacing-3xl;
  margin-bottom: $spacing-4xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacing-xl;
  }
}

.featured-project-card {
  @include card-base;
  background: $gradient-card;
  border-radius: $radius-2xl;
  overflow: hidden;
  @include transition(all, $transition-normal);
  @include animate-on-scroll(scaleIn, 0.8s);

  &.animate-delay-1 {
    animation-delay: 0.2s;
  }

  &.animate-delay-2 {
    animation-delay: 0.4s;
  }

  @include card-hover;

  .project-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;

    .project-image {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      @include transition(transform, $transition-slow);
    }

    .project-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba($primary-dark, 0.8) 0%, rgba($secondary-dark, 0.8) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      @include transition(opacity, $transition-normal);
    }

    .project-actions {
      display: flex;
      gap: $spacing-lg;
      flex-wrap: wrap;
      justify-content: center;
    }

    .action-btn {
      @include button-primary;
      padding: $spacing-md $spacing-lg;
      font-size: 0.9rem;
      text-decoration: none;

      &.demo-btn {
        background: $gradient-secondary;
      }

      &.github-btn {
        background: $gradient-accent;
      }

      i {
        margin-right: $spacing-xs;
      }
    }

    .project-status {
      position: absolute;
      top: $spacing-md;
      right: $spacing-md;
      padding: $spacing-xs $spacing-md;
      border-radius: $radius-full;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.completed {
        background: rgba($secondary-light, 0.9);
        color: $neutral-white;
      }

      &.in-progress {
        background: rgba($accent-amber, 0.9);
        color: $primary-dark;
      }

      &.planned {
        background: rgba($neutral-gray-400, 0.9);
        color: $neutral-white;
      }
    }

    &:hover {
      .project-image {
        transform: scale(1.1);
      }

      .project-overlay {
        opacity: 1;
      }
    }
  }

  .project-content {
    padding: $spacing-xl;

    .project-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-md;

      .project-category {
        background: rgba($secondary-light, 0.2);
        color: $secondary-light;
        padding: $spacing-xs $spacing-md;
        border-radius: $radius-full;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .team-member {
        background: rgba($accent-amber, 0.2);
        color: $accent-amber;
        padding: $spacing-xs $spacing-md;
        border-radius: $radius-full;
        font-size: 0.8rem;
        font-weight: 600;
      }
    }

    .project-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: $spacing-md;
      color: $neutral-white;
      line-height: 1.3;
    }

    .project-description {
      font-size: 1rem;
      line-height: 1.6;
      margin-bottom: $spacing-lg;
      opacity: 0.9;
    }

    .project-technologies {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-sm;

      .tech-tag {
        @include skill-tag;
        background: rgba($neutral-white, 0.1);
        color: $neutral-white;
        font-size: 0.75rem;
        padding: $spacing-xs $spacing-sm;

        &:hover {
          background: rgba($neutral-white, 0.2);
          transform: translateY(-1px);
        }
      }
    }
  }
}

// Project Filters
.project-filters {
  margin-bottom: $spacing-3xl;
  text-align: center;

  .subsection-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: $spacing-xl;
    @include text-gradient($text-gradient-accent);

    @include mobile {
      font-size: 2rem;
    }
  }

  .filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: $spacing-md;

    @include mobile {
      gap: $spacing-sm;
    }
  }

  .filter-btn {
    @include button-secondary;
    padding: $spacing-md $spacing-lg;
    font-size: 0.9rem;
    border: 2px solid rgba($neutral-white, 0.3);
    color: $neutral-white;
    background: transparent;
    @include transition(all, $transition-normal);

    &:hover {
      background: rgba($neutral-white, 0.1);
      border-color: $secondary-light;
      transform: translateY(-2px);
    }

    &.active {
      background: $gradient-secondary;
      border-color: $secondary-light;
      color: $neutral-white;
      box-shadow: $shadow-glow;
    }

    @include mobile {
      padding: $spacing-sm $spacing-md;
      font-size: 0.8rem;
    }
  }
}

// Projects Grid
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: $spacing-xl;
  margin-bottom: $spacing-4xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacing-lg;
  }
}

.project-card {
  height: 400px;
  perspective: 1000px;
  @include animate-on-scroll(fadeInUp, 0.8s);

  &.animate-delay-1 {
    animation-delay: 0.1s;
  }

  &.animate-delay-2 {
    animation-delay: 0.2s;
  }

  .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    @include transition(transform, 0.6s);
    transform-style: preserve-3d;
    border-radius: $radius-2xl;
    cursor: pointer;

    &:hover {
      transform: rotateY(180deg);
    }
  }

  .card-front,
  .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: $radius-2xl;
    @include card-base;
    background: $gradient-card;
    border: 1px solid rgba($neutral-white, 0.1);
    overflow: hidden;
  }

  .card-back {
    transform: rotateY(180deg);
    background: linear-gradient(135deg, rgba($secondary-dark, 0.9) 0%, rgba($primary-dark, 0.9) 100%);
  }

  // Card Front Content
  .project-image-small {
    height: 180px;
    background-size: cover;
    background-position: center;
    position: relative;

    .project-status-small {
      position: absolute;
      top: $spacing-sm;
      right: $spacing-sm;
      padding: $spacing-xs $spacing-sm;
      border-radius: $radius-full;
      font-size: 0.7rem;
      font-weight: 600;
      text-transform: uppercase;

      &.completed {
        background: rgba($secondary-light, 0.9);
        color: $neutral-white;
      }

      &.in-progress {
        background: rgba($accent-amber, 0.9);
        color: $primary-dark;
      }

      &.planned {
        background: rgba($neutral-gray-400, 0.9);
        color: $neutral-white;
      }
    }
  }

  .project-info {
    padding: $spacing-lg;
    height: calc(100% - 180px);
    display: flex;
    flex-direction: column;

    .project-meta-small {
      display: flex;
      justify-content: space-between;
      margin-bottom: $spacing-sm;

      .category-tag {
        background: rgba($secondary-light, 0.2);
        color: $secondary-light;
        padding: $spacing-xs $spacing-sm;
        border-radius: $radius-full;
        font-size: 0.7rem;
        font-weight: 600;
      }

      .member-tag {
        background: rgba($accent-amber, 0.2);
        color: $accent-amber;
        padding: $spacing-xs $spacing-sm;
        border-radius: $radius-full;
        font-size: 0.7rem;
        font-weight: 600;
      }
    }

    .project-title-small {
      font-size: 1.1rem;
      font-weight: 700;
      margin-bottom: $spacing-sm;
      color: $neutral-white;
      line-height: 1.3;
    }

    .project-desc-small {
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: $spacing-md;
      opacity: 0.9;
      flex-grow: 1;
    }

    .tech-preview {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
      align-items: center;

      .tech-chip {
        background: rgba($neutral-white, 0.1);
        color: $neutral-white;
        padding: $spacing-xs $spacing-sm;
        border-radius: $radius-md;
        font-size: 0.7rem;
        font-weight: 500;
      }

      .tech-more {
        color: $secondary-light;
        font-size: 0.8rem;
        font-weight: 600;
      }
    }
  }

  // Card Back Content
  .detailed-info {
    padding: $spacing-lg;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .project-title-back {
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: $spacing-md;
      color: $neutral-white;
      @include text-gradient($text-gradient-accent);
    }

    .project-long-desc {
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: $spacing-lg;
      opacity: 0.9;
      flex-grow: 1;
    }

    .all-technologies {
      margin-bottom: $spacing-lg;

      h6 {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: $spacing-sm;
        color: $secondary-light;
      }

      .tech-list {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-xs;

        .tech-tag-back {
          background: rgba($secondary-light, 0.2);
          color: $secondary-light;
          padding: $spacing-xs $spacing-sm;
          border-radius: $radius-md;
          font-size: 0.7rem;
          font-weight: 500;
          border: 1px solid rgba($secondary-light, 0.3);
        }
      }
    }

    .project-links {
      display: flex;
      gap: $spacing-md;
      justify-content: center;

      .link-btn {
        @include button-primary;
        padding: $spacing-sm $spacing-md;
        font-size: 0.8rem;
        text-decoration: none;
        flex: 1;
        text-align: center;

        &.demo {
          background: $gradient-secondary;
        }

        &.github {
          background: $gradient-accent;
        }

        i {
          margin-right: $spacing-xs;
        }
      }
    }
  }
}

// Call to Action
.projects-cta {
  text-align: center;
  padding: $spacing-3xl;
  background: $gradient-card;
  border-radius: $radius-2xl;
  border: 1px solid rgba($neutral-white, 0.1);
  @include animate-on-scroll(fadeInUp, 0.8s);

  h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: $spacing-md;
    @include text-gradient($text-gradient-primary);

    @include mobile {
      font-size: 1.5rem;
    }
  }

  p {
    font-size: 1.1rem;
    margin-bottom: $spacing-xl;
    opacity: 0.9;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;

    @include mobile {
      font-size: 1rem;
    }
  }

  .cta-button {
    @include button-primary;
    background: $gradient-hero;
    padding: $spacing-lg $spacing-2xl;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: $spacing-sm;
    @include hover-lift;

    &:hover {
      box-shadow: $shadow-glow;
      transform: translateY(-3px) scale(1.02);
    }

    i {
      font-size: 1.2rem;
    }

    @include mobile {
      padding: $spacing-md $spacing-xl;
      font-size: 1rem;
    }
  }
}