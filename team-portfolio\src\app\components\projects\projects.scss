@import '../../../styles/variables';
@import '../../../styles/mixins';

.projects-section {
  padding: $spacing-4xl 0;
  background: linear-gradient(135deg, $primary-medium 0%, $primary-light 50%, $secondary-dark 100%);
  color: $neutral-white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }

  .container {
    position: relative;
    z-index: 1;
  }
}

// Section Header
.section-header {
  text-align: center;
  margin-bottom: $spacing-4xl;

  .section-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: $spacing-lg;
    @include text-gradient($text-gradient-primary);
    @include animate-on-scroll(fadeInDown, 0.8s);

    @include mobile {
      font-size: 2.5rem;
    }
  }

  .section-description {
    font-size: 1.2rem;
    max-width: 700px;
    margin: 0 auto;
    opacity: 0.9;
    line-height: 1.6;
    @include animate-on-scroll(fadeInUp, 0.8s, 0.2s);

    @include mobile {
      font-size: 1rem;
      padding: 0 $spacing-md;
    }
  }
}

// Featured Projects
.featured-projects {
  margin-bottom: $spacing-4xl;

  .subsection-title {
    font-size: 2.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: $spacing-3xl;
    @include text-gradient($text-gradient-accent);

    @include mobile {
      font-size: 2rem;
    }
  }
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: $spacing-3xl;
  margin-bottom: $spacing-4xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacing-xl;
  }
}

.featured-project-card {
  @include card-base;
  background: $gradient-card;
  border-radius: $radius-2xl;
  overflow: hidden;
  @include transition(all, $transition-normal);
  @include animate-on-scroll(scaleIn, 0.8s);

  &.animate-delay-1 {
    animation-delay: 0.2s;
  }

  &.animate-delay-2 {
    animation-delay: 0.4s;
  }

  @include card-hover;

  .project-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;

    .project-image {
      width: 100%;
      height: 100%;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      @include transition(transform, $transition-slow);
    }

    .project-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba($primary-dark, 0.8) 0%, rgba($secondary-dark, 0.8) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      @include transition(opacity, $transition-normal);
    }

    .project-actions {
      display: flex;
      gap: $spacing-lg;
      flex-wrap: wrap;
      justify-content: center;
    }

    .action-btn {
      @include button-primary;
      padding: $spacing-md $spacing-lg;
      font-size: 0.9rem;
      text-decoration: none;

      &.demo-btn {
        background: $gradient-secondary;
      }

      &.github-btn {
        background: $gradient-accent;
      }

      i {
        margin-right: $spacing-xs;
      }
    }

    .project-status {
      position: absolute;
      top: $spacing-md;
      right: $spacing-md;
      padding: $spacing-xs $spacing-md;
      border-radius: $radius-full;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.completed {
        background: rgba($secondary-light, 0.9);
        color: $neutral-white;
      }

      &.in-progress {
        background: rgba($accent-amber, 0.9);
        color: $primary-dark;
      }

      &.planned {
        background: rgba($neutral-gray-400, 0.9);
        color: $neutral-white;
      }
    }

    &:hover {
      .project-image {
        transform: scale(1.1);
      }

      .project-overlay {
        opacity: 1;
      }
    }
  }

  .project-content {
    padding: $spacing-xl;

    .project-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $spacing-md;

      .project-category {
        background: rgba($secondary-light, 0.2);
        color: $secondary-light;
        padding: $spacing-xs $spacing-md;
        border-radius: $radius-full;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .team-member {
        background: rgba($accent-amber, 0.2);
        color: $accent-amber;
        padding: $spacing-xs $spacing-md;
        border-radius: $radius-full;
        font-size: 0.8rem;
        font-weight: 600;
      }
    }

    .project-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: $spacing-md;
      color: $neutral-white;
      line-height: 1.3;
    }

    .project-description {
      font-size: 1rem;
      line-height: 1.6;
      margin-bottom: $spacing-lg;
      opacity: 0.9;
    }

    .project-technologies {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-sm;

      .tech-tag {
        @include skill-tag;
        background: rgba($neutral-white, 0.1);
        color: $neutral-white;
        font-size: 0.75rem;
        padding: $spacing-xs $spacing-sm;

        &:hover {
          background: rgba($neutral-white, 0.2);
          transform: translateY(-1px);
        }
      }
    }
  }
}

// Project Filters
.project-filters {
  margin-bottom: $spacing-3xl;
  text-align: center;

  .subsection-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: $spacing-xl;
    @include text-gradient($text-gradient-accent);

    @include mobile {
      font-size: 2rem;
    }
  }

  .filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: $spacing-md;

    @include mobile {
      gap: $spacing-sm;
    }
  }

  .filter-btn {
    @include button-secondary;
    padding: $spacing-md $spacing-lg;
    font-size: 0.9rem;
    border: 2px solid rgba($neutral-white, 0.3);
    color: $neutral-white;
    background: transparent;
    @include transition(all, $transition-normal);

    &:hover {
      background: rgba($neutral-white, 0.1);
      border-color: $secondary-light;
      transform: translateY(-2px);
    }

    &.active {
      background: $gradient-secondary;
      border-color: $secondary-light;
      color: $neutral-white;
      box-shadow: $shadow-glow;
    }

    @include mobile {
      padding: $spacing-sm $spacing-md;
      font-size: 0.8rem;
    }
  }
}

// Projects Grid
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: $spacing-xl;
  margin-bottom: $spacing-4xl;

  @include mobile {
    grid-template-columns: 1fr;
    gap: $spacing-lg;
  }
}

.project-card {
  height: 400px;
  perspective: 1000px;
  @include animate-on-scroll(fadeInUp, 0.8s);

  &.animate-delay-1 {
    animation-delay: 0.1s;
  }

  &.animate-delay-2 {
    animation-delay: 0.2s;
  }

  .card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    @include transition(transform, 0.6s);
    transform-style: preserve-3d;
    border-radius: $radius-2xl;
    cursor: pointer;

    &:hover {
      transform: rotateY(180deg);
    }
  }

  .card-front,
  .card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: $radius-2xl;
    @include card-base;
    background: $gradient-card;
    border: 1px solid rgba($neutral-white, 0.1);
    overflow: hidden;
  }

  .card-back {
    transform: rotateY(180deg);
    background: linear-gradient(135deg, rgba($secondary-dark, 0.9) 0%, rgba($primary-dark, 0.9) 100%);
  }

  // Card Front Content
  .project-image-small {
    height: 180px;
    background-size: cover;
    background-position: center;
    position: relative;

    .project-status-small {
      position: absolute;
      top: $spacing-sm;
      right: $spacing-sm;
      padding: $spacing-xs $spacing-sm;
      border-radius: $radius-full;
      font-size: 0.7rem;
      font-weight: 600;
      text-transform: uppercase;

      &.completed {
        background: rgba($secondary-light, 0.9);
        color: $neutral-white;
      }

      &.in-progress {
        background: rgba($accent-amber, 0.9);
        color: $primary-dark;
      }

      &.planned {
        background: rgba($neutral-gray-400, 0.9);
        color: $neutral-white;
      }
    }
  }

  .project-info {
    padding: $spacing-lg;
    height: calc(100% - 180px);
    display: flex;
    flex-direction: column;

    .project-meta-small {
      display: flex;
      justify-content: space-between;
      margin-bottom: $spacing-sm;

      .category-tag {
        background: rgba($secondary-light, 0.2);
        color: $secondary-light;
        padding: $spacing-xs $spacing-sm;
        border-radius: $radius-full;
        font-size: 0.7rem;
        font-weight: 600;
      }

      .member-tag {
        background: rgba($accent-amber, 0.2);
        color: $accent-amber;
        padding: $spacing-xs $spacing-sm;
        border-radius: $radius-full;
        font-size: 0.7rem;
        font-weight: 600;
      }
    }

    .project-title-small {
      font-size: 1.1rem;
      font-weight: 700;
      margin-bottom: $spacing-sm;
      color: $neutral-white;
      line-height: 1.3;
    }

    .project-desc-small {
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: $spacing-md;
      opacity: 0.9;
      flex-grow: 1;
    }

    .tech-preview {
      display: flex;
      flex-wrap: wrap;
      gap: $spacing-xs;
      align-items: center;

      .tech-chip {
        background: rgba($neutral-white, 0.1);
        color: $neutral-white;
        padding: $spacing-xs $spacing-sm;
        border-radius: $radius-md;
        font-size: 0.7rem;
        font-weight: 500;
      }

      .tech-more {
        color: $secondary-light;
        font-size: 0.8rem;
        font-weight: 600;
      }
    }
  }

  // Card Back Content
  .detailed-info {
    padding: $spacing-lg;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .project-title-back {
      font-size: 1.3rem;
      font-weight: 700;
      margin-bottom: $spacing-md;
      color: $neutral-white;
      @include text-gradient($text-gradient-accent);
    }

    .project-long-desc {
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: $spacing-lg;
      opacity: 0.9;
      flex-grow: 1;
    }

    .all-technologies {
      margin-bottom: $spacing-lg;

      h6 {
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: $spacing-sm;
        color: $secondary-light;
      }

      .tech-list {
        display: flex;
        flex-wrap: wrap;
        gap: $spacing-xs;

        .tech-tag-back {
          background: rgba($secondary-light, 0.2);
          color: $secondary-light;
          padding: $spacing-xs $spacing-sm;
          border-radius: $radius-md;
          font-size: 0.7rem;
          font-weight: 500;
          border: 1px solid rgba($secondary-light, 0.3);
        }
      }
    }

    .project-links {
      display: flex;
      gap: $spacing-md;
      justify-content: center;

      .link-btn {
        @include button-primary;
        padding: $spacing-sm $spacing-md;
        font-size: 0.8rem;
        text-decoration: none;
        flex: 1;
        text-align: center;

        &.demo {
          background: $gradient-secondary;
        }

        &.github {
          background: $gradient-accent;
        }

        i {
          margin-right: $spacing-xs;
        }
      }
    }
  }
}

// Call to Action
.projects-cta {
  text-align: center;
  padding: $spacing-3xl;
  background: $gradient-card;
  border-radius: $radius-2xl;
  border: 1px solid rgba($neutral-white, 0.1);
  @include animate-on-scroll(fadeInUp, 0.8s);

  h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: $spacing-md;
    @include text-gradient($text-gradient-primary);

    @include mobile {
      font-size: 1.5rem;
    }
  }

  p {
    font-size: 1.1rem;
    margin-bottom: $spacing-xl;
    opacity: 0.9;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;

    @include mobile {
      font-size: 1rem;
    }
  }

  .cta-button {
    @include button-primary;
    background: $gradient-hero;
    padding: $spacing-lg $spacing-2xl;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: $spacing-sm;
    @include hover-lift;

    &:hover {
      box-shadow: $shadow-glow;
      transform: translateY(-3px) scale(1.02);
    }

    i {
      font-size: 1.2rem;
    }

    @include mobile {
      padding: $spacing-md $spacing-xl;
      font-size: 1rem;
    }
  }
}