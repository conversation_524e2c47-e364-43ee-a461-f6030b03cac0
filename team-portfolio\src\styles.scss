/* Global styles for Team Portfolio */

// Import variables and mixins
@import 'styles/variables';
@import 'styles/mixins';

// Global reset and base styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: $neutral-gray-800;
  background: $primary-dark;
  overflow-x: hidden;
}

// Typography
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: $spacing-md;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: $spacing-md;
}

a {
  color: $secondary-light;
  text-decoration: none;
  transition: color $transition-normal;

  &:hover {
    color: $accent-amber;
  }
}

// Utility classes
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @include mobile {
    padding: 0 $spacing-md;
  }
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.fade-in-up {
  @include animate-on-scroll(fadeInUp);
}

.fade-in-down {
  @include animate-on-scroll(fadeInDown);
}

.fade-in-left {
  @include animate-on-scroll(fadeInLeft);
}

.fade-in-right {
  @include animate-on-scroll(fadeInRight);
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: $primary-medium;
}

::-webkit-scrollbar-thumb {
  background: $secondary-light;
  border-radius: $radius-full;
}

::-webkit-scrollbar-thumb:hover {
  background: $accent-amber;
}

// Scroll-triggered animations
.animate-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;

  &.animate {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-on-scroll-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.8s ease-out;

  &.animate {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-on-scroll-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.8s ease-out;

  &.animate {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-on-scroll-scale {
  opacity: 0;
  transform: scale(0.9);
  transition: all 0.8s ease-out;

  &.animate {
    opacity: 1;
    transform: scale(1);
  }
}

// Professional loading animation
.loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba($neutral-white, 0.1) 0%,
    rgba($neutral-white, 0.3) 50%,
    rgba($neutral-white, 0.1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

// Floating animation for decorative elements
.float {
  animation: float 3s ease-in-out infinite;
}

.float-delayed {
  animation: float 3s ease-in-out infinite 1.5s;
}

// Pulse animation for call-to-action elements
.pulse {
  animation: pulse 2s ease-in-out infinite;
}

// Glow effect for interactive elements
.glow {
  animation: glow 2s ease-in-out infinite alternate;
}
