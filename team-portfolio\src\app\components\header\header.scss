@import '../../../styles/variables';
@import '../../../styles/mixins';

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: $z-fixed;
  @include glassmorphism(0.05, 20px, 0.1);
  background: rgba($primary-dark, 0.95);
  box-shadow: $shadow-lg;
  @include transition(all, $transition-normal);

  &.scrolled {
    background: rgba($primary-dark, 0.98);
    box-shadow: $shadow-xl;
  }
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-lg $spacing-xl;
  max-width: 1200px;
  margin: 0 auto;

  @include mobile {
    padding: $spacing-md;
  }
}

.nav-brand {
  h1 {
    font-size: 1.8rem;
    font-weight: 700;
    @include text-gradient($text-gradient-accent);
    margin: 0;
    @include transition(transform, $transition-normal);

    &:hover {
      transform: scale(1.05);
    }
  }
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
  margin: 0;
  padding: 0;

  @media (max-width: 768px) {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    transition: left 0.3s ease;

    &.active {
      left: 0;
    }
  }
}

.nav-link {
  text-decoration: none;
  color: $neutral-white;
  font-weight: 500;
  font-size: 1rem;
  padding: $spacing-sm $spacing-lg;
  border-radius: $radius-full;
  @include transition(all, $transition-normal);
  position: relative;
  opacity: 0.8;

  &:hover {
    background: $gradient-secondary;
    color: $neutral-white;
    opacity: 1;
    transform: translateY(-2px);
    box-shadow: $shadow-glow;
  }

  @include mobile {
    font-size: 1.2rem;
    padding: $spacing-lg $spacing-xl;
    margin: $spacing-sm 0;
  }
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;

  span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
  }

  &.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
  }

  &.active span:nth-child(2) {
    opacity: 0;
  }

  &.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
  }

  @media (max-width: 768px) {
    display: flex;
  }
}