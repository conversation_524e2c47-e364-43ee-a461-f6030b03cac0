import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './components/header/header';
import { HeroComponent } from './components/hero/hero';
import { TeamMemberComponent } from './components/team-member/team-member';
import { Projects } from './components/projects/projects';
import { ContactComponent } from './components/contact/contact';
import { FooterComponent } from './components/footer/footer';
import { TeamService, TeamMember } from './services/team';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
    CommonModule,
    HeaderComponent,
    HeroComponent,
    TeamMemberComponent,
    Projects,
    ContactComponent,
    FooterComponent
  ],
  templateUrl: './app.html',
  styleUrl: './app.scss'
})
export class AppComponent {
  title = 'team-portfolio';
  teamMembers: TeamMember[] = [];

  constructor(private teamService: TeamService) {
    this.teamMembers = this.teamService.getTeamMembers();
  }
}
