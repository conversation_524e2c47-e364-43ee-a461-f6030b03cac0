// ===== MODERN PROFESSIONAL COLOR PALETTE =====

// Primary Colors - Deep Ocean Blues
$primary-dark: #0f172a;      // Slate 900
$primary-medium: #1e293b;    // Slate 800
$primary-light: #334155;     // Slate 700

// Secondary Colors - Emerald & Teal
$secondary-dark: #064e3b;    // Emerald 900
$secondary-medium: #059669;  // Emerald 600
$secondary-light: #10b981;   // Emerald 500

// Accent Colors - Warm & Vibrant
$accent-orange: #ea580c;     // Orange 600
$accent-amber: #f59e0b;      // Amber 500
$accent-rose: #e11d48;       // Rose 600
$accent-purple: #7c3aed;     // Violet 600

// Neutral Colors
$neutral-white: #ffffff;
$neutral-gray-50: #f8fafc;
$neutral-gray-100: #f1f5f9;
$neutral-gray-200: #e2e8f0;
$neutral-gray-300: #cbd5e1;
$neutral-gray-400: #94a3b8;
$neutral-gray-500: #64748b;
$neutral-gray-600: #475569;
$neutral-gray-700: #334155;
$neutral-gray-800: #1e293b;
$neutral-gray-900: #0f172a;

// Gradient Definitions
$gradient-primary: linear-gradient(135deg, $primary-medium 0%, $secondary-medium 100%);
$gradient-secondary: linear-gradient(135deg, $secondary-light 0%, $accent-amber 100%);
$gradient-accent: linear-gradient(135deg, $accent-orange 0%, $accent-rose 100%);
$gradient-hero: linear-gradient(135deg, $primary-dark 0%, $primary-medium 50%, $secondary-dark 100%);
$gradient-card: linear-gradient(135deg, rgba($neutral-white, 0.1) 0%, rgba($neutral-white, 0.05) 100%);

// Text Gradients
$text-gradient-primary: linear-gradient(45deg, $neutral-white, $neutral-gray-100);
$text-gradient-accent: linear-gradient(45deg, $secondary-light, $accent-amber);
$text-gradient-warm: linear-gradient(45deg, $accent-orange, $accent-rose);

// Shadow Definitions
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
$shadow-glow: 0 0 20px rgba($secondary-light, 0.3);
$shadow-glow-warm: 0 0 30px rgba($accent-orange, 0.4);

// Animation & Transition Variables
$transition-fast: 0.15s ease-in-out;
$transition-normal: 0.3s ease-in-out;
$transition-slow: 0.5s ease-in-out;
$transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

// Border Radius
$radius-sm: 0.375rem;   // 6px
$radius-md: 0.5rem;     // 8px
$radius-lg: 0.75rem;    // 12px
$radius-xl: 1rem;       // 16px
$radius-2xl: 1.5rem;    // 24px
$radius-full: 9999px;

// Spacing
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-2xl: 3rem;     // 48px
$spacing-3xl: 4rem;     // 64px
$spacing-4xl: 5rem;     // 80px

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index layers
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;
