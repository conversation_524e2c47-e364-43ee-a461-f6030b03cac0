<section class="projects-section">
  <div class="container">
    <!-- Section Header -->
    <div class="section-header animate-on-scroll">
      <h2 class="section-title">Our Projects</h2>
      <p class="section-description">
        Explore our portfolio of innovative solutions, from AI-powered applications to blockchain systems.
        Each project showcases our expertise in cutting-edge technologies and problem-solving capabilities.
      </p>
    </div>

    <!-- Featured Projects -->
    <div class="featured-projects animate-on-scroll">
      <h3 class="subsection-title">Featured Projects</h3>
      <div class="featured-grid">
        <div
          *ngFor="let project of getFeaturedProjects(); let i = index"
          class="featured-project-card"
          [class.animate-delay-1]="i === 1"
          [class.animate-delay-2]="i === 2">

          <div class="project-image-container">
            <div class="project-image" [style.background-image]="'url(' + project.image + ')'">
              <div class="project-overlay">
                <div class="project-actions">
                  <a *ngIf="project.demoUrl" [href]="project.demoUrl" target="_blank" class="action-btn demo-btn">
                    <i class="fas fa-external-link-alt"></i>
                    Live Demo
                  </a>
                  <a *ngIf="project.githubUrl" [href]="project.githubUrl" target="_blank" class="action-btn github-btn">
                    <i class="fab fa-github"></i>
                    Source Code
                  </a>
                </div>
              </div>
            </div>
            <div class="project-status" [class]="project.status.toLowerCase().replace(' ', '-')">
              {{ project.status }}
            </div>
          </div>

          <div class="project-content">
            <div class="project-meta">
              <span class="project-category">{{ project.category }}</span>
              <span class="team-member">{{ project.teamMember }}</span>
            </div>

            <h4 class="project-title">{{ project.title }}</h4>
            <p class="project-description">{{ project.description }}</p>

            <div class="project-technologies">
              <span *ngFor="let tech of project.technologies" class="tech-tag">
                {{ tech }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Project Filters -->
    <div class="project-filters animate-on-scroll">
      <h3 class="subsection-title">All Projects</h3>
      <div class="filter-buttons">
        <button
          *ngFor="let category of categories"
          (click)="filterProjects(category)"
          [class.active]="selectedCategory === category"
          class="filter-btn">
          {{ category }}
        </button>
      </div>
    </div>

    <!-- All Projects Grid -->
    <div class="projects-grid animate-on-scroll">
      <div
        *ngFor="let project of filteredProjects; let i = index"
        class="project-card"
        [class.animate-delay-1]="i % 3 === 1"
        [class.animate-delay-2]="i % 3 === 2">

        <div class="card-inner">
          <div class="card-front">
            <div class="project-image-small" [style.background-image]="'url(' + project.image + ')'">
              <div class="project-status-small" [class]="project.status.toLowerCase().replace(' ', '-')">
                {{ project.status }}
              </div>
            </div>

            <div class="project-info">
              <div class="project-meta-small">
                <span class="category-tag">{{ project.category }}</span>
                <span class="member-tag">{{ project.teamMember }}</span>
              </div>

              <h5 class="project-title-small">{{ project.title }}</h5>
              <p class="project-desc-small">{{ project.description }}</p>

              <div class="tech-preview">
                <span *ngFor="let tech of project.technologies.slice(0, 3)" class="tech-chip">
                  {{ tech }}
                </span>
                <span *ngIf="project.technologies.length > 3" class="tech-more">
                  +{{ project.technologies.length - 3 }}
                </span>
              </div>
            </div>
          </div>

          <div class="card-back">
            <div class="detailed-info">
              <h5 class="project-title-back">{{ project.title }}</h5>
              <p class="project-long-desc">{{ project.longDescription }}</p>

              <div class="all-technologies">
                <h6>Technologies Used:</h6>
                <div class="tech-list">
                  <span *ngFor="let tech of project.technologies" class="tech-tag-back">
                    {{ tech }}
                  </span>
                </div>
              </div>

              <div class="project-links">
                <a *ngIf="project.demoUrl" [href]="project.demoUrl" target="_blank" class="link-btn demo">
                  <i class="fas fa-play"></i> Demo
                </a>
                <a *ngIf="project.githubUrl" [href]="project.githubUrl" target="_blank" class="link-btn github">
                  <i class="fab fa-github"></i> Code
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="projects-cta animate-on-scroll">
      <h3>Interested in Our Work?</h3>
      <p>Let's discuss how we can bring your ideas to life with innovative technology solutions.</p>
      <a href="#contact" class="cta-button">
        <i class="fas fa-rocket"></i>
        Start Your Project
      </a>
    </div>
  </div>
</section>
