@import '../../../styles/variables';
@import '../../../styles/mixins';

.contact-section {
  padding: $spacing-4xl 0;
  background: $gradient-primary;
  color: $neutral-white;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: $spacing-lg;
    @include text-gradient($text-gradient-primary);
    @include animate-on-scroll(fadeInDown, 0.8s);
  }

  .section-description {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 60px;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    margin-bottom: 60px;
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }

  .contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    i {
      font-size: 2rem;
      color: #64b5f6;
      min-width: 40px;
    }

    h3 {
      margin: 0 0 5px 0;
      font-size: 1.2rem;
      font-weight: 600;
    }

    p {
      margin: 0;
      opacity: 0.8;
    }
  }

  .contact-form {
    form {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .form-group {
      input, textarea {
        width: 100%;
        padding: 15px 20px;
        border: none;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;

        &::placeholder {
          color: rgba(255, 255, 255, 0.7);
        }

        &:focus {
          outline: none;
          background: rgba(255, 255, 255, 0.15);
          border-color: #64b5f6;
          box-shadow: 0 0 20px rgba(100, 181, 246, 0.3);
        }
      }

      textarea {
        resize: vertical;
        min-height: 120px;
      }
    }

    .submit-btn {
      @include button-primary;
      background: $gradient-secondary;
      padding: $spacing-lg $spacing-2xl;
      border-radius: $radius-lg;
      font-size: 1.1rem;
      text-transform: uppercase;
      letter-spacing: 1px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: $shadow-glow;
      }
    }
  }

  .social-links {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;

    a {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 15px 25px;
      background: rgba(255, 255, 255, 0.1);
      color: white;
      text-decoration: none;
      border-radius: 50px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;

      i {
        font-size: 1.2rem;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }
    }
  }

  @media (max-width: 768px) {
    padding: 60px 0;

    .section-title {
      font-size: 2rem;
    }

    .section-description {
      font-size: 1rem;
    }

    .contact-content {
      grid-template-columns: 1fr;
      gap: 40px;
    }

    .social-links {
      gap: 15px;

      a {
        padding: 12px 20px;
        font-size: 0.9rem;
      }
    }
  }
}