@import '../../../styles/variables';
@import '../../../styles/mixins';

.team-member {
  display: flex;
  align-items: center;
  gap: $spacing-3xl;
  margin: $spacing-4xl 0;
  @include card-base;
  background: $gradient-secondary;
  border-radius: $radius-2xl;
  box-shadow: $shadow-xl;
  @include transition(all, $transition-normal);
  @include animate-on-scroll(fadeInUp, 0.8s);

  @include card-hover;

  &.reverse {
    flex-direction: row-reverse;
    background: $gradient-accent;
    @include animate-on-scroll(fadeInUp, 0.8s, 0.2s);
  }

  @include mobile {
    flex-direction: column;
    gap: $spacing-xl;
    margin: $spacing-xl 0;
    padding: $spacing-lg;

    &.reverse {
      flex-direction: column;
    }
  }
}

.member-image {
  position: relative;
  flex-shrink: 0;

  img {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid rgba(255, 255, 255, 0.3);
    transition: transform 0.3s ease;

    @media (max-width: 768px) {
      width: 200px;
      height: 200px;
    }
  }

  .social-links {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover .social-links {
    opacity: 1;
  }

  .social-link {
    @include social-link;
    background: rgba($neutral-white, 0.9);
    color: $primary-dark;

    &:hover {
      transform: scale(1.1);
      background: $neutral-white;
    }

    &.linkedin:hover {
      color: #0077b5;
      box-shadow: 0 0 20px rgba(0, 119, 181, 0.3);
    }

    &.github:hover {
      color: $primary-dark;
      box-shadow: 0 0 20px rgba($primary-dark, 0.3);
    }

    &.email:hover {
      color: $accent-rose;
      box-shadow: 0 0 20px rgba($accent-rose, 0.3);
    }

    &.huggingface:hover {
      color: #ff9500;
      box-shadow: 0 0 20px rgba(255, 149, 0, 0.3);
    }
  }
}

.member-info {
  flex: 1;
  color: $neutral-white;

  .member-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 $spacing-sm 0;
    @include text-gradient($text-gradient-primary);
    @include animate-on-scroll(fadeInLeft, 0.8s, 0.3s);

    @include mobile {
      font-size: 2rem;
      text-align: center;
    }
  }

  .member-title {
    font-size: 1.3rem;
    font-weight: 400;
    margin: 0 0 0.5rem 0;
    opacity: 0.9;

    @media (max-width: 768px) {
      font-size: 1.1rem;
      text-align: center;
    }
  }

  .member-experience {
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 1rem 0;
    opacity: 0.8;

    @media (max-width: 768px) {
      text-align: center;
    }
  }

  .member-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0 0 2rem 0;
    opacity: 0.9;

    @media (max-width: 768px) {
      text-align: center;
    }
  }

  .specializations, .skills {
    margin-bottom: 1.5rem;

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
      opacity: 0.9;

      @media (max-width: 768px) {
        text-align: center;
      }
    }
  }

  .spec-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .spec-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  }

  .skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .skill-tag {
    @include skill-tag;
    background: rgba($neutral-white, 0.15);
    color: $neutral-white;
    font-size: 0.8rem;
    font-weight: 400;

    &:hover {
      background: rgba($neutral-white, 0.25);
      transform: translateY(-1px);
      box-shadow: $shadow-sm;
    }
  }
}