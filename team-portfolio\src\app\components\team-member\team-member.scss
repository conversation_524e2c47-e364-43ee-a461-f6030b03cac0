.team-member {
  display: flex;
  align-items: center;
  gap: 3rem;
  margin: 4rem 0;
  padding: 2rem;
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
  }

  &.reverse {
    flex-direction: row-reverse;
    background: linear-gradient(135deg, #f97316 0%, #ec4899 100%);
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
    margin: 2rem 0;
    padding: 1.5rem;

    &.reverse {
      flex-direction: column;
    }
  }
}

.member-image {
  position: relative;
  flex-shrink: 0;

  img {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid rgba(255, 255, 255, 0.3);
    transition: transform 0.3s ease;

    @media (max-width: 768px) {
      width: 200px;
      height: 200px;
    }
  }

  .social-links {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover .social-links {
    opacity: 1;
  }

  .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      background: white;
    }

    &.linkedin:hover {
      color: #0077b5;
    }

    &.github:hover {
      color: #333;
    }

    &.email:hover {
      color: #ea4335;
    }
  }
}

.member-info {
  flex: 1;
  color: white;

  .member-name {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    @media (max-width: 768px) {
      font-size: 2rem;
      text-align: center;
    }
  }

  .member-title {
    font-size: 1.3rem;
    font-weight: 400;
    margin: 0 0 0.5rem 0;
    opacity: 0.9;

    @media (max-width: 768px) {
      font-size: 1.1rem;
      text-align: center;
    }
  }

  .member-experience {
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 1rem 0;
    opacity: 0.8;

    @media (max-width: 768px) {
      text-align: center;
    }
  }

  .member-description {
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0 0 2rem 0;
    opacity: 0.9;

    @media (max-width: 768px) {
      text-align: center;
    }
  }

  .specializations, .skills {
    margin-bottom: 1.5rem;

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
      opacity: 0.9;

      @media (max-width: 768px) {
        text-align: center;
      }
    }
  }

  .spec-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.8rem;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .spec-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  }

  .skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .skill-tag {
    background: rgba(255, 255, 255, 0.15);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 400;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      transform: translateY(-1px);
    }
  }
}