<div class="team-member" [class.reverse]="reverse">
  <div class="member-image">
    <img [src]="member.image" [alt]="member.name" />
    <div class="social-links">
      <a [href]="member.linkedin" target="_blank" class="social-link linkedin">
        <i class="fab fa-linkedin"></i>
      </a>
      <a [href]="member.github" target="_blank" class="social-link github">
        <i class="fab fa-github"></i>
      </a>
      <a *ngIf="member.huggingface" [href]="member.huggingface" target="_blank" class="social-link huggingface">
        <i class="fas fa-robot"></i>
      </a>
      <a [href]="'mailto:' + member.email" class="social-link email">
        <i class="fas fa-envelope"></i>
      </a>
    </div>
  </div>

  <div class="member-info">
    <h2 class="member-name">{{ member.name }}</h2>
    <h3 class="member-title">{{ member.title }}</h3>
    <p class="member-experience">{{ member.experience }}</p>
    <p class="member-description">{{ member.description }}</p>

    <div class="specializations">
      <h4>Specializations</h4>
      <div class="spec-list">
        <span class="spec-item" *ngFor="let spec of member.specializations">
          {{ spec }}
        </span>
      </div>
    </div>

    <div class="skills">
      <h4>Technical Skills</h4>
      <div class="skills-list">
        <span class="skill-tag" *ngFor="let skill of member.skills">
          {{ skill }}
        </span>
      </div>
    </div>
  </div>
</div>
