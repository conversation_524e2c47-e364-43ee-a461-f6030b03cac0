.hero {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 80px 2rem 2rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
  }
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  width: 100%;
  gap: 4rem;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    gap: 2rem;
  }
}

.hero-text {
  flex: 1;
  color: white;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin: 0 0 2rem 0;

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }

  .title-line {
    display: block;
    animation: slideInUp 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);

    &:nth-child(1) {
      animation-delay: 0.2s;
    }

    &:nth-child(2) {
      animation-delay: 0.4s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }

  .gradient-text {
    background: linear-gradient(45deg, #fff, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.hero-description {
  font-size: 1.3rem;
  line-height: 1.6;
  margin: 0 0 3rem 0;
  opacity: 0.9;
  animation: slideInUp 0.8s ease-out 0.8s forwards;
  opacity: 0;
  transform: translateY(30px);

  @media (max-width: 768px) {
    font-size: 1.1rem;
  }
}

.hero-stats {
  display: flex;
  gap: 2rem;
  margin: 0 0 3rem 0;
  animation: slideInUp 0.8s ease-out 1s forwards;
  opacity: 0;
  transform: translateY(30px);

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }

  .stat {
    text-align: center;

    .stat-number {
      display: block;
      font-size: 2.5rem;
      font-weight: 700;
      color: #f093fb;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .stat-label {
      display: block;
      font-size: 0.9rem;
      opacity: 0.8;
      margin-top: 0.5rem;
    }
  }
}

.cta-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: slideInUp 0.8s ease-out 1.2s forwards;
  opacity: 0;
  transform: translateY(30px);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(240, 147, 251, 0.4);
  }

  i {
    transition: transform 0.3s ease;
  }

  &:hover i {
    transform: translateY(3px);
  }
}

.hero-visual {
  flex: 1;
  position: relative;
  height: 500px;

  @media (max-width: 768px) {
    height: 300px;
  }
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;

  .element {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: white;
    animation: float 6s ease-in-out infinite;

    &.element-1 {
      top: 10%;
      left: 20%;
      animation-delay: 0s;
      font-size: 1.2rem;
    }

    &.element-2 {
      top: 30%;
      right: 10%;
      animation-delay: 1s;
      font-size: 1rem;
    }

    &.element-3 {
      top: 60%;
      left: 10%;
      animation-delay: 2s;
      font-size: 1.1rem;
    }

    &.element-4 {
      top: 20%;
      left: 60%;
      animation-delay: 3s;
      font-size: 0.9rem;
    }

    &.element-5 {
      bottom: 20%;
      right: 30%;
      animation-delay: 4s;
      font-size: 1rem;
    }

    &.element-6 {
      bottom: 40%;
      left: 40%;
      animation-delay: 5s;
      font-size: 1.1rem;
    }
  }
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;

  .scroll-arrow {
    width: 2px;
    height: 30px;
    background: white;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -5px;
      width: 12px;
      height: 12px;
      border-right: 2px solid white;
      border-bottom: 2px solid white;
      transform: rotate(45deg);
    }
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}