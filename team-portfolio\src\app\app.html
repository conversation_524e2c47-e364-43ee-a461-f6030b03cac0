<!-- Team Portfolio Application -->
<app-header></app-header>

<main>
    <app-hero></app-hero>

    <section id="team" class="team-section">
        <div class="container">
            <h2 class="section-title">Meet Our Team</h2>
            <p class="section-description">
                Two passionate developers with complementary skills, working together to create exceptional digital experiences.
            </p>

            <div class="team-members">
                <app-team-member
                    *ngFor="let member of teamMembers; let i = index"
                    [member]="member"
                    [reverse]="i % 2 === 0">
                </app-team-member>
            </div>
        </div>
    </section>

    <app-contact></app-contact>
</main>

<app-footer></app-footer>

<router-outlet />

<style>
    .team-section {
        padding: 80px 0;
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: white;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section-title {
        font-size: 3rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #fff, #f0f0f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .section-description {
        font-size: 1.2rem;
        text-align: center;
        margin-bottom: 60px;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .team-members {
        display: flex;
        flex-direction: column;
        gap: 40px;
    }

    @media (max-width: 768px) {
        .team-section {
            padding: 60px 0;
        }

        .section-title {
            font-size: 2rem;
        }

        .section-description {
            font-size: 1rem;
        }

        .team-members {
            gap: 30px;
        }
    }
</style>
